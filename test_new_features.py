#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الميزات الجديدة
Test New Features
"""

import sqlite3
import datetime
import os

def test_national_id_sequence():
    """اختبار تسلسل الرقم الوطني"""
    print("🔢 اختبار تسلسل الرقم الوطني...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # الحصول على آخر رقم وطني
        cursor.execute("SELECT MAX(national_id) FROM patients")
        result = cursor.fetchone()
        last_id = result[0] or 0
        
        print(f"✅ آخر رقم وطني في قاعدة البيانات: {last_id}")
        print(f"✅ الرقم الوطني التالي سيكون: {last_id + 1}")
        
        # اختبار إضافة مريض جديد
        next_id = last_id + 1
        cursor.execute('''
            INSERT INTO patients (national_id, name, age, gender, address, sample_type, phone, 
                                sender_org, sample_date, tests)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (next_id, 'مريض اختبار', 30, 'M', 'عنوان اختبار', 'دم', '07801234567', 
              'مستشفى اختبار', '2024-01-15', 'COVID-19'))
        
        conn.commit()
        
        # التحقق من الرقم الجديد
        cursor.execute("SELECT MAX(national_id) FROM patients")
        new_result = cursor.fetchone()
        new_last_id = new_result[0]
        
        if new_last_id == next_id:
            print(f"✅ تم إضافة مريض جديد بالرقم الوطني: {new_last_id}")
            
            # حذف المريض التجريبي
            cursor.execute("DELETE FROM patients WHERE national_id = ?", (next_id,))
            conn.commit()
            print("✅ تم حذف المريض التجريبي")
        else:
            print("❌ فشل في إضافة المريض بالرقم الصحيح")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الرقم الوطني: {e}")
        return False

def test_logo_settings():
    """اختبار إعدادات الشعار"""
    print("\n🖼️ اختبار إعدادات الشعار...")
    
    try:
        # التحقق من وجود الشعار الافتراضي
        if os.path.exists('ministry_logo.png'):
            print("✅ الشعار الافتراضي موجود: ministry_logo.png")
        else:
            print("❌ الشعار الافتراضي مفقود")
        
        # اختبار حفظ إعدادات الشعار
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # حفظ مسار الشعار
        cursor.execute('''
            INSERT OR REPLACE INTO settings (category, name, value)
            VALUES (?, ?, ?)
        ''', ('report_settings', 'logo_path', 'ministry_logo.png'))
        
        # حفظ إعدادات أخرى
        cursor.execute('''
            INSERT OR REPLACE INTO settings (category, name, value)
            VALUES (?, ?, ?)
        ''', ('report_settings', 'arabic_header', 'جمهورية العراق\nوزارة الصحة'))
        
        conn.commit()
        
        # التحقق من الحفظ
        cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'logo_path'")
        result = cursor.fetchone()
        
        if result and result[0] == 'ministry_logo.png':
            print("✅ تم حفظ إعدادات الشعار بنجاح")
        else:
            print("❌ فشل في حفظ إعدادات الشعار")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إعدادات الشعار: {e}")
        return False

def test_date_functions():
    """اختبار وظائف التاريخ"""
    print("\n📅 اختبار وظائف التاريخ...")
    
    try:
        # اختبار تنسيق التاريخ
        today = datetime.datetime.now()
        date_str = today.strftime("%Y-%m-%d")
        print(f"✅ التاريخ الحالي بالتنسيق الصحيح: {date_str}")
        
        # اختبار تحويل التاريخ
        test_date = "2024-01-15"
        try:
            parsed_date = datetime.datetime.strptime(test_date, "%Y-%m-%d")
            print(f"✅ تم تحويل التاريخ بنجاح: {test_date} -> {parsed_date}")
        except:
            print(f"❌ فشل في تحويل التاريخ: {test_date}")
            return False
        
        # اختبار مقارنة التواريخ
        start_date = "2024-01-01"
        end_date = "2024-01-31"
        
        start_dt = datetime.datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.datetime.strptime(end_date, "%Y-%m-%d")
        
        if start_dt < end_dt:
            print(f"✅ مقارنة التواريخ صحيحة: {start_date} < {end_date}")
        else:
            print(f"❌ خطأ في مقارنة التواريخ")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف التاريخ: {e}")
        return False

def test_calendar_widget():
    """اختبار ويدجت التقويم"""
    print("\n📆 اختبار ويدجت التقويم...")
    
    try:
        import calendar
        
        # اختبار إنشاء تقويم للشهر الحالي
        today = datetime.datetime.now()
        cal = calendar.monthcalendar(today.year, today.month)
        
        print(f"✅ تم إنشاء تقويم للشهر {today.month}/{today.year}")
        print(f"✅ عدد الأسابيع في الشهر: {len(cal)}")
        
        # اختبار أسماء الأشهر
        months = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
        
        current_month_name = months[today.month]
        print(f"✅ اسم الشهر الحالي: {current_month_name}")
        
        # اختبار أيام الأسبوع
        days = ['أح', 'إث', 'ثل', 'أر', 'خم', 'جم', 'سب']
        print(f"✅ أيام الأسبوع: {', '.join(days)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ويدجت التقويم: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار الميزات الجديدة")
    print("=" * 60)
    
    # اختبار تسلسل الرقم الوطني
    national_id_test = test_national_id_sequence()
    
    # اختبار إعدادات الشعار
    logo_test = test_logo_settings()
    
    # اختبار وظائف التاريخ
    date_test = test_date_functions()
    
    # اختبار ويدجت التقويم
    calendar_test = test_calendar_widget()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"تسلسل الرقم الوطني: {'✅ نجح' if national_id_test else '❌ فشل'}")
    print(f"إعدادات الشعار: {'✅ نجح' if logo_test else '❌ فشل'}")
    print(f"وظائف التاريخ: {'✅ نجح' if date_test else '❌ فشل'}")
    print(f"ويدجت التقويم: {'✅ نجح' if calendar_test else '❌ فشل'}")
    
    if all([national_id_test, logo_test, date_test, calendar_test]):
        print("\n🎉 جميع الاختبارات نجحت! الميزات الجديدة جاهزة:")
        print("1. ✅ الرقم الوطني التصاعدي يعمل بشكل صحيح")
        print("2. ✅ إعدادات الشعار المخصص متاحة")
        print("3. ✅ اختيار التاريخ بالتقويم متاح في جميع التبويبات")
        print("\n🚀 يمكنك الآن تشغيل البرنامج واختبار الميزات الجديدة!")
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
