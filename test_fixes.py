#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات الجديدة
Test New Fixes
"""

import sqlite3
import datetime

def test_database_structure():
    """اختبار هيكل قاعدة البيانات"""
    print("🔍 اختبار هيكل قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # اختبار جدول المرضى
        cursor.execute("PRAGMA table_info(patients)")
        patients_columns = [row[1] for row in cursor.fetchall()]
        print(f"✅ أعمدة جدول المرضى: {patients_columns}")
        
        # اختبار جدول النتائج
        cursor.execute("PRAGMA table_info(results)")
        results_columns = [row[1] for row in cursor.fetchall()]
        print(f"✅ أعمدة جدول النتائج: {results_columns}")
        
        # التحقق من وجود عمود test_name
        if 'test_name' in results_columns:
            print("✅ عمود test_name موجود في جدول النتائج")
        else:
            print("❌ عمود test_name مفقود في جدول النتائج")
        
        # اختبار جدول الوجبات
        cursor.execute("PRAGMA table_info(batches)")
        batches_columns = [row[1] for row in cursor.fetchall()]
        print(f"✅ أعمدة جدول الوجبات: {batches_columns}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_sample_data():
    """إنشاء بيانات تجريبية للاختبار"""
    print("\n📝 إنشاء بيانات تجريبية...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # إضافة مريض تجريبي
        cursor.execute('''
            INSERT OR IGNORE INTO patients 
            (name, age, gender, address, sample_type, phone, sender_org, sample_date, tests)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('أحمد محمد علي', 35, 'M', 'الناصرية - حي الجمهورية', 'دم', '07801234567', 
              'مستشفى الناصرية العام', '2024-01-15', 'COVID-19,Hepatitis'))
        
        # إضافة وجبة تجريبية
        cursor.execute('''
            INSERT OR IGNORE INTO batches 
            (batch_no, start_date, end_date, technician)
            VALUES (?, ?, ?, ?)
        ''', (1, '2024-01-15', '2024-01-15', 'فاطمة أحمد'))
        
        # إضافة نتائج تجريبية
        cursor.execute("SELECT id FROM patients WHERE name = 'أحمد محمد علي'")
        patient_result = cursor.fetchone()
        
        cursor.execute("SELECT id FROM batches WHERE batch_no = 1")
        batch_result = cursor.fetchone()
        
        if patient_result and batch_result:
            patient_id = patient_result[0]
            batch_id = batch_result[0]
            
            # إضافة نتيجة COVID-19
            cursor.execute('''
                INSERT OR REPLACE INTO results 
                (patient_id, batch_id, test_name, result, result_date)
                VALUES (?, ?, ?, ?, ?)
            ''', (patient_id, batch_id, 'COVID-19', 'Negative', '2024-01-16'))
            
            # إضافة نتيجة Hepatitis
            cursor.execute('''
                INSERT OR REPLACE INTO results 
                (patient_id, batch_id, test_name, result, result_date)
                VALUES (?, ?, ?, ?, ?)
            ''', (patient_id, batch_id, 'Hepatitis', 'Positive', '2024-01-16'))
            
            print("✅ تم إنشاء بيانات تجريبية بنجاح")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def test_queries():
    """اختبار الاستعلامات"""
    print("\n🔍 اختبار الاستعلامات...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # اختبار استعلام النتائج
        print("📊 اختبار استعلام النتائج:")
        cursor.execute('''
            SELECT p.national_id, p.name, p.sample_type, r.test_name, r.result, r.result_date
            FROM patients p
            LEFT JOIN results r ON p.id = r.patient_id
            WHERE p.name = 'أحمد محمد علي'
        ''')
        
        results = cursor.fetchall()
        for result in results:
            print(f"  - {result}")
        
        # اختبار استعلام التقارير
        print("\n📋 اختبار استعلام التقارير:")
        cursor.execute('''
            SELECT p.national_id, p.name, p.age, p.gender, p.sample_type, 
                   p.sender_org, COALESCE(r.test_name, 'غير محدد') as test_name,
                   COALESCE(r.result, 'لا توجد نتيجة') as result,
                   COALESCE(r.result_date, '') as result_date
            FROM patients p
            LEFT JOIN results r ON p.id = r.patient_id
            ORDER BY p.national_id, r.test_name
        ''')
        
        reports = cursor.fetchall()
        for report in reports:
            print(f"  - {report}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستعلامات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار الإصلاحات الجديدة")
    print("=" * 60)
    
    # اختبار هيكل قاعدة البيانات
    db_test = test_database_structure()
    
    # إنشاء بيانات تجريبية
    data_test = test_sample_data()
    
    # اختبار الاستعلامات
    query_test = test_queries()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"هيكل قاعدة البيانات: {'✅ نجح' if db_test else '❌ فشل'}")
    print(f"البيانات التجريبية: {'✅ نجح' if data_test else '❌ فشل'}")
    print(f"الاستعلامات: {'✅ نجح' if query_test else '❌ فشل'}")
    
    if db_test and data_test and query_test:
        print("\n🎉 جميع الاختبارات نجحت! يمكنك الآن اختبار البرنامج:")
        print("1. شغل البرنامج: python main.py")
        print("2. اذهب لتبويب النتائج")
        print("3. أدخل رقم الوجبة: 1")
        print("4. اضغط 'عرض الوجبة'")
        print("5. اختر تحليل وأعطه نتيجة")
        print("6. اذهب لتبويب التقارير واختبر الطباعة")
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
