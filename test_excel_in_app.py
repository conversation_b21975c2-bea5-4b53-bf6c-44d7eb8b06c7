#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تصدير Excel داخل التطبيق
Test Excel Export in Application
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import datetime
import os

class ExcelTestApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار تصدير Excel")
        self.root.geometry("800x600")
        self.root.configure(bg='#ecf0f1')
        
        self.create_interface()
        self.create_sample_data()
    
    def create_interface(self):
        """إنشاء واجهة الاختبار"""
        # العنوان
        title_label = tk.Label(self.root, text="اختبار تصدير Excel", 
                              font=('Arial', 16, 'bold'), bg='#ecf0f1')
        title_label.pack(pady=20)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.root, bg='#ecf0f1')
        buttons_frame.pack(pady=20)
        
        button_style = {
            'font': ('Arial', 12, 'bold'),
            'relief': 'raised',
            'bd': 3,
            'width': 15,
            'height': 2
        }
        
        # أزرار الاختبار
        tk.Button(buttons_frame, text="اختبار المكتبات", bg='#3498db', fg='white',
                 command=self.test_libraries, **button_style).pack(side=tk.LEFT, padx=10)
        
        tk.Button(buttons_frame, text="تصدير بيانات تجريبية", bg='#27ae60', fg='white',
                 command=self.export_sample_data, **button_style).pack(side=tk.LEFT, padx=10)
        
        tk.Button(buttons_frame, text="تصدير جدول", bg='#f39c12', fg='white',
                 command=self.export_table_data, **button_style).pack(side=tk.LEFT, padx=10)
        
        # إطار الجدول
        table_frame = tk.Frame(self.root, bg='#ecf0f1')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # جدول البيانات
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'النتيجة')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين رؤوس الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # منطقة النتائج
        results_frame = tk.Frame(self.root, bg='#ecf0f1')
        results_frame.pack(fill=tk.X, padx=20, pady=10)
        
        tk.Label(results_frame, text="نتائج الاختبار:", font=('Arial', 12, 'bold'), 
                bg='#ecf0f1').pack(anchor='w')
        
        self.results_text = tk.Text(results_frame, height=8, font=('Arial', 10))
        self.results_text.pack(fill=tk.X, pady=5)
    
    def create_sample_data(self):
        """إنشاء بيانات تجريبية"""
        sample_data = [
            ("12345678901", "أحمد محمد علي", "35", "ذكر", "دم", "طبيعي"),
            ("12345678902", "فاطمة حسن", "28", "أنثى", "بول", "طبيعي"),
            ("12345678903", "علي أحمد", "42", "ذكر", "براز", "طبيعي"),
            ("12345678904", "زينب كاظم", "31", "أنثى", "دم", "طبيعي"),
            ("12345678905", "محمد جاسم", "45", "ذكر", "بول", "طبيعي"),
            ("12345678906", "سارة أحمد", "29", "أنثى", "دم", "طبيعي"),
            ("12345678907", "حسام علي", "38", "ذكر", "بول", "طبيعي"),
            ("12345678908", "نور فاطمة", "26", "أنثى", "دم", "طبيعي"),
            ("12345678909", "كريم محمد", "33", "ذكر", "بول", "طبيعي"),
            ("12345678910", "هدى علي", "41", "أنثى", "براز", "طبيعي")
        ]
        
        # إضافة البيانات للجدول
        for data in sample_data:
            self.tree.insert('', 'end', values=data)
        
        self.log_result(f"تم إنشاء {len(sample_data)} صف من البيانات التجريبية")
    
    def log_result(self, message):
        """إضافة رسالة لمنطقة النتائج"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.results_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.results_text.see(tk.END)
        self.root.update()
    
    def test_libraries(self):
        """اختبار المكتبات المطلوبة"""
        self.log_result("🧪 بدء اختبار المكتبات...")
        
        try:
            import pandas as pd
            self.log_result("✅ pandas: متوفر")
            
            import openpyxl
            self.log_result("✅ openpyxl: متوفر")
            
            # اختبار إنشاء DataFrame
            test_df = pd.DataFrame({'اختبار': ['قيمة1', 'قيمة2']})
            self.log_result("✅ إنشاء DataFrame: نجح")
            
            # اختبار حفظ ملف
            test_file = "test_temp.xlsx"
            test_df.to_excel(test_file, index=False)
            
            if os.path.exists(test_file):
                self.log_result("✅ حفظ ملف Excel: نجح")
                os.remove(test_file)
                self.log_result("✅ حذف الملف التجريبي: نجح")
            else:
                self.log_result("❌ حفظ ملف Excel: فشل")
            
            messagebox.showinfo("نتيجة الاختبار", "✅ جميع المكتبات تعمل بشكل صحيح!")
            
        except ImportError as e:
            self.log_result(f"❌ مكتبة مفقودة: {e}")
            messagebox.showerror("خطأ", f"مكتبة مفقودة: {e}\n\nلحل المشكلة:\npip install pandas openpyxl")
        except Exception as e:
            self.log_result(f"❌ خطأ في الاختبار: {e}")
            messagebox.showerror("خطأ", f"خطأ في الاختبار: {e}")
    
    def export_sample_data(self):
        """تصدير بيانات تجريبية"""
        self.log_result("📊 بدء تصدير البيانات التجريبية...")
        
        try:
            # إنشاء بيانات تجريبية
            data = [
                ["12345678901", "أحمد محمد علي", "35", "ذكر", "دم", "مستشفى الناصرية", "CBC", "طبيعي"],
                ["12345678902", "فاطمة حسن", "28", "أنثى", "بول", "مركز صحي السوق", "Urine", "طبيعي"],
                ["12345678903", "علي أحمد", "42", "ذكر", "براز", "مستشفى الحبوبي", "Stool", "طبيعي"]
            ]
            
            columns = ['الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة',
                      'جهة الإرسال', 'التحليل', 'النتيجة']
            
            df = pd.DataFrame(data, columns=columns)
            self.log_result(f"✅ تم إنشاء DataFrame بحجم: {df.shape}")
            
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                title="حفظ ملف Excel",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname=f"sample_export_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )
            
            if filename:
                # حفظ الملف
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='بيانات تجريبية')
                
                if os.path.exists(filename):
                    file_size = os.path.getsize(filename)
                    self.log_result(f"✅ تم حفظ الملف: {filename}")
                    self.log_result(f"📁 حجم الملف: {file_size} بايت")
                    
                    messagebox.showinfo("نجح", f"تم تصدير البيانات بنجاح!\n\nالملف: {filename}\nالحجم: {file_size} بايت")
                    
                    # فتح الملف
                    try:
                        os.startfile(filename)
                        self.log_result("✅ تم فتح الملف")
                    except:
                        self.log_result("⚠️ لا يمكن فتح الملف تلقائياً")
                else:
                    self.log_result("❌ فشل في إنشاء الملف")
            else:
                self.log_result("❌ تم إلغاء العملية")
                
        except Exception as e:
            self.log_result(f"❌ خطأ في التصدير: {e}")
            messagebox.showerror("خطأ", f"خطأ في التصدير: {e}")
    
    def export_table_data(self):
        """تصدير بيانات الجدول"""
        self.log_result("📋 بدء تصدير بيانات الجدول...")
        
        try:
            # جمع البيانات من الجدول
            data = []
            for item in self.tree.get_children():
                values = self.tree.item(item)['values']
                data.append([str(val) for val in values])
            
            if not data:
                self.log_result("❌ لا توجد بيانات في الجدول")
                messagebox.showwarning("تحذير", "لا توجد بيانات في الجدول")
                return
            
            columns = ['الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'النتيجة']
            df = pd.DataFrame(data, columns=columns)
            self.log_result(f"✅ تم جمع {len(data)} صف من الجدول")
            
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                title="حفظ بيانات الجدول",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname=f"table_export_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )
            
            if filename:
                # حفظ الملف
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='بيانات الجدول')
                
                if os.path.exists(filename):
                    file_size = os.path.getsize(filename)
                    self.log_result(f"✅ تم حفظ بيانات الجدول: {filename}")
                    self.log_result(f"📁 حجم الملف: {file_size} بايت")
                    
                    messagebox.showinfo("نجح", f"تم تصدير بيانات الجدول بنجاح!\n\nالملف: {filename}\nعدد الصفوف: {len(data)}\nالحجم: {file_size} بايت")
                    
                    # فتح الملف
                    try:
                        os.startfile(filename)
                        self.log_result("✅ تم فتح الملف")
                    except:
                        self.log_result("⚠️ لا يمكن فتح الملف تلقائياً")
                else:
                    self.log_result("❌ فشل في إنشاء الملف")
            else:
                self.log_result("❌ تم إلغاء العملية")
                
        except Exception as e:
            self.log_result(f"❌ خطأ في تصدير الجدول: {e}")
            messagebox.showerror("خطأ", f"خطأ في تصدير الجدول: {e}")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = ExcelTestApp()
    app.run()
