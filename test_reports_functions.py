#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف التقارير والإحصائيات
Test Reports and Statistics Functions
"""

import sqlite3
import datetime
import os

def test_required_libraries():
    """اختبار توفر المكتبات المطلوبة"""
    print("📚 اختبار توفر المكتبات المطلوبة...")
    
    required_libs = {
        'pandas': 'تصدير Excel',
        'openpyxl': 'تصدير Excel',
        'reportlab': 'طباعة PDF',
        'PIL': 'معالجة الصور'
    }
    
    missing_libs = []
    
    for lib, purpose in required_libs.items():
        try:
            if lib == 'pandas':
                import pandas as pd
                print(f"✅ {lib}: متوفر - {purpose}")
            elif lib == 'openpyxl':
                import openpyxl
                print(f"✅ {lib}: متوفر - {purpose}")
            elif lib == 'reportlab':
                from reportlab.lib.pagesizes import A4
                from reportlab.pdfgen import canvas
                from reportlab.lib.utils import ImageReader
                print(f"✅ {lib}: متوفر - {purpose}")
            elif lib == 'PIL':
                from PIL import Image, ImageTk
                print(f"✅ {lib}: متوفر - {purpose}")
        except ImportError:
            print(f"❌ {lib}: غير متوفر - {purpose}")
            missing_libs.append(lib)
    
    if missing_libs:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing_libs)}")
        print("لتثبيت المكتبات المفقودة:")
        for lib in missing_libs:
            print(f"  pip install {lib}")
        return False
    else:
        print("\n✅ جميع المكتبات المطلوبة متوفرة")
        return True

def test_database_data():
    """اختبار وجود البيانات في قاعدة البيانات"""
    print("\n🗄️ اختبار وجود البيانات في قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # فحص جدول المرضى
        cursor.execute("SELECT COUNT(*) FROM patients")
        patients_count = cursor.fetchone()[0]
        print(f"📊 عدد المرضى: {patients_count}")
        
        # فحص جدول النتائج
        cursor.execute("SELECT COUNT(*) FROM results")
        results_count = cursor.fetchone()[0]
        print(f"📊 عدد النتائج: {results_count}")
        
        # فحص جدول الوجبات
        cursor.execute("SELECT COUNT(*) FROM batches")
        batches_count = cursor.fetchone()[0]
        print(f"📊 عدد الوجبات: {batches_count}")
        
        if patients_count == 0:
            print("⚠️ لا توجد بيانات مرضى - إضافة بيانات تجريبية...")
            
            # إضافة بيانات تجريبية
            test_patients = [
                (5001, 'أحمد محمد علي', 30, 'M', 'بغداد', 'دم', '07801234567', 'مستشفى بغداد', '2024-01-25', 'COVID-19'),
                (5002, 'فاطمة حسن', 25, 'F', 'البصرة', 'بول', '07801234568', 'مستشفى البصرة', '2024-01-26', 'Hepatitis'),
                (5003, 'علي أحمد', 35, 'M', 'الموصل', 'دم', '07801234569', 'مستشفى الموصل', '2024-01-27', 'COVID-19,Hepatitis')
            ]
            
            for patient in test_patients:
                cursor.execute('''
                    INSERT OR REPLACE INTO patients 
                    (national_id, name, age, gender, address, sample_type, phone, sender_org, sample_date, tests)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', patient)
            
            # إضافة نتائج تجريبية
            cursor.execute("SELECT id FROM patients WHERE national_id IN (5001, 5002, 5003)")
            patient_ids = cursor.fetchall()
            
            test_results = [
                (patient_ids[0][0], 1, 'COVID-19', 'Negative', '2024-01-26'),
                (patient_ids[1][0], 1, 'Hepatitis', 'Positive', '2024-01-27'),
                (patient_ids[2][0], 1, 'COVID-19', 'Negative', '2024-01-28'),
                (patient_ids[2][0], 1, 'Hepatitis', 'Retest', '2024-01-28')
            ]
            
            for result in test_results:
                cursor.execute('''
                    INSERT OR REPLACE INTO results 
                    (patient_id, batch_id, test_name, result, result_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', result)
            
            conn.commit()
            print("✅ تم إضافة بيانات تجريبية")
            
            # إعادة فحص البيانات
            cursor.execute("SELECT COUNT(*) FROM patients")
            patients_count = cursor.fetchone()[0]
            cursor.execute("SELECT COUNT(*) FROM results")
            results_count = cursor.fetchone()[0]
            
            print(f"📊 عدد المرضى بعد الإضافة: {patients_count}")
            print(f"📊 عدد النتائج بعد الإضافة: {results_count}")
        
        conn.close()
        
        if patients_count > 0 and results_count > 0:
            print("✅ توجد بيانات كافية للاختبار")
            return True
        else:
            print("❌ لا توجد بيانات كافية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def test_excel_export_simulation():
    """اختبار محاكاة تصدير Excel"""
    print("\n📊 اختبار محاكاة تصدير Excel...")
    
    try:
        import pandas as pd
        import openpyxl
        
        # محاكاة البيانات من الجدول
        test_data = [
            [5001, 'أحمد محمد علي', 30, 'M', 'دم', 'مستشفى بغداد', 'COVID-19', 'Negative', '2024-01-26'],
            [5002, 'فاطمة حسن', 25, 'F', 'بول', 'مستشفى البصرة', 'Hepatitis', 'Positive', '2024-01-27'],
            [5003, 'علي أحمد', 35, 'M', 'دم', 'مستشفى الموصل', 'COVID-19', 'Negative', '2024-01-28']
        ]
        
        # إنشاء DataFrame
        columns = ['الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة',
                  'جهة الإرسال', 'التحليل', 'النتيجة', 'تاريخ النتيجة']
        
        df = pd.DataFrame(test_data, columns=columns)
        print(f"✅ تم إنشاء DataFrame مع {len(test_data)} صف")
        
        # اختبار حفظ الملف
        test_filename = f"test_export_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        try:
            df.to_excel(test_filename, index=False, engine='openpyxl')
            
            if os.path.exists(test_filename):
                print(f"✅ تم إنشاء ملف Excel تجريبي: {test_filename}")
                
                # حذف الملف التجريبي
                os.remove(test_filename)
                print("✅ تم حذف الملف التجريبي")
                return True
            else:
                print("❌ فشل في إنشاء ملف Excel")
                return False
                
        except Exception as save_error:
            print(f"❌ خطأ في حفظ ملف Excel: {save_error}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تصدير Excel: {e}")
        return False

def test_pdf_report_simulation():
    """اختبار محاكاة إنشاء تقرير PDF"""
    print("\n📄 اختبار محاكاة إنشاء تقرير PDF...")
    
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        from reportlab.lib.utils import ImageReader
        
        # إنشاء ملف PDF تجريبي
        test_filename = f"test_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        c = canvas.Canvas(test_filename, pagesize=A4)
        width, height = A4
        
        print(f"✅ تم إنشاء canvas PDF: {width:.1f} x {height:.1f}")
        
        # إضافة نص تجريبي
        c.setFont("Helvetica-Bold", 16)
        c.drawCentredText(width/2, height - 100, "Test Laboratory Report")
        c.drawCentredText(width/2, height - 120, "تقرير مختبر تجريبي")
        
        # إضافة جدول تجريبي
        c.setFont("Helvetica", 10)
        y_pos = height - 200
        
        headers = ["ID", "Name", "Test", "Result"]
        x_positions = [50, 150, 250, 350]
        
        # رؤوس الجدول
        for i, header in enumerate(headers):
            c.drawString(x_positions[i], y_pos, header)
        
        # خط تحت الرؤوس
        c.line(50, y_pos - 5, 400, y_pos - 5)
        
        # بيانات تجريبية
        test_data = [
            ["5001", "Ahmed Ali", "COVID-19", "Negative"],
            ["5002", "Fatima Hassan", "Hepatitis", "Positive"],
            ["5003", "Ali Ahmed", "COVID-19", "Negative"]
        ]
        
        y_pos -= 20
        for row in test_data:
            for i, cell in enumerate(row):
                c.drawString(x_positions[i], y_pos, str(cell))
            y_pos -= 15
        
        # حفظ الملف
        c.save()
        
        if os.path.exists(test_filename):
            print(f"✅ تم إنشاء ملف PDF تجريبي: {test_filename}")
            
            # حذف الملف التجريبي
            os.remove(test_filename)
            print("✅ تم حذف الملف التجريبي")
            return True
        else:
            print("❌ فشل في إنشاء ملف PDF")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء PDF: {e}")
        return False

def test_search_query():
    """اختبار استعلام البحث"""
    print("\n🔍 اختبار استعلام البحث...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # استعلام البحث الأساسي
        query = '''
            SELECT p.national_id, p.name, p.age, p.gender, p.sample_type,
                   p.sender_org,
                   COALESCE(r.test_name, 'غير محدد') as test_name,
                   COALESCE(r.result, 'لا توجد نتيجة') as result,
                   COALESCE(r.result_date, '') as result_date
            FROM patients p
            LEFT JOIN results r ON p.id = r.patient_id
            ORDER BY p.receive_date DESC, p.national_id, r.test_name
        '''
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"✅ تم تنفيذ استعلام البحث: {len(results)} نتيجة")
        
        if results:
            print("📋 عينة من النتائج:")
            for i, result in enumerate(results[:3]):  # أول 3 نتائج
                print(f"  {i+1}. {result[0]} - {result[1]} - {result[6]} - {result[7]}")
        
        conn.close()
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار استعلام البحث: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار وظائف التقارير والإحصائيات")
    print("=" * 60)
    
    # اختبار المكتبات المطلوبة
    libs_test = test_required_libraries()
    
    # اختبار البيانات في قاعدة البيانات
    data_test = test_database_data()
    
    # اختبار تصدير Excel
    excel_test = test_excel_export_simulation()
    
    # اختبار إنشاء PDF
    pdf_test = test_pdf_report_simulation()
    
    # اختبار استعلام البحث
    search_test = test_search_query()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"المكتبات المطلوبة: {'✅ نجح' if libs_test else '❌ فشل'}")
    print(f"البيانات في قاعدة البيانات: {'✅ نجح' if data_test else '❌ فشل'}")
    print(f"تصدير Excel: {'✅ نجح' if excel_test else '❌ فشل'}")
    print(f"إنشاء تقرير PDF: {'✅ نجح' if pdf_test else '❌ فشل'}")
    print(f"استعلام البحث: {'✅ نجح' if search_test else '❌ فشل'}")
    
    if all([libs_test, data_test, excel_test, pdf_test, search_test]):
        print("\n🎉 جميع الاختبارات نجحت! وظائف التقارير جاهزة:")
        print("1. ✅ جميع المكتبات المطلوبة متوفرة")
        print("2. ✅ توجد بيانات في قاعدة البيانات")
        print("3. ✅ تصدير Excel يعمل بشكل صحيح")
        print("4. ✅ إنشاء تقرير PDF يعمل بشكل صحيح")
        print("5. ✅ استعلام البحث يعمل بشكل صحيح")
        print("\n🚀 يمكنك الآن تشغيل البرنامج واختبار وظائف التقارير!")
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        
        if not libs_test:
            print("\n💡 لحل مشكلة المكتبات المفقودة:")
            print("pip install pandas openpyxl reportlab pillow")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
