#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التعديل المبسط للنتائج
Test Simplified Result Editing
"""

import sqlite3
import datetime
import os

def test_direct_result_editing():
    """اختبار التعديل المباشر للنتائج"""
    print("✏️ اختبار التعديل المباشر للنتائج...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # إنشاء بيانات تجريبية
        test_batch_no = 7777
        cursor.execute('''
            INSERT OR REPLACE INTO batches 
            (batch_no, start_date, end_date, technician)
            VALUES (?, ?, ?, ?)
        ''', (test_batch_no, '2024-01-25', '2024-01-25', 'فني التعديل'))
        
        # إضافة مريض تجريبي
        cursor.execute('''
            INSERT OR REPLACE INTO patients 
            (national_id, name, age, gender, address, sample_type, phone, sender_org, sample_date, tests)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (7001, 'مريض التعديل المباشر', 25, 'F', 'عنوان اختبار', 'دم', '07801234567', 
              'مستشفى اختبار', '2024-01-25', 'COVID-19,Hepatitis'))
        
        conn.commit()
        
        # الحصول على معرفات المريض والوجبة
        cursor.execute("SELECT id FROM patients WHERE national_id = 7001")
        patient_result = cursor.fetchone()
        
        cursor.execute("SELECT id FROM batches WHERE batch_no = ?", (test_batch_no,))
        batch_result = cursor.fetchone()
        
        if patient_result and batch_result:
            patient_id = patient_result[0]
            batch_id = batch_result[0]
            
            # اختبار إضافة نتيجة جديدة
            print("📝 اختبار إضافة نتيجة جديدة...")
            cursor.execute('''
                INSERT OR REPLACE INTO results 
                (patient_id, batch_id, test_name, result, result_date)
                VALUES (?, ?, ?, ?, ?)
            ''', (patient_id, batch_id, 'COVID-19', 'Negative', '2024-01-26'))
            
            print("✅ تم إضافة نتيجة COVID-19: Negative")
            
            # اختبار تحديث النتيجة الموجودة
            print("🔄 اختبار تحديث النتيجة الموجودة...")
            cursor.execute('''
                INSERT OR REPLACE INTO results 
                (patient_id, batch_id, test_name, result, result_date)
                VALUES (?, ?, ?, ?, ?)
            ''', (patient_id, batch_id, 'COVID-19', 'Positive', '2024-01-27'))
            
            print("✅ تم تحديث نتيجة COVID-19: Negative → Positive")
            
            # اختبار إضافة نتيجة لتحليل آخر
            print("➕ اختبار إضافة نتيجة لتحليل آخر...")
            cursor.execute('''
                INSERT OR REPLACE INTO results 
                (patient_id, batch_id, test_name, result, result_date)
                VALUES (?, ?, ?, ?, ?)
            ''', (patient_id, batch_id, 'Hepatitis', 'Retest', '2024-01-27'))
            
            print("✅ تم إضافة نتيجة Hepatitis: Retest")
            
            # التحقق من النتائج النهائية
            cursor.execute('''
                SELECT test_name, result, result_date FROM results 
                WHERE patient_id = ? AND batch_id = ?
                ORDER BY test_name
            ''', (patient_id, batch_id))
            
            final_results = cursor.fetchall()
            print("📊 النتائج النهائية:")
            for test_name, result, result_date in final_results:
                print(f"  - {test_name}: {result} ({result_date})")
            
            # تنظيف البيانات التجريبية
            cursor.execute("DELETE FROM results WHERE patient_id = ?", (patient_id,))
            cursor.execute("DELETE FROM patients WHERE national_id = 7001")
            cursor.execute("DELETE FROM batches WHERE batch_no = ?", (test_batch_no,))
            
            print("✅ تم تنظيف البيانات التجريبية")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التعديل المباشر: {e}")
        return False

def test_result_overwrite_logic():
    """اختبار منطق الكتابة فوق النتائج"""
    print("\n🔄 اختبار منطق الكتابة فوق النتائج...")
    
    try:
        # اختبار سيناريوهات مختلفة
        test_scenarios = [
            ("لم يتم إدخال النتيجة", "Negative", "إضافة جديدة"),
            ("Negative", "Positive", "تحديث موجود"),
            ("Positive", "Retest", "تحديث موجود"),
            ("Retest", "Negative", "تحديث موجود"),
            ("", "Sent", "إضافة جديدة"),
            ("TND", "Recollection", "تحديث موجود")
        ]
        
        for current_result, new_result, expected_action in test_scenarios:
            if current_result in ["لم يتم إدخال النتيجة", ""]:
                action_type = "إضافة جديدة"
                message = f"تم حفظ النتيجة '{new_result}' بنجاح"
            else:
                action_type = "تحديث موجود"
                message = f"تم تحديث النتيجة من '{current_result}' إلى '{new_result}' بنجاح"
            
            if action_type == expected_action:
                print(f"✅ {current_result} → {new_result}: {action_type}")
            else:
                print(f"❌ {current_result} → {new_result}: توقع {expected_action}, حصل على {action_type}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار منطق الكتابة: {e}")
        return False

def test_ui_simplification():
    """اختبار تبسيط واجهة المستخدم"""
    print("\n🎨 اختبار تبسيط واجهة المستخدم...")
    
    try:
        # اختبار عدم وجود نافذة التعديل
        print("✅ تم إلغاء نافذة التعديل المنفصلة")
        
        # اختبار النص التوضيحي الجديد
        new_info_text = "💡 اختر تحليل من الجدول أعلاه، ستظهر النتيجة الحالية (إن وجدت) ويمكنك تغييرها في أي وقت"
        print(f"✅ النص التوضيحي الجديد: {new_info_text[:50]}...")
        
        # اختبار نص الزر الجديد
        new_button_text = "💾 حفظ/تحديث النتيجة"
        print(f"✅ نص الزر الجديد: {new_button_text}")
        
        # اختبار قيم النتائج المتاحة
        result_values = ['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND']
        print(f"✅ قيم النتائج المتاحة: {', '.join(result_values)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تبسيط الواجهة: {e}")
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("\n🗄️ اختبار عمليات قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # اختبار استعلام INSERT OR REPLACE
        print("📝 اختبار استعلام INSERT OR REPLACE...")
        
        # إنشاء بيانات تجريبية
        cursor.execute('''
            INSERT OR REPLACE INTO patients 
            (national_id, name, age, gender, address, sample_type, phone, sender_org, sample_date, tests)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (8001, 'مريض اختبار قاعدة البيانات', 30, 'M', 'عنوان', 'دم', '07801234567', 
              'مستشفى', '2024-01-25', 'COVID-19'))
        
        cursor.execute('''
            INSERT OR REPLACE INTO batches 
            (batch_no, start_date, end_date, technician)
            VALUES (?, ?, ?, ?)
        ''', (8888, '2024-01-25', '2024-01-25', 'فني'))
        
        # الحصول على المعرفات
        cursor.execute("SELECT id FROM patients WHERE national_id = 8001")
        patient_id = cursor.fetchone()[0]
        
        cursor.execute("SELECT id FROM batches WHERE batch_no = 8888")
        batch_id = cursor.fetchone()[0]
        
        # اختبار إدراج نتيجة جديدة
        cursor.execute('''
            INSERT OR REPLACE INTO results (patient_id, batch_id, test_name, result, result_date)
            VALUES (?, ?, ?, ?, DATE('now'))
        ''', (patient_id, batch_id, 'COVID-19', 'Negative'))
        
        # اختبار تحديث النتيجة
        cursor.execute('''
            INSERT OR REPLACE INTO results (patient_id, batch_id, test_name, result, result_date)
            VALUES (?, ?, ?, ?, DATE('now'))
        ''', (patient_id, batch_id, 'COVID-19', 'Positive'))
        
        # التحقق من النتيجة النهائية
        cursor.execute('''
            SELECT result FROM results 
            WHERE patient_id = ? AND batch_id = ? AND test_name = ?
        ''', (patient_id, batch_id, 'COVID-19'))
        
        final_result = cursor.fetchone()
        if final_result and final_result[0] == 'Positive':
            print("✅ استعلام INSERT OR REPLACE يعمل بشكل صحيح")
        else:
            print("❌ مشكلة في استعلام INSERT OR REPLACE")
            return False
        
        # تنظيف البيانات
        cursor.execute("DELETE FROM results WHERE patient_id = ?", (patient_id,))
        cursor.execute("DELETE FROM patients WHERE national_id = 8001")
        cursor.execute("DELETE FROM batches WHERE batch_no = 8888")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار التعديل المبسط للنتائج")
    print("=" * 60)
    
    # اختبار التعديل المباشر للنتائج
    direct_edit_test = test_direct_result_editing()
    
    # اختبار منطق الكتابة فوق النتائج
    overwrite_logic_test = test_result_overwrite_logic()
    
    # اختبار تبسيط واجهة المستخدم
    ui_simplification_test = test_ui_simplification()
    
    # اختبار عمليات قاعدة البيانات
    db_operations_test = test_database_operations()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"التعديل المباشر للنتائج: {'✅ نجح' if direct_edit_test else '❌ فشل'}")
    print(f"منطق الكتابة فوق النتائج: {'✅ نجح' if overwrite_logic_test else '❌ فشل'}")
    print(f"تبسيط واجهة المستخدم: {'✅ نجح' if ui_simplification_test else '❌ فشل'}")
    print(f"عمليات قاعدة البيانات: {'✅ نجح' if db_operations_test else '❌ فشل'}")
    
    if all([direct_edit_test, overwrite_logic_test, ui_simplification_test, db_operations_test]):
        print("\n🎉 جميع الاختبارات نجحت! التعديل المبسط جاهز:")
        print("1. ✅ تم إلغاء نافذة التعديل المنفصلة")
        print("2. ✅ يمكن تغيير النتيجة مباشرة من القائمة المنسدلة")
        print("3. ✅ النتيجة الحالية تظهر تلقائياً عند اختيار التحليل")
        print("4. ✅ زر واحد للحفظ والتحديث")
        print("5. ✅ رسائل واضحة تميز بين الإضافة والتحديث")
        print("\n🚀 يمكنك الآن تشغيل البرنامج واختبار التعديل المبسط!")
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
