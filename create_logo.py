#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء شعار وزارة الصحة العراقية
Create Iraqi Ministry of Health Logo
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_ministry_logo():
    """إنشاء شعار وزارة الصحة العراقية"""
    
    # إنشاء صورة جديدة
    width, height = 200, 200
    img = Image.new('RGBA', (width, height), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان العلم العراقي
    red = (206, 17, 38)
    white = (255, 255, 255)
    black = (0, 0, 0)
    green = (0, 122, 61)
    
    # رسم دائرة خارجية
    outer_circle = [(10, 10), (190, 190)]
    draw.ellipse(outer_circle, fill=red, outline=black, width=3)
    
    # رسم دائرة داخلية
    inner_circle = [(30, 30), (170, 170)]
    draw.ellipse(inner_circle, fill=white, outline=black, width=2)
    
    # رسم صليب طبي
    # الخط العمودي
    draw.rectangle([(90, 60), (110, 140)], fill=red)
    # الخط الأفقي
    draw.rectangle([(60, 90), (140, 110)], fill=red)
    
    # رسم نجمة صغيرة في الأعلى
    star_points = [
        (100, 45), (105, 55), (115, 55), (107, 62),
        (110, 72), (100, 67), (90, 72), (93, 62),
        (85, 55), (95, 55)
    ]
    draw.polygon(star_points, fill=green)
    
    # رسم هلال صغير في الأسفل
    crescent_outer = [(85, 145), (115, 165)]
    crescent_inner = [(90, 148), (110, 162)]
    draw.ellipse(crescent_outer, fill=green)
    draw.ellipse(crescent_inner, fill=white)
    
    # حفظ الصورة
    img.save('ministry_logo.png', 'PNG')
    print("تم إنشاء شعار وزارة الصحة: ministry_logo.png")
    
    return 'ministry_logo.png'

def create_simple_logo():
    """إنشاء شعار بسيط إذا فشل الشعار المعقد"""
    
    width, height = 150, 150
    img = Image.new('RGB', (width, height), (255, 255, 255))
    draw = ImageDraw.Draw(img)
    
    # رسم دائرة
    draw.ellipse([(10, 10), (140, 140)], fill=(206, 17, 38), outline=(0, 0, 0), width=3)
    
    # رسم صليب
    draw.rectangle([(65, 30), (85, 120)], fill=(255, 255, 255))
    draw.rectangle([(30, 65), (120, 85)], fill=(255, 255, 255))
    
    # حفظ الصورة
    img.save('ministry_logo.png', 'PNG')
    print("تم إنشاء شعار بسيط: ministry_logo.png")
    
    return 'ministry_logo.png'

if __name__ == "__main__":
    try:
        logo_path = create_ministry_logo()
    except Exception as e:
        print(f"فشل في إنشاء الشعار المعقد: {e}")
        try:
            logo_path = create_simple_logo()
        except Exception as e2:
            print(f"فشل في إنشاء الشعار البسيط: {e2}")
            logo_path = None
    
    if logo_path and os.path.exists(logo_path):
        print(f"تم إنشاء الشعار بنجاح: {logo_path}")
    else:
        print("فشل في إنشاء الشعار")
