#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مختبر الصحة العامة المركزي - ذي قار
Central Public Health Laboratory - Dhi Qar
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import datetime
import os
from PIL import Image, ImageTk
import pandas as pd
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.utils import ImageReader
from reportlab.pdfbase import pdfutils
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
import win32print
import win32ui
from win32.lib import win32con

class LabManagementSystem:
    def __init__(self, root):
        self.root = root
        self.root.title("مختبر الصحة العامة المركزي - ذي قار | Central Public Health Laboratory - Dhi Qar")
        self.root.geometry("1400x800")
        self.root.configure(bg='#f0f8ff')

        # محاولة تعيين أيقونة
        try:
            if os.path.exists('icon.ico'):
                self.root.iconbitmap('icon.ico')
        except:
            pass

        # تعيين الحد الأدنى لحجم النافذة
        self.root.minsize(1200, 700)

        # إنشاء قاعدة البيانات
        self.init_database()

        # إنشاء الواجهة الرئيسية
        self.create_main_interface()

        # تحديث العدادات
        self.update_counters()

        # إضافة شريط الحالة
        self.create_status_bar()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        self.conn = sqlite3.connect('lab_database.db')
        self.cursor = self.conn.cursor()
        
        # جدول البيانات الأساسية
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                national_id INTEGER UNIQUE,
                name TEXT NOT NULL,
                age INTEGER NOT NULL,
                gender TEXT NOT NULL,
                address TEXT NOT NULL,
                sample_type TEXT NOT NULL,
                phone TEXT NOT NULL,
                sender_org TEXT NOT NULL,
                sample_date DATE NOT NULL,
                receive_date DATE DEFAULT CURRENT_DATE,
                passport_no TEXT,
                receipt_no TEXT,
                tests TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الوجبات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS batches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_no INTEGER UNIQUE,
                start_date DATE,
                end_date DATE,
                technician TEXT,
                work_date DATE DEFAULT CURRENT_DATE,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول النتائج (محسن لدعم تحاليل متعددة)
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                batch_id INTEGER,
                test_name TEXT,
                result TEXT,
                result_date DATE DEFAULT CURRENT_DATE,
                FOREIGN KEY (patient_id) REFERENCES patients (id),
                FOREIGN KEY (batch_id) REFERENCES batches (id)
            )
        ''')
        
        # جدول الإعدادات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT,
                name TEXT,
                value TEXT,
                UNIQUE(category, name)
            )
        ''')
        
        # تحديث قاعدة البيانات إذا لزم الأمر
        self.update_database_schema()

        # إدراج البيانات الافتراضية
        self.insert_default_settings()
        self.conn.commit()

    def update_database_schema(self):
        """تحديث هيكل قاعدة البيانات"""
        try:
            # التحقق من وجود عمود test_name في جدول results
            self.cursor.execute("PRAGMA table_info(results)")
            columns = [row[1] for row in self.cursor.fetchall()]

            if 'test_name' not in columns:
                print("تحديث قاعدة البيانات: إضافة عمود test_name...")
                # إضافة عمود test_name
                self.cursor.execute("ALTER TABLE results ADD COLUMN test_name TEXT")
                print("✅ تم تحديث قاعدة البيانات بنجاح")

        except Exception as e:
            print(f"تحذير: لم يتم تحديث قاعدة البيانات: {e}")
    
    def insert_default_settings(self):
        """إدراج الإعدادات الافتراضية"""
        default_settings = [
            ('sample_types', 'Blood', 'دم'),
            ('sample_types', 'Urine', 'بول'),
            ('sample_types', 'Stool', 'براز'),
            ('sample_types', 'Sputum', 'بلغم'),
            ('sender_orgs', 'Hospital A', 'مستشفى أ'),
            ('sender_orgs', 'Clinic B', 'عيادة ب'),
            ('tests', 'COVID-19', 'كوفيد-19'),
            ('tests', 'Hepatitis', 'التهاب الكبد'),
            ('technicians', 'أحمد محمد', 'أحمد محمد'),
            ('technicians', 'فاطمة علي', 'فاطمة علي'),
        ]
        
        for category, name, value in default_settings:
            self.cursor.execute('''
                INSERT OR IGNORE INTO settings (category, name, value)
                VALUES (?, ?, ?)
            ''', (category, name, value))
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية مع تصميم عصري"""
        # إنشاء الإطار الرئيسي مع تدرج لوني
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الشريط الجانبي للتبويبات مع تصميم حديث
        sidebar_frame = tk.Frame(main_frame, bg='#1a252f', width=220)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        sidebar_frame.pack_propagate(False)

        # شعار وعنوان المختبر مع تصميم أنيق
        header_frame = tk.Frame(sidebar_frame, bg='#1a252f')
        header_frame.pack(fill=tk.X, pady=20)

        # أيقونة المختبر
        lab_icon = tk.Label(header_frame, text="🏥", font=('Arial', 24),
                           bg='#1a252f', fg='#3498db')
        lab_icon.pack()

        title_label = tk.Label(header_frame,
                              text="مختبر الصحة العامة\nالمركزي - ذي قار",
                              font=('Arial', 11, 'bold'),
                              fg='white', bg='#1a252f',
                              justify=tk.CENTER)
        title_label.pack(pady=(10, 0))

        subtitle_label = tk.Label(header_frame,
                                 text="Central Public Health Lab",
                                 font=('Arial', 8),
                                 fg='#bdc3c7', bg='#1a252f')
        subtitle_label.pack(pady=(5, 0))

        # خط فاصل
        separator = tk.Frame(sidebar_frame, bg='#34495e', height=2)
        separator.pack(fill=tk.X, padx=20, pady=10)

        # أزرار التبويبات
        self.create_sidebar_buttons(sidebar_frame)

        # معلومات النظام في أسفل الشريط الجانبي
        info_frame = tk.Frame(sidebar_frame, bg='#1a252f')
        info_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=20)

        version_label = tk.Label(info_frame, text="الإصدار 1.0",
                               font=('Arial', 8), fg='#7f8c8d', bg='#1a252f')
        version_label.pack()

        # إنشاء منطقة المحتوى مع تصميم محسن
        self.content_frame = tk.Frame(main_frame, bg='#f8f9fa')
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # متغير لتتبع التبويب النشط
        self.active_tab = None

        # عرض التبويب الأول افتراضياً
        self.show_data_entry_tab()
    
    def create_sidebar_buttons(self, parent):
        """إنشاء أزرار الشريط الجانبي مع تصميم عصري"""
        # إطار الأزرار
        buttons_frame = tk.Frame(parent, bg='#1a252f')
        buttons_frame.pack(fill=tk.X, padx=15)

        # تعريف الأزرار مع الأيقونات
        buttons_data = [
            ("📝 إدخال البيانات", self.show_data_entry_tab, "#3498db"),
            ("⚙️ العمل", self.show_work_tab, "#e67e22"),
            ("📊 النتائج", self.show_results_tab, "#2ecc71"),
            ("📋 التقارير والإحصائيات", self.show_reports_tab, "#9b59b6"),
            ("🔧 الإعدادات", self.show_settings_tab, "#34495e")
        ]

        self.sidebar_buttons = []

        for i, (text, command, color) in enumerate(buttons_data):
            # إنشاء إطار للزر
            btn_frame = tk.Frame(buttons_frame, bg='#1a252f')
            btn_frame.pack(fill=tk.X, pady=3)

            # إنشاء الزر مع تصميم حديث
            btn = tk.Button(btn_frame, text=text,
                           font=('Arial', 10, 'bold'),
                           bg=color, fg='white',
                           relief='flat', bd=0,
                           width=25, height=2,
                           cursor='hand2',
                           activebackground=self.lighten_color(color),
                           activeforeground='white',
                           command=lambda cmd=command, idx=i: self.on_tab_click(cmd, idx))
            btn.pack(fill=tk.X, padx=5)

            # تأثيرات التفاعل
            btn.bind("<Enter>", lambda e, b=btn, c=color: self.on_button_hover(b, c))
            btn.bind("<Leave>", lambda e, b=btn, c=color: self.on_button_leave(b, c))

            self.sidebar_buttons.append(btn)

        # تعيين الزر الأول كنشط
        if self.sidebar_buttons:
            self.set_active_button(0)

    def lighten_color(self, color):
        """تفتيح اللون للتأثير التفاعلي"""
        color_map = {
            "#3498db": "#5dade2",
            "#e67e22": "#f39c12",
            "#2ecc71": "#58d68d",
            "#9b59b6": "#bb8fce",
            "#34495e": "#5d6d7e"
        }
        return color_map.get(color, color)

    def on_button_hover(self, button, original_color):
        """تأثير عند تمرير الماوس على الزر"""
        if button != self.sidebar_buttons[self.active_tab] if hasattr(self, 'active_tab') else True:
            button.config(bg=self.lighten_color(original_color))

    def on_button_leave(self, button, original_color):
        """تأثير عند مغادرة الماوس للزر"""
        if button != self.sidebar_buttons[self.active_tab] if hasattr(self, 'active_tab') else True:
            button.config(bg=original_color)

    def on_tab_click(self, command, index):
        """عند النقر على تبويب"""
        self.set_active_button(index)
        command()

    def set_active_button(self, index):
        """تعيين الزر النشط"""
        # إعادة تعيين جميع الأزرار
        colors = ["#3498db", "#e67e22", "#2ecc71", "#9b59b6", "#34495e"]
        for i, btn in enumerate(self.sidebar_buttons):
            if i == index:
                btn.config(bg="#1abc9c", relief='solid', bd=2)  # لون مميز للزر النشط
            else:
                btn.config(bg=colors[i], relief='flat', bd=0)

        self.active_tab = index
    
    def clear_content_frame(self):
        """مسح محتوى الإطار الرئيسي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_data_entry_tab(self):
        """عرض تبويب إدخال البيانات"""
        self.clear_content_frame()

        # عنوان التبويب مع تصميم حديث
        title_frame = tk.Frame(self.content_frame, bg='#2c3e50', height=60)
        title_frame.pack(fill=tk.X, padx=0, pady=0)
        title_frame.pack_propagate(False)

        title = tk.Label(title_frame, text="📋 إدخال البيانات",
                        font=('Arial', 18, 'bold'), bg='#2c3e50', fg='white')
        title.pack(expand=True)

        # إطار رئيسي مع تصميم حديث
        main_container = tk.Frame(self.content_frame, bg='#ecf0f1')
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # إطار النموذج مع حدود وظلال
        form_container = tk.LabelFrame(main_container, text="معلومات المريض",
                                     font=('Arial', 12, 'bold'), bg='white',
                                     fg='#2c3e50', relief='ridge', bd=2)
        form_container.pack(fill=tk.X, pady=(0, 15))

        # متغيرات النموذج
        self.entry_vars = {}

        # الصف الأول - المعلومات الأساسية
        row1_frame = tk.Frame(form_container, bg='white')
        row1_frame.pack(fill=tk.X, padx=15, pady=10)

        # الرقم الوطني (عرض فقط)
        tk.Label(row1_frame, text="الرقم الوطني", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=0, sticky='w', padx=(0, 5))
        self.national_id_display = tk.StringVar()
        national_id_label = tk.Label(row1_frame, textvariable=self.national_id_display,
                                   font=('Arial', 10, 'bold'), bg='#e8f5e8', fg='#27ae60',
                                   relief='solid', bd=1, width=10, anchor='center')
        national_id_label.grid(row=1, column=0, padx=(0, 15), pady=(2, 0), sticky='ew')

        # الاسم
        tk.Label(row1_frame, text="الاسم*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=1, sticky='w', padx=(0, 5))
        self.entry_vars['name'] = tk.StringVar()
        name_entry = tk.Entry(row1_frame, textvariable=self.entry_vars['name'],
                             font=('Arial', 10), width=20, relief='solid', bd=1)
        name_entry.grid(row=1, column=1, padx=(0, 15), pady=(2, 0), sticky='ew')

        # العمر
        tk.Label(row1_frame, text="العمر*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=2, sticky='w', padx=(0, 5))
        self.entry_vars['age'] = tk.StringVar()
        age_entry = tk.Entry(row1_frame, textvariable=self.entry_vars['age'],
                            font=('Arial', 10), width=10, relief='solid', bd=1)
        age_entry.grid(row=1, column=2, padx=(0, 15), pady=(2, 0), sticky='ew')

        # الجنس
        tk.Label(row1_frame, text="الجنس*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=3, sticky='w', padx=(0, 5))
        self.entry_vars['gender'] = tk.StringVar()
        gender_combo = ttk.Combobox(row1_frame, textvariable=self.entry_vars['gender'],
                                   values=['M', 'F'], width=8, state='readonly')
        gender_combo.grid(row=1, column=3, padx=(0, 15), pady=(2, 0), sticky='ew')

        # رقم الهاتف
        tk.Label(row1_frame, text="رقم الهاتف*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=4, sticky='w', padx=(0, 5))
        self.entry_vars['phone'] = tk.StringVar()
        phone_entry = tk.Entry(row1_frame, textvariable=self.entry_vars['phone'],
                              font=('Arial', 10), width=15, relief='solid', bd=1)
        phone_entry.grid(row=1, column=4, padx=(0, 0), pady=(2, 0), sticky='ew')

        # تكوين الأعمدة للتوسع
        row1_frame.columnconfigure(0, weight=1)
        row1_frame.columnconfigure(1, weight=2)
        row1_frame.columnconfigure(2, weight=1)
        row1_frame.columnconfigure(3, weight=1)
        row1_frame.columnconfigure(4, weight=1)

        # تحديث الرقم الوطني التلقائي
        self.update_next_national_id()

        # الصف الثاني - العنوان ونوع العينة
        row2_frame = tk.Frame(form_container, bg='white')
        row2_frame.pack(fill=tk.X, padx=15, pady=10)

        # العنوان
        tk.Label(row2_frame, text="العنوان*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=0, sticky='w', padx=(0, 5))
        self.entry_vars['address'] = tk.StringVar()
        address_entry = tk.Entry(row2_frame, textvariable=self.entry_vars['address'],
                                font=('Arial', 10), width=30, relief='solid', bd=1)
        address_entry.grid(row=1, column=0, padx=(0, 15), pady=(2, 0), sticky='ew')

        # نوع العينة
        tk.Label(row2_frame, text="نوع العينة*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=1, sticky='w', padx=(0, 5))
        self.entry_vars['sample_type'] = tk.StringVar()
        sample_combo = ttk.Combobox(row2_frame, textvariable=self.entry_vars['sample_type'],
                                   width=15, state='readonly')
        sample_combo.grid(row=1, column=1, padx=(0, 15), pady=(2, 0), sticky='ew')
        self.update_combobox_values(sample_combo, "sample_type")

        # جهة الإرسال
        tk.Label(row2_frame, text="جهة الإرسال*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=2, sticky='w', padx=(0, 5))
        self.entry_vars['sender_org'] = tk.StringVar()
        sender_combo = ttk.Combobox(row2_frame, textvariable=self.entry_vars['sender_org'],
                                   width=15, state='readonly')
        sender_combo.grid(row=1, column=2, padx=(0, 0), pady=(2, 0), sticky='ew')
        self.update_combobox_values(sender_combo, "sender_org")

        # تكوين الأعمدة للتوسع
        row2_frame.columnconfigure(0, weight=2)
        row2_frame.columnconfigure(1, weight=1)
        row2_frame.columnconfigure(2, weight=1)

        # الصف الثالث - التواريخ والمعلومات الإضافية
        row3_frame = tk.Frame(form_container, bg='white')
        row3_frame.pack(fill=tk.X, padx=15, pady=10)

        # تاريخ سحب العينة مع تحسينات
        date_label_frame = tk.Frame(row3_frame, bg='white')
        date_label_frame.grid(row=0, column=0, sticky='w', padx=(0, 5))

        tk.Label(date_label_frame, text="تاريخ سحب العينة*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(side=tk.LEFT)
        tk.Button(date_label_frame, text="📅", font=('Arial', 8), bg='#3498db', fg='white',
                 relief='flat', bd=0, cursor='hand2',
                 command=self.set_current_date).pack(side=tk.LEFT, padx=(5, 0))

        self.entry_vars['sample_date'] = tk.StringVar()
        # تعيين التاريخ الحالي كافتراضي
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        self.entry_vars['sample_date'].set(current_date)

        date_frame = tk.Frame(row3_frame, bg='white')
        date_frame.grid(row=1, column=0, padx=(0, 15), pady=(2, 0), sticky='ew')

        date_entry = tk.Entry(date_frame, textvariable=self.entry_vars['sample_date'],
                             font=('Arial', 10), width=12, relief='solid', bd=1)
        date_entry.pack(side=tk.LEFT)

        # زر التاريخ الحالي
        tk.Button(date_frame, text="اليوم", font=('Arial', 8), bg='#95a5a6', fg='white',
                 relief='flat', bd=0, cursor='hand2', width=4,
                 command=self.set_current_date).pack(side=tk.LEFT, padx=(2, 0))

        # رقم الجواز
        tk.Label(row3_frame, text="رقم الجواز", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=1, sticky='w', padx=(0, 5))
        self.entry_vars['passport_no'] = tk.StringVar()
        passport_entry = tk.Entry(row3_frame, textvariable=self.entry_vars['passport_no'],
                                 font=('Arial', 10), width=15, relief='solid', bd=1)
        passport_entry.grid(row=1, column=1, padx=(0, 15), pady=(2, 0), sticky='ew')

        # رقم الوصل
        tk.Label(row3_frame, text="رقم الوصل", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=2, sticky='w', padx=(0, 5))
        self.entry_vars['receipt_no'] = tk.StringVar()
        receipt_entry = tk.Entry(row3_frame, textvariable=self.entry_vars['receipt_no'],
                                font=('Arial', 10), width=15, relief='solid', bd=1)
        receipt_entry.grid(row=1, column=2, padx=(0, 0), pady=(2, 0), sticky='ew')

        # تكوين الأعمدة للتوسع
        row3_frame.columnconfigure(0, weight=1)
        row3_frame.columnconfigure(1, weight=1)
        row3_frame.columnconfigure(2, weight=1)

        # الصف الرابع - التحاليل
        row4_frame = tk.Frame(form_container, bg='white')
        row4_frame.pack(fill=tk.X, padx=15, pady=10)

        tk.Label(row4_frame, text="التحاليل المطلوبة:", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w')

        tests_frame = tk.Frame(row4_frame, bg='white', relief='solid', bd=1)
        tests_frame.pack(fill=tk.X, pady=(5, 0))

        self.tests_listbox = tk.Listbox(tests_frame, selectmode=tk.MULTIPLE,
                                       height=3, font=('Arial', 9), bg='#f8f9fa')
        scrollbar_tests = ttk.Scrollbar(tests_frame, orient=tk.VERTICAL, command=self.tests_listbox.yview)
        self.tests_listbox.configure(yscrollcommand=scrollbar_tests.set)

        self.tests_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_tests.pack(side=tk.RIGHT, fill=tk.Y)
        self.update_tests_listbox()

        # أزرار العمليات مع تصميم حديث
        buttons_container = tk.Frame(main_container, bg='#ecf0f1')
        buttons_container.pack(fill=tk.X, pady=(0, 15))

        button_style = {
            'font': ('Arial', 10, 'bold'),
            'relief': 'raised',
            'bd': 3,
            'width': 12,
            'height': 2,
            'cursor': 'hand2'
        }

        buttons_frame = tk.Frame(buttons_container, bg='#ecf0f1')
        buttons_frame.pack()

        tk.Button(buttons_frame, text="➕ إضافة", bg='#27ae60', fg='white',
                 command=self.add_patient, **button_style).pack(side=tk.LEFT, padx=8)

        tk.Button(buttons_frame, text="✏️ تعديل", bg='#f39c12', fg='white',
                 command=self.edit_patient, **button_style).pack(side=tk.LEFT, padx=8)

        tk.Button(buttons_frame, text="🗑️ حذف", bg='#e74c3c', fg='white',
                 command=self.delete_patient, **button_style).pack(side=tk.LEFT, padx=8)

        tk.Button(buttons_frame, text="🔍 بحث", bg='#3498db', fg='white',
                 command=self.search_patient, **button_style).pack(side=tk.LEFT, padx=8)

        tk.Button(buttons_frame, text="📊 استيراد Excel", bg='#9b59b6', fg='white',
                 command=self.import_excel, **button_style).pack(side=tk.LEFT, padx=8)

        # جدول عرض البيانات مع تصميم محسن
        self.create_patients_table_modern(main_container)
    
    def update_combobox_values(self, combo, category):
        """تحديث قيم القائمة المنسدلة من قاعدة البيانات"""
        if category == "sample_type":
            db_category = "sample_types"
        elif category == "sender_org":
            db_category = "sender_orgs"
        else:
            return
        
        self.cursor.execute("SELECT value FROM settings WHERE category = ?", (db_category,))
        values = [row[0] for row in self.cursor.fetchall()]
        combo['values'] = values
    
    def update_tests_listbox(self):
        """تحديث قائمة التحاليل"""
        self.tests_listbox.delete(0, tk.END)
        self.cursor.execute("SELECT value FROM settings WHERE category = 'tests'")
        tests = [row[0] for row in self.cursor.fetchall()]
        for test in tests:
            self.tests_listbox.insert(tk.END, test)
    
    def create_patients_table_modern(self, parent):
        """إنشاء جدول عرض المرضى مع تصميم حديث"""
        # إطار الجدول مع عنوان
        table_container = tk.LabelFrame(parent, text="قائمة المرضى",
                                       font=('Arial', 12, 'bold'), bg='white',
                                       fg='#2c3e50', relief='ridge', bd=2)
        table_container.pack(fill=tk.BOTH, expand=True)

        # إطار البحث السريع
        search_frame = tk.Frame(table_container, bg='white')
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(search_frame, text="🔍 بحث سريع:", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(side=tk.LEFT, padx=(0, 10))

        self.quick_search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.quick_search_var,
                               font=('Arial', 10), width=30, relief='solid', bd=1)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_quick_search)

        tk.Button(search_frame, text="مسح", font=('Arial', 9), bg='#95a5a6', fg='white',
                 relief='raised', bd=2, command=self.clear_quick_search).pack(side=tk.LEFT)

        # إطار الجدول
        table_frame = tk.Frame(table_container, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # إنشاء Treeview مع تصميم محسن
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'تاريخ الاستلام')
        self.patients_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)

        # تعيين عناوين الأعمدة مع تحسين العرض
        column_widths = {'الرقم الوطني': 100, 'الاسم': 150, 'العمر': 60,
                        'الجنس': 60, 'نوع العينة': 120, 'تاريخ الاستلام': 120}

        for col in columns:
            self.patients_tree.heading(col, text=col, anchor='center')
            self.patients_tree.column(col, width=column_widths.get(col, 100),
                                    anchor='center', minwidth=50)

        # تنسيق الجدول
        style = ttk.Style()
        style.configure("Treeview", font=('Arial', 9))
        style.configure("Treeview.Heading", font=('Arial', 10, 'bold'))

        # شريط التمرير العمودي والأفقي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.patients_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.patients_tree.xview)

        self.patients_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.patients_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # تكوين التوسع
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # إضافة ألوان متناوبة للصفوف
        self.patients_tree.tag_configure('oddrow', background='#f8f9fa')
        self.patients_tree.tag_configure('evenrow', background='white')

        # تحديث البيانات
        self.refresh_patients_table()

    def on_quick_search(self, event):
        """البحث السريع في الجدول"""
        search_term = self.quick_search_var.get().lower()

        # مسح الجدول
        for item in self.patients_tree.get_children():
            self.patients_tree.delete(item)

        # جلب البيانات مع الفلترة
        if search_term:
            query = '''
                SELECT national_id, name, age, gender, sample_type, receive_date
                FROM patients
                WHERE LOWER(name) LIKE ? OR CAST(national_id AS TEXT) LIKE ?
                ORDER BY national_id DESC
            '''
            self.cursor.execute(query, (f'%{search_term}%', f'%{search_term}%'))
        else:
            query = '''
                SELECT national_id, name, age, gender, sample_type, receive_date
                FROM patients ORDER BY national_id DESC
            '''
            self.cursor.execute(query)

        # إضافة البيانات مع الألوان المتناوبة
        for i, row in enumerate(self.cursor.fetchall()):
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.patients_tree.insert('', tk.END, values=row, tags=(tag,))

    def clear_quick_search(self):
        """مسح البحث السريع"""
        self.quick_search_var.set("")
        self.refresh_patients_table()
    
    def refresh_patients_table(self):
        """تحديث جدول المرضى مع تحسينات"""
        # مسح البيانات الحالية
        for item in self.patients_tree.get_children():
            self.patients_tree.delete(item)

        # جلب البيانات من قاعدة البيانات
        self.cursor.execute('''
            SELECT national_id, name, age, gender, sample_type, receive_date
            FROM patients ORDER BY national_id DESC
        ''')

        # إضافة البيانات مع الألوان المتناوبة
        for i, row in enumerate(self.cursor.fetchall()):
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.patients_tree.insert('', tk.END, values=row, tags=(tag,))
    
    def add_patient(self):
        """إضافة مريض جديد"""
        # التحقق من الحقول المطلوبة
        required_fields = ['name', 'age', 'gender', 'address', 'sample_type', 'phone', 'sender_org', 'sample_date']
        
        for field in required_fields:
            if not self.entry_vars[field].get().strip():
                messagebox.showerror("خطأ", f"يرجى ملء حقل {field}")
                return
        
        # الحصول على التحاليل المحددة
        selected_tests = [self.tests_listbox.get(i) for i in self.tests_listbox.curselection()]
        tests_str = ','.join(selected_tests)
        
        try:
            # إدراج البيانات
            self.cursor.execute('''
                INSERT INTO patients (name, age, gender, address, sample_type, phone, 
                                    sender_org, sample_date, passport_no, receipt_no, tests)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.entry_vars['name'].get(),
                int(self.entry_vars['age'].get()),
                self.entry_vars['gender'].get(),
                self.entry_vars['address'].get(),
                self.entry_vars['sample_type'].get(),
                self.entry_vars['phone'].get(),
                self.entry_vars['sender_org'].get(),
                self.entry_vars['sample_date'].get(),
                self.entry_vars['passport_no'].get(),
                self.entry_vars['receipt_no'].get(),
                tests_str
            ))
            
            self.conn.commit()
            
            # طباعة الاستيكر
            self.print_sticker()

            # تحديث الجدول والعدادات
            self.refresh_patients_table()
            self.update_counters()

            # تحديث الرقم الوطني التالي
            self.update_next_national_id()

            # مسح الحقول
            self.clear_entry_fields()

            # تحديث شريط الحالة
            self.update_status("تم إضافة المريض بنجاح")

            messagebox.showinfo("نجح", "تم إضافة المريض بنجاح وطباعة الاستيكر")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة المريض: {str(e)}")
    
    def print_sticker(self):
        """طباعة استيكر المريض"""
        try:
            # الحصول على آخر مريض مضاف
            self.cursor.execute("SELECT * FROM patients ORDER BY id DESC LIMIT 1")
            patient = self.cursor.fetchone()

            if patient:
                # إنشاء نص الاستيكر
                sticker_text = f"""
╔══════════════════════════════════════╗
║        مختبر الصحة العامة المركزي        ║
║              ذي قار                  ║
╠══════════════════════════════════════╣
║ اسم المريض: {patient[2]:<20} ║
║ نوع العينة: {patient[5]:<20} ║
║ الرقم الوطني: {patient[1]:<19} ║
║ التاريخ: {patient[9]:<24} ║
╚══════════════════════════════════════╝
                """

                # محاولة طباعة الاستيكر على طابعة الاستيكر
                self.print_to_sticker_printer(sticker_text)

        except Exception as e:
            print(f"خطأ في طباعة الاستيكر: {str(e)}")

    def print_to_sticker_printer(self, text):
        """طباعة على طابعة الاستيكر"""
        try:
            # البحث عن طابعة الاستيكر تلقائياً
            printers = win32print.EnumPrinters(2)
            sticker_printer = None

            # البحث عن طابعة تحتوي على كلمات مفتاحية للاستيكر
            sticker_keywords = ['label', 'sticker', 'zebra', 'dymo', 'brother']

            for printer in printers:
                printer_name = printer[2].lower()
                if any(keyword in printer_name for keyword in sticker_keywords):
                    sticker_printer = printer[2]
                    break

            # إذا لم توجد طابعة استيكر، استخدم الطابعة الافتراضية
            if not sticker_printer:
                sticker_printer = win32print.GetDefaultPrinter()

            # إنشاء مهمة طباعة
            hprinter = win32print.OpenPrinter(sticker_printer)
            try:
                hdc = win32ui.CreateDC()
                hdc.CreatePrinterDC(sticker_printer)
                hdc.StartDoc("Lab Sticker")
                hdc.StartPage()

                # طباعة النص
                hdc.TextOut(50, 50, text)

                hdc.EndPage()
                hdc.EndDoc()
                hdc.DeleteDC()

                print(f"تم إرسال الاستيكر إلى الطابعة: {sticker_printer}")

            finally:
                win32print.ClosePrinter(hprinter)

        except Exception as e:
            # في حالة فشل الطباعة، عرض الاستيكر في نافذة
            self.show_sticker_preview(text)
            print(f"تعذر الطباعة، عرض معاينة: {str(e)}")

    def show_sticker_preview(self, text):
        """عرض معاينة الاستيكر"""
        preview_window = tk.Toplevel(self.root)
        preview_window.title("معاينة الاستيكر")
        preview_window.geometry("400x300")
        preview_window.configure(bg='white')

        # عرض نص الاستيكر
        text_widget = tk.Text(preview_window, font=('Courier', 10),
                             bg='white', fg='black', wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert('1.0', text)
        text_widget.config(state=tk.DISABLED)

        # زر إغلاق
        tk.Button(preview_window, text="إغلاق", font=('Arial', 10, 'bold'),
                 bg='#e74c3c', fg='white', command=preview_window.destroy).pack(pady=10)
    
    def update_next_national_id(self):
        """تحديث الرقم الوطني التالي"""
        try:
            self.cursor.execute("SELECT MAX(national_id) FROM patients")
            result = self.cursor.fetchone()
            next_id = (result[0] or 0) + 1
            self.national_id_display.set(f"#{next_id}")
        except:
            self.national_id_display.set("#1")

    def set_current_date(self):
        """تعيين التاريخ الحالي"""
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        self.entry_vars['sample_date'].set(current_date)

    def clear_entry_fields(self):
        """مسح حقول الإدخال"""
        for var in self.entry_vars.values():
            var.set("")
        # إعادة تعيين التاريخ الحالي
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        self.entry_vars['sample_date'].set(current_date)
        self.tests_listbox.selection_clear(0, tk.END)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة مع تصميم عصري"""
        self.status_bar = tk.Frame(self.root, bg='#1a252f', height=35)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_bar.pack_propagate(False)

        # إطار المعلومات اليسار
        left_frame = tk.Frame(self.status_bar, bg='#1a252f')
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=15)

        # معلومات الحالة مع أيقونة
        status_frame = tk.Frame(left_frame, bg='#1a252f')
        status_frame.pack(side=tk.LEFT, pady=8)

        tk.Label(status_frame, text="🟢", font=('Arial', 10),
                bg='#1a252f', fg='#2ecc71').pack(side=tk.LEFT)
        self.status_label = tk.Label(status_frame, text="جاهز",
                                    font=('Arial', 9, 'bold'), bg='#1a252f', fg='#ecf0f1')
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))

        # فاصل
        tk.Label(left_frame, text="│", font=('Arial', 12),
                bg='#1a252f', fg='#34495e').pack(side=tk.LEFT, padx=15)

        # عداد المرضى مع أيقونة
        patients_frame = tk.Frame(left_frame, bg='#1a252f')
        patients_frame.pack(side=tk.LEFT, pady=8)

        tk.Label(patients_frame, text="👥", font=('Arial', 10),
                bg='#1a252f').pack(side=tk.LEFT)
        self.patients_count_label = tk.Label(patients_frame, text="المرضى: 0",
                                           font=('Arial', 9), bg='#1a252f', fg='#3498db')
        self.patients_count_label.pack(side=tk.LEFT, padx=(5, 0))

        # فاصل
        tk.Label(left_frame, text="│", font=('Arial', 12),
                bg='#1a252f', fg='#34495e').pack(side=tk.LEFT, padx=15)

        # عداد الوجبات مع أيقونة
        batches_frame = tk.Frame(left_frame, bg='#1a252f')
        batches_frame.pack(side=tk.LEFT, pady=8)

        tk.Label(batches_frame, text="📦", font=('Arial', 10),
                bg='#1a252f').pack(side=tk.LEFT)
        self.batches_count_label = tk.Label(batches_frame, text="الوجبات: 0",
                                          font=('Arial', 9), bg='#1a252f', fg='#e67e22')
        self.batches_count_label.pack(side=tk.LEFT, padx=(5, 0))

        # إطار المعلومات اليمين
        right_frame = tk.Frame(self.status_bar, bg='#1a252f')
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=15)

        # التاريخ والوقت مع أيقونة
        datetime_frame = tk.Frame(right_frame, bg='#1a252f')
        datetime_frame.pack(side=tk.RIGHT, pady=8)

        tk.Label(datetime_frame, text="🕐", font=('Arial', 10),
                bg='#1a252f').pack(side=tk.LEFT)
        self.datetime_label = tk.Label(datetime_frame, text="",
                                     font=('Arial', 9), bg='#1a252f', fg='#bdc3c7')
        self.datetime_label.pack(side=tk.LEFT, padx=(5, 0))

        # تحديث التاريخ والوقت
        self.update_datetime()

    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.datetime.now()
        datetime_str = now.strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.config(text=datetime_str)

        # تحديث كل ثانية
        self.root.after(1000, self.update_datetime)

    def update_counters(self):
        """تحديث العدادات"""
        try:
            # عدد المرضى
            self.cursor.execute("SELECT COUNT(*) FROM patients")
            patients_count = self.cursor.fetchone()[0]
            self.patients_count_label.config(text=f"المرضى: {patients_count}")

            # عدد الوجبات
            self.cursor.execute("SELECT COUNT(*) FROM batches")
            batches_count = self.cursor.fetchone()[0]
            self.batches_count_label.config(text=f"الوجبات: {batches_count}")

        except:
            pass

    def update_status(self, message):
        """تحديث رسالة الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)
            self.root.update_idletasks()
    
    def edit_patient(self):
        """تعديل بيانات المريض"""
        selection = self.patients_tree.selection()
        if not selection:
            messagebox.showerror("خطأ", "يرجى اختيار مريض للتعديل")
            return

        # الحصول على بيانات المريض المحدد
        item = self.patients_tree.item(selection[0])
        national_id = item['values'][0]

        # جلب البيانات الكاملة من قاعدة البيانات
        self.cursor.execute("SELECT * FROM patients WHERE national_id = ?", (national_id,))
        patient = self.cursor.fetchone()

        if patient:
            # ملء الحقول بالبيانات الحالية
            self.entry_vars['name'].set(patient[2])
            self.entry_vars['age'].set(patient[3])
            self.entry_vars['gender'].set(patient[4])
            self.entry_vars['address'].set(patient[5])
            self.entry_vars['sample_type'].set(patient[6])
            self.entry_vars['phone'].set(patient[7])
            self.entry_vars['sender_org'].set(patient[8])
            self.entry_vars['sample_date'].set(patient[9])
            self.entry_vars['passport_no'].set(patient[11] or "")
            self.entry_vars['receipt_no'].set(patient[12] or "")

            # تحديد التحاليل
            if patient[13]:
                tests = patient[13].split(',')
                for i in range(self.tests_listbox.size()):
                    if self.tests_listbox.get(i) in tests:
                        self.tests_listbox.selection_set(i)

            # تغيير زر الإضافة إلى تحديث
            messagebox.showinfo("تعديل", "تم تحميل بيانات المريض. قم بالتعديل ثم اضغط 'تحديث'")

    def delete_patient(self):
        """حذف المريض"""
        selection = self.patients_tree.selection()
        if not selection:
            messagebox.showerror("خطأ", "يرجى اختيار مريض للحذف")
            return

        item = self.patients_tree.item(selection[0])
        national_id = item['values'][0]
        patient_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المريض '{patient_name}'؟"):
            try:
                # حذف النتائج المرتبطة أولاً
                self.cursor.execute('''
                    DELETE FROM results WHERE patient_id =
                    (SELECT id FROM patients WHERE national_id = ?)
                ''', (national_id,))

                # حذف المريض
                self.cursor.execute("DELETE FROM patients WHERE national_id = ?", (national_id,))
                self.conn.commit()

                # تحديث الجدول
                self.refresh_patients_table()
                messagebox.showinfo("نجح", "تم حذف المريض بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    def search_patient(self):
        """البحث عن المريض"""
        search_window = tk.Toplevel(self.root)
        search_window.title("البحث عن المريض")
        search_window.geometry("400x300")
        search_window.configure(bg='#ecf0f1')

        # حقول البحث
        tk.Label(search_window, text="البحث بالاسم:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5, sticky='e')
        search_name = tk.StringVar()
        tk.Entry(search_window, textvariable=search_name, width=25).grid(
            row=0, column=1, padx=5, pady=5)

        tk.Label(search_window, text="البحث بالرقم الوطني:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=1, column=0, padx=5, pady=5, sticky='e')
        search_national_id = tk.StringVar()
        tk.Entry(search_window, textvariable=search_national_id, width=25).grid(
            row=1, column=1, padx=5, pady=5)

        tk.Label(search_window, text="البحث بالهاتف:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=2, column=0, padx=5, pady=5, sticky='e')
        search_phone = tk.StringVar()
        tk.Entry(search_window, textvariable=search_phone, width=25).grid(
            row=2, column=1, padx=5, pady=5)

        def perform_search():
            query = "SELECT national_id, name, age, gender, sample_type, receive_date FROM patients WHERE 1=1"
            params = []

            if search_name.get():
                query += " AND name LIKE ?"
                params.append(f"%{search_name.get()}%")

            if search_national_id.get():
                query += " AND national_id = ?"
                params.append(int(search_national_id.get()))

            if search_phone.get():
                query += " AND phone LIKE ?"
                params.append(f"%{search_phone.get()}%")

            try:
                self.cursor.execute(query, params)
                results = self.cursor.fetchall()

                # عرض النتائج
                results_text = tk.Text(search_window, height=10, width=50)
                results_text.grid(row=4, column=0, columnspan=2, padx=10, pady=10)

                if results:
                    results_text.insert('1.0', "نتائج البحث:\n\n")
                    for result in results:
                        results_text.insert(tk.END, f"الرقم الوطني: {result[0]}\n")
                        results_text.insert(tk.END, f"الاسم: {result[1]}\n")
                        results_text.insert(tk.END, f"العمر: {result[2]}\n")
                        results_text.insert(tk.END, f"الجنس: {result[3]}\n")
                        results_text.insert(tk.END, f"نوع العينة: {result[4]}\n")
                        results_text.insert(tk.END, f"تاريخ الاستلام: {result[5]}\n")
                        results_text.insert(tk.END, "-" * 40 + "\n")
                else:
                    results_text.insert('1.0', "لم يتم العثور على نتائج")

                results_text.config(state=tk.DISABLED)

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

        tk.Button(search_window, text="بحث", font=('Arial', 10, 'bold'),
                 bg='#3498db', fg='white', relief='raised', bd=3,
                 command=perform_search).grid(row=3, column=0, columnspan=2, pady=10)

    def import_excel(self):
        """استيراد بيانات من Excel"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Excel",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            # قراءة ملف Excel
            df = pd.read_excel(file_path)

            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['الاسم', 'العمر', 'الجنس', 'العنوان', 'نوع العينة',
                              'رقم الهاتف', 'جهة الإرسال', 'تاريخ سحب العينة']

            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                messagebox.showerror("خطأ", f"الأعمدة التالية مفقودة: {', '.join(missing_columns)}")
                return

            # استيراد البيانات
            imported_count = 0
            errors = []

            for index, row in df.iterrows():
                try:
                    self.cursor.execute('''
                        INSERT INTO patients (name, age, gender, address, sample_type, phone,
                                            sender_org, sample_date, passport_no, receipt_no, tests)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        row['الاسم'],
                        int(row['العمر']),
                        row['الجنس'],
                        row['العنوان'],
                        row['نوع العينة'],
                        str(row['رقم الهاتف']),
                        row['جهة الإرسال'],
                        row['تاريخ سحب العينة'],
                        row.get('رقم الجواز', ''),
                        row.get('رقم الوصل', ''),
                        row.get('التحاليل', '')
                    ))

                    imported_count += 1

                    # طباعة استيكر لكل عينة
                    self.print_sticker()

                except Exception as e:
                    errors.append(f"الصف {index + 1}: {str(e)}")

            self.conn.commit()

            # تحديث الجدول
            self.refresh_patients_table()

            # عرض النتائج
            message = f"تم استيراد {imported_count} عينة بنجاح"
            if errors:
                message += f"\n\nأخطاء ({len(errors)}):\n" + "\n".join(errors[:5])
                if len(errors) > 5:
                    message += f"\n... و {len(errors) - 5} أخطاء أخرى"

            messagebox.showinfo("نتائج الاستيراد", message)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء استيراد الملف: {str(e)}")
    
    def show_work_tab(self):
        """عرض تبويب العمل"""
        self.clear_content_frame()

        # عنوان التبويب
        title = tk.Label(self.content_frame, text="العمل",
                        font=('Arial', 16, 'bold'), bg='#ecf0f1')
        title.pack(pady=10)

        # إطار البحث
        search_frame = tk.Frame(self.content_frame, bg='#ecf0f1')
        search_frame.pack(fill=tk.X, padx=20, pady=10)

        # تحديد فترة البحث مع تحسينات
        tk.Label(search_frame, text="من تاريخ:", font=('Arial', 10, 'bold'),
                bg='#f8f9fa', fg='#2c3e50').grid(row=0, column=0, padx=5, pady=5, sticky='e')

        start_date_frame = tk.Frame(search_frame, bg='#f8f9fa')
        start_date_frame.grid(row=0, column=1, padx=5, pady=5)

        self.work_start_date = tk.StringVar()
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        self.work_start_date.set(current_date)

        start_entry = tk.Entry(start_date_frame, textvariable=self.work_start_date,
                              width=12, relief='solid', bd=1)
        start_entry.pack(side=tk.LEFT)
        tk.Button(start_date_frame, text="📅", font=('Arial', 8), bg='#3498db', fg='white',
                 relief='flat', bd=0, cursor='hand2',
                 command=lambda: self.work_start_date.set(current_date)).pack(side=tk.LEFT, padx=(2, 0))

        tk.Label(search_frame, text="إلى تاريخ:", font=('Arial', 10, 'bold'),
                bg='#f8f9fa', fg='#2c3e50').grid(row=0, column=2, padx=5, pady=5, sticky='e')

        end_date_frame = tk.Frame(search_frame, bg='#f8f9fa')
        end_date_frame.grid(row=0, column=3, padx=5, pady=5)

        self.work_end_date = tk.StringVar()
        self.work_end_date.set(current_date)

        end_entry = tk.Entry(end_date_frame, textvariable=self.work_end_date,
                            width=12, relief='solid', bd=1)
        end_entry.pack(side=tk.LEFT)
        tk.Button(end_date_frame, text="📅", font=('Arial', 8), bg='#3498db', fg='white',
                 relief='flat', bd=0, cursor='hand2',
                 command=lambda: self.work_end_date.set(current_date)).pack(side=tk.LEFT, padx=(2, 0))

        tk.Button(search_frame, text="بحث", font=('Arial', 10, 'bold'),
                 bg='#3498db', fg='white', relief='raised', bd=3,
                 command=self.search_work_samples).grid(row=0, column=4, padx=10, pady=5)

        # معلومات الوجبة
        batch_frame = tk.Frame(self.content_frame, bg='#ecf0f1')
        batch_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(batch_frame, text="رقم الوجبة:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5)
        self.batch_no_var = tk.StringVar()
        tk.Label(batch_frame, textvariable=self.batch_no_var, font=('Arial', 10),
                bg='white', relief='sunken', width=10).grid(row=0, column=1, padx=5, pady=5)

        tk.Label(batch_frame, text="الفني المسؤول:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=2, padx=5, pady=5)
        self.work_technician = tk.StringVar()
        tech_combo = ttk.Combobox(batch_frame, textvariable=self.work_technician, width=20)
        tech_combo.grid(row=0, column=3, padx=5, pady=5)
        self.update_technicians_combo(tech_combo)

        tk.Button(batch_frame, text="إنشاء وجبة جديدة", font=('Arial', 10, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=3,
                 command=self.create_new_batch).grid(row=0, column=4, padx=10, pady=5)

        tk.Button(batch_frame, text="إرسال إلى النتائج", font=('Arial', 10, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=3,
                 command=self.send_to_results).grid(row=0, column=5, padx=10, pady=5)

        # جدول العينات
        self.create_work_samples_table()

        # تحديث رقم الوجبة التلقائي
        self.update_batch_number()

    def update_technicians_combo(self, combo):
        """تحديث قائمة الفنيين"""
        self.cursor.execute("SELECT value FROM settings WHERE category = 'technicians'")
        technicians = [row[0] for row in self.cursor.fetchall()]
        combo['values'] = technicians

    def search_work_samples(self):
        """البحث عن العينات للعمل"""
        start_date = self.work_start_date.get()
        end_date = self.work_end_date.get()

        if not start_date or not end_date:
            messagebox.showerror("خطأ", "يرجى تحديد فترة البحث")
            return

        # جلب العينات من قاعدة البيانات
        self.cursor.execute('''
            SELECT national_id, name, sample_type, receive_date, tests
            FROM patients
            WHERE receive_date BETWEEN ? AND ?
            ORDER BY receive_date
        ''', (start_date, end_date))

        samples = self.cursor.fetchall()

        # تحديث جدول العينات
        self.refresh_work_samples_table(samples)

    def create_work_samples_table(self):
        """إنشاء جدول عينات العمل"""
        table_frame = tk.Frame(self.content_frame, bg='#ecf0f1')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        columns = ('الرقم الوطني', 'الاسم', 'نوع العينة', 'تاريخ الاستلام', 'التحاليل')
        self.work_samples_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.work_samples_tree.heading(col, text=col)
            self.work_samples_tree.column(col, width=150, anchor='center')

        scrollbar_work = ttk.Scrollbar(table_frame, orient=tk.VERTICAL,
                                      command=self.work_samples_tree.yview)
        self.work_samples_tree.configure(yscrollcommand=scrollbar_work.set)

        self.work_samples_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_work.pack(side=tk.RIGHT, fill=tk.Y)

    def refresh_work_samples_table(self, samples=None):
        """تحديث جدول عينات العمل"""
        for item in self.work_samples_tree.get_children():
            self.work_samples_tree.delete(item)

        if samples:
            for sample in samples:
                self.work_samples_tree.insert('', tk.END, values=sample)

    def update_batch_number(self):
        """تحديث رقم الوجبة التلقائي"""
        self.cursor.execute("SELECT MAX(batch_no) FROM batches")
        result = self.cursor.fetchone()
        next_batch = (result[0] or 0) + 1
        self.batch_no_var.set(str(next_batch))

    def create_new_batch(self):
        """إنشاء وجبة جديدة"""
        if not self.work_technician.get():
            messagebox.showerror("خطأ", "يرجى اختيار الفني المسؤول")
            return

        try:
            batch_no = int(self.batch_no_var.get())
            self.cursor.execute('''
                INSERT INTO batches (batch_no, start_date, end_date, technician)
                VALUES (?, ?, ?, ?)
            ''', (batch_no, self.work_start_date.get(), self.work_end_date.get(),
                  self.work_technician.get()))

            self.conn.commit()
            messagebox.showinfo("نجح", f"تم إنشاء الوجبة رقم {batch_no} بنجاح")
            self.update_batch_number()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الوجبة: {str(e)}")

    def send_to_results(self):
        """إرسال الوجبة إلى النتائج"""
        batch_no = self.batch_no_var.get()
        if not batch_no:
            messagebox.showerror("خطأ", "لا توجد وجبة محددة")
            return

        try:
            self.cursor.execute('''
                UPDATE batches SET status = 'sent_to_results'
                WHERE batch_no = ?
            ''', (int(batch_no),))

            self.conn.commit()
            messagebox.showinfo("نجح", f"تم إرسال الوجبة رقم {batch_no} إلى النتائج")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الإرسال: {str(e)}")
    
    def show_results_tab(self):
        """عرض تبويب النتائج"""
        self.clear_content_frame()

        # عنوان التبويب مع تصميم حديث
        title_frame = tk.Frame(self.content_frame, bg='#2c3e50', height=60)
        title_frame.pack(fill=tk.X, padx=0, pady=0)
        title_frame.pack_propagate(False)

        title = tk.Label(title_frame, text="📊 النتائج",
                        font=('Arial', 18, 'bold'), bg='#2c3e50', fg='white')
        title.pack(expand=True)

        # إطار رئيسي
        main_container = tk.Frame(self.content_frame, bg='#f8f9fa')
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # إطار البحث بالوجبة
        search_container = tk.LabelFrame(main_container, text="البحث عن الوجبة",
                                       font=('Arial', 12, 'bold'), bg='white',
                                       fg='#2c3e50', relief='ridge', bd=2)
        search_container.pack(fill=tk.X, pady=(0, 15))

        search_frame = tk.Frame(search_container, bg='white')
        search_frame.pack(fill=tk.X, padx=15, pady=10)

        tk.Label(search_frame, text="رقم الوجبة:", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=0, padx=5, pady=5, sticky='e')

        self.results_batch_no = tk.StringVar()
        batch_entry = tk.Entry(search_frame, textvariable=self.results_batch_no,
                              font=('Arial', 10), width=15, relief='solid', bd=1)
        batch_entry.grid(row=0, column=1, padx=5, pady=5)

        tk.Button(search_frame, text="🔍 عرض الوجبة", font=('Arial', 10, 'bold'),
                 bg='#3498db', fg='white', relief='raised', bd=3, cursor='hand2',
                 command=self.load_batch_results).grid(row=0, column=2, padx=10, pady=5)

        tk.Button(search_frame, text="📋 عرض جميع الوجبات", font=('Arial', 10, 'bold'),
                 bg='#9b59b6', fg='white', relief='raised', bd=3, cursor='hand2',
                 command=self.show_all_batches).grid(row=0, column=3, padx=5, pady=5)

        # جدول العينات للنتائج
        self.create_results_table(main_container)

        # إطار إدخال النتائج
        input_container = tk.LabelFrame(main_container, text="إدخال النتيجة",
                                      font=('Arial', 12, 'bold'), bg='white',
                                      fg='#2c3e50', relief='ridge', bd=2)
        input_container.pack(fill=tk.X, pady=(15, 0))

        input_frame = tk.Frame(input_container, bg='white')
        input_frame.pack(fill=tk.X, padx=15, pady=10)

        tk.Label(input_frame, text="النتيجة:", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=0, padx=5, pady=5, sticky='e')

        self.result_value = tk.StringVar()
        result_combo = ttk.Combobox(input_frame, textvariable=self.result_value,
                                   values=['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND'],
                                   width=15, state='readonly')
        result_combo.grid(row=0, column=1, padx=5, pady=5)

        tk.Button(input_frame, text="💾 حفظ النتيجة", font=('Arial', 10, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=3, cursor='hand2',
                 command=self.save_result).grid(row=0, column=2, padx=10, pady=5)

        # معلومات إضافية
        info_label = tk.Label(input_frame,
                             text="💡 اختر تحليل من الجدول أعلاه ثم اختر النتيجة واضغط حفظ",
                             font=('Arial', 9), bg='white', fg='#7f8c8d')
        info_label.grid(row=1, column=0, columnspan=3, pady=(10, 0))

    def create_results_table(self, parent):
        """إنشاء جدول النتائج المحسن"""
        # إطار الجدول مع عنوان
        table_container = tk.LabelFrame(parent, text="نتائج التحاليل",
                                       font=('Arial', 12, 'bold'), bg='white',
                                       fg='#2c3e50', relief='ridge', bd=2)
        table_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        columns = ('الرقم الوطني', 'الاسم', 'نوع العينة', 'التحليل', 'النتيجة', 'تاريخ النتيجة')
        self.results_tree = ttk.Treeview(table_container, columns=columns, show='headings', height=12)

        # تعيين عناوين الأعمدة مع تحسين العرض
        column_widths = {'الرقم الوطني': 100, 'الاسم': 150, 'نوع العينة': 120,
                        'التحليل': 120, 'النتيجة': 100, 'تاريخ النتيجة': 120}

        for col in columns:
            self.results_tree.heading(col, text=col, anchor='center')
            self.results_tree.column(col, width=column_widths.get(col, 100),
                                    anchor='center', minwidth=50)

        # شريط التمرير
        scrollbar_results = ttk.Scrollbar(table_container, orient=tk.VERTICAL,
                                         command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar_results.set)

        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar_results.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

        # إضافة ألوان متناوبة للصفوف
        self.results_tree.tag_configure('oddrow', background='#f8f9fa')
        self.results_tree.tag_configure('evenrow', background='white')
        self.results_tree.tag_configure('no_result', background='#fff3cd', foreground='#856404')
        self.results_tree.tag_configure('positive', background='#f8d7da', foreground='#721c24')
        self.results_tree.tag_configure('negative', background='#d4edda', foreground='#155724')

        # ربط حدث التحديد
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_select)

    def show_all_batches(self):
        """عرض جميع الوجبات المتاحة"""
        try:
            self.cursor.execute("SELECT batch_no, start_date, end_date, technician FROM batches ORDER BY batch_no DESC")
            batches = self.cursor.fetchall()

            if not batches:
                messagebox.showinfo("معلومات", "لا توجد وجبات متاحة. يرجى إنشاء وجبة من تبويب العمل أولاً.")
                return

            # إنشاء نافذة عرض الوجبات
            batches_window = tk.Toplevel(self.root)
            batches_window.title("الوجبات المتاحة")
            batches_window.geometry("600x400")
            batches_window.configure(bg='white')

            tk.Label(batches_window, text="الوجبات المتاحة",
                    font=('Arial', 14, 'bold'), bg='white').pack(pady=10)

            # جدول الوجبات
            columns = ('رقم الوجبة', 'تاريخ البداية', 'تاريخ النهاية', 'الفني')
            batches_tree = ttk.Treeview(batches_window, columns=columns, show='headings', height=15)

            for col in columns:
                batches_tree.heading(col, text=col)
                batches_tree.column(col, width=120, anchor='center')

            for batch in batches:
                batches_tree.insert('', tk.END, values=batch)

            batches_tree.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            # زر اختيار الوجبة
            def select_batch():
                selection = batches_tree.selection()
                if selection:
                    item = batches_tree.item(selection[0])
                    batch_no = item['values'][0]
                    self.results_batch_no.set(str(batch_no))
                    batches_window.destroy()
                    self.load_batch_results()
                else:
                    messagebox.showerror("خطأ", "يرجى اختيار وجبة")

            tk.Button(batches_window, text="اختيار الوجبة", font=('Arial', 10, 'bold'),
                     bg='#27ae60', fg='white', command=select_batch).pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عرض الوجبات: {str(e)}")

    def load_batch_results(self):
        """تحميل نتائج الوجبة مع دعم التحاليل المتعددة"""
        batch_no = self.results_batch_no.get()
        if not batch_no:
            messagebox.showerror("خطأ", "يرجى إدخال رقم الوجبة أو اختيارها من قائمة الوجبات")
            return

        try:
            # مسح الجدول أولاً
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            # التحقق من وجود الوجبة أولاً
            self.cursor.execute("SELECT id, start_date, end_date FROM batches WHERE batch_no = ?", (int(batch_no),))
            batch_info = self.cursor.fetchone()

            if not batch_info:
                messagebox.showwarning("تحذير", f"الوجبة رقم {batch_no} غير موجودة. يرجى إنشاؤها من تبويب العمل أولاً.")
                return

            batch_id, start_date, end_date = batch_info

            # جلب المرضى في الوجبة حسب التاريخ
            if start_date and end_date:
                self.cursor.execute('''
                    SELECT id, national_id, name, sample_type, tests
                    FROM patients
                    WHERE receive_date BETWEEN ? AND ?
                    ORDER BY national_id
                ''', (start_date, end_date))
            else:
                # إذا لم تكن التواريخ محددة، جلب جميع المرضى
                self.cursor.execute('''
                    SELECT id, national_id, name, sample_type, tests
                    FROM patients
                    ORDER BY national_id DESC
                    LIMIT 50
                ''')

            patients = self.cursor.fetchall()

            if not patients:
                messagebox.showwarning("تحذير", f"لا توجد عينات في الوجبة رقم {batch_no}")
                return

            row_count = 0
            for patient in patients:
                patient_id, national_id, name, sample_type, tests_str = patient

                if tests_str:
                    # تقسيم التحاليل
                    tests = [test.strip() for test in tests_str.split(',') if test.strip()]

                    for test in tests:
                        # البحث عن النتيجة الموجودة لهذا التحليل
                        self.cursor.execute('''
                            SELECT result, result_date FROM results
                            WHERE patient_id = ? AND test_name = ? AND batch_id = ?
                        ''', (patient_id, test, batch_id))

                        result_data = self.cursor.fetchone()

                        if result_data:
                            result, result_date = result_data
                        else:
                            result = 'لم يتم إدخال النتيجة'
                            result_date = ''

                        # تحديد اللون حسب النتيجة
                        if result == 'لم يتم إدخال النتيجة':
                            tag = 'no_result'
                        elif result.lower() == 'positive':
                            tag = 'positive'
                        elif result.lower() == 'negative':
                            tag = 'negative'
                        else:
                            tag = 'evenrow' if row_count % 2 == 0 else 'oddrow'

                        # إدراج الصف
                        item_id = self.results_tree.insert('', tk.END,
                                                          values=(national_id, name, sample_type, test, result, result_date),
                                                          tags=(tag, str(patient_id), test, str(batch_id)))
                        row_count += 1
                else:
                    # إذا لم تكن هناك تحاليل محددة
                    self.results_tree.insert('', tk.END,
                                            values=(national_id, name, sample_type, 'غير محدد', 'لم يتم إدخال النتيجة', ''),
                                            tags=('no_result', str(patient_id), 'غير محدد', str(batch_id)))
                    row_count += 1

            messagebox.showinfo("نجح", f"تم تحميل {row_count} تحليل من الوجبة رقم {batch_no}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل النتائج: {str(e)}")

    def on_result_select(self, event):
        """عند تحديد تحليل في جدول النتائج"""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            current_result = item['values'][4]  # النتيجة في العمود الخامس
            if current_result != 'لم يتم إدخال النتيجة':
                self.result_value.set(current_result)
            else:
                self.result_value.set("")

    def save_result(self):
        """حفظ النتيجة للتحليل المحدد"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showerror("خطأ", "يرجى اختيار تحليل")
            return

        if not self.result_value.get():
            messagebox.showerror("خطأ", "يرجى اختيار النتيجة")
            return

        try:
            # الحصول على معلومات التحليل المحدد
            item = self.results_tree.item(selection[0])
            tags = item['tags']

            if len(tags) < 4:
                messagebox.showerror("خطأ", "خطأ في بيانات التحليل")
                return

            patient_id = int(tags[1])  # معرف المريض
            test_name = tags[2]        # اسم التحليل
            batch_id = int(tags[3])    # معرف الوجبة

            # إدراج أو تحديث النتيجة للتحليل المحدد
            self.cursor.execute('''
                INSERT OR REPLACE INTO results (patient_id, batch_id, test_name, result, result_date)
                VALUES (?, ?, ?, ?, DATE('now'))
            ''', (patient_id, batch_id, test_name, self.result_value.get()))

            self.conn.commit()

            # تحديث الجدول
            self.load_batch_results()

            # مسح النتيجة المحددة
            self.result_value.set("")

            messagebox.showinfo("نجح", f"تم حفظ نتيجة تحليل '{test_name}' بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ النتيجة: {str(e)}")
    
    def show_reports_tab(self):
        """عرض تبويب التقارير والإحصائيات"""
        self.clear_content_frame()

        # عنوان التبويب
        title = tk.Label(self.content_frame, text="التقارير والإحصائيات",
                        font=('Arial', 16, 'bold'), bg='#ecf0f1')
        title.pack(pady=10)

        # إطار الفلترة
        filter_frame = tk.LabelFrame(self.content_frame, text="فلترة البيانات",
                                    font=('Arial', 12, 'bold'), bg='#ecf0f1')
        filter_frame.pack(fill=tk.X, padx=20, pady=10)

        # الفترة الزمنية مع تحسينات
        tk.Label(filter_frame, text="من تاريخ:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1', fg='#2c3e50').grid(row=0, column=0, padx=5, pady=5, sticky='e')

        start_frame = tk.Frame(filter_frame, bg='#ecf0f1')
        start_frame.grid(row=0, column=1, padx=5, pady=5)

        self.report_start_date = tk.StringVar()
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")

        start_entry = tk.Entry(start_frame, textvariable=self.report_start_date,
                              width=12, relief='solid', bd=1)
        start_entry.pack(side=tk.LEFT)
        tk.Button(start_frame, text="📅", font=('Arial', 8), bg='#3498db', fg='white',
                 relief='flat', bd=0, cursor='hand2',
                 command=lambda: self.report_start_date.set(current_date)).pack(side=tk.LEFT, padx=(2, 0))

        tk.Label(filter_frame, text="إلى تاريخ:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1', fg='#2c3e50').grid(row=0, column=2, padx=5, pady=5, sticky='e')

        end_frame = tk.Frame(filter_frame, bg='#ecf0f1')
        end_frame.grid(row=0, column=3, padx=5, pady=5)

        self.report_end_date = tk.StringVar()

        end_entry = tk.Entry(end_frame, textvariable=self.report_end_date,
                            width=12, relief='solid', bd=1)
        end_entry.pack(side=tk.LEFT)
        tk.Button(end_frame, text="📅", font=('Arial', 8), bg='#3498db', fg='white',
                 relief='flat', bd=0, cursor='hand2',
                 command=lambda: self.report_end_date.set(current_date)).pack(side=tk.LEFT, padx=(2, 0))

        # الاسم
        tk.Label(filter_frame, text="الاسم:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=1, column=0, padx=5, pady=5, sticky='e')
        self.report_name = tk.StringVar()
        tk.Entry(filter_frame, textvariable=self.report_name, width=20).grid(
            row=1, column=1, padx=5, pady=5)

        # نوع العينة
        tk.Label(filter_frame, text="نوع العينة:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=1, column=2, padx=5, pady=5, sticky='e')
        self.report_sample_type = tk.StringVar()
        sample_combo = ttk.Combobox(filter_frame, textvariable=self.report_sample_type, width=15)
        sample_combo.grid(row=1, column=3, padx=5, pady=5)
        self.update_combobox_values(sample_combo, "sample_type")

        # الجنس
        tk.Label(filter_frame, text="الجنس:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=2, column=0, padx=5, pady=5, sticky='e')
        self.report_gender = tk.StringVar()
        gender_combo = ttk.Combobox(filter_frame, textvariable=self.report_gender,
                                   values=['', 'M', 'F'], width=15)
        gender_combo.grid(row=2, column=1, padx=5, pady=5)

        # جهة الإرسال
        tk.Label(filter_frame, text="جهة الإرسال:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=2, column=2, padx=5, pady=5, sticky='e')
        self.report_sender = tk.StringVar()
        sender_combo = ttk.Combobox(filter_frame, textvariable=self.report_sender, width=15)
        sender_combo.grid(row=2, column=3, padx=5, pady=5)
        self.update_combobox_values(sender_combo, "sender_org")

        # أزرار العمليات
        buttons_frame = tk.Frame(filter_frame, bg='#ecf0f1')
        buttons_frame.grid(row=3, column=0, columnspan=4, pady=10)

        button_style = {
            'font': ('Arial', 10, 'bold'),
            'relief': 'raised',
            'bd': 3,
            'width': 12,
            'height': 2
        }

        tk.Button(buttons_frame, text="بحث", bg='#3498db', fg='white',
                 command=self.search_reports, **button_style).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="معاينة التقرير", bg='#9b59b6', fg='white',
                 command=self.preview_report, **button_style).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="طباعة التقرير", bg='#27ae60', fg='white',
                 command=self.print_report, **button_style).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="تصدير Excel", bg='#f39c12', fg='white',
                 command=self.export_excel, **button_style).pack(side=tk.LEFT, padx=5)

        # جدول النتائج
        self.create_reports_table()

    def create_reports_table(self):
        """إنشاء جدول التقارير مع الترتيب الصحيح للأعمدة"""
        table_frame = tk.Frame(self.content_frame, bg='#ecf0f1')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # ترتيب الأعمدة الصحيح: الرقم الوطني، الاسم، العمر، الجنس، نوع العينة، جهة الإرسال، التحليل، النتيجة، تاريخ النتيجة
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة',
                  'جهة الإرسال', 'التحليل', 'النتيجة', 'تاريخ النتيجة')
        self.reports_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعيين عرض مناسب لكل عمود
        column_widths = {
            'الرقم الوطني': 100,
            'الاسم': 150,
            'العمر': 60,
            'الجنس': 60,
            'نوع العينة': 120,
            'جهة الإرسال': 150,
            'التحليل': 120,
            'النتيجة': 100,
            'تاريخ النتيجة': 120
        }

        for col in columns:
            self.reports_tree.heading(col, text=col, anchor='center')
            self.reports_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # شريط التمرير العمودي والأفقي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL,
                                   command=self.reports_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL,
                                   command=self.reports_tree.xview)

        self.reports_tree.configure(yscrollcommand=v_scrollbar.set,
                                   xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.reports_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # تكوين التوسع
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

    def search_reports(self):
        """البحث في التقارير مع دعم التحاليل المتعددة"""
        # بناء استعلام SQL ديناميكي للتحاليل المتعددة
        # ترتيب الأعمدة: الرقم الوطني، الاسم، العمر، الجنس، نوع العينة، جهة الإرسال، التحليل، النتيجة، تاريخ النتيجة
        query = '''
            SELECT p.national_id, p.name, p.age, p.gender, p.sample_type,
                   p.sender_org,
                   COALESCE(r.test_name, 'غير محدد') as test_name,
                   COALESCE(r.result, 'لا توجد نتيجة') as result,
                   COALESCE(r.result_date, '') as result_date
            FROM patients p
            LEFT JOIN results r ON p.id = r.patient_id
            WHERE 1=1
        '''
        params = []

        # إضافة شروط البحث
        if self.report_start_date.get():
            query += " AND p.receive_date >= ?"
            params.append(self.report_start_date.get())

        if self.report_end_date.get():
            query += " AND p.receive_date <= ?"
            params.append(self.report_end_date.get())

        if self.report_name.get():
            query += " AND p.name LIKE ?"
            params.append(f"%{self.report_name.get()}%")

        if self.report_sample_type.get():
            query += " AND p.sample_type = ?"
            params.append(self.report_sample_type.get())

        if self.report_gender.get():
            query += " AND p.gender = ?"
            params.append(self.report_gender.get())

        if self.report_sender.get():
            query += " AND p.sender_org = ?"
            params.append(self.report_sender.get())

        query += " ORDER BY p.receive_date DESC, p.national_id, r.test_name"

        try:
            self.cursor.execute(query, params)
            results = self.cursor.fetchall()

            # تحديث الجدول
            for item in self.reports_tree.get_children():
                self.reports_tree.delete(item)

            for result in results:
                self.reports_tree.insert('', tk.END, values=result)

            messagebox.showinfo("نتائج البحث", f"تم العثور على {len(results)} نتيجة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def preview_report(self):
        """معاينة التقرير"""
        # التحقق من وجود بيانات
        if not self.reports_tree.get_children():
            messagebox.showwarning("تحذير", "لا توجد بيانات لمعاينة التقرير")
            return

        # إنشاء نافذة المعاينة
        preview_window = tk.Toplevel(self.root)
        preview_window.title("معاينة التقرير")
        preview_window.geometry("800x600")
        preview_window.configure(bg='white')

        # إطار المعاينة
        preview_frame = tk.Frame(preview_window, bg='white')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان التقرير
        header_frame = tk.Frame(preview_frame, bg='white')
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # العنوان العربي
        arabic_header = tk.Label(header_frame,
                               text="جمهورية العراق\nوزارة الصحة\nقسم الصحة العامة\nmختبر الصحة العامة\nوحدة الفايروسات",
                               font=('Arial', 12, 'bold'), bg='white', fg='#2c3e50',
                               justify=tk.CENTER)
        arabic_header.pack(side=tk.RIGHT, padx=20)

        # العنوان الإنجليزي
        english_header = tk.Label(header_frame,
                                text="Republic of Iraq\nMinistry of Health\nPublic Health Department\nPublic Health Laboratory\nVirus Unit",
                                font=('Arial', 12, 'bold'), bg='white', fg='#2c3e50',
                                justify=tk.CENTER)
        english_header.pack(side=tk.LEFT, padx=20)

        # عنوان التقرير
        report_title = tk.Label(preview_frame, text="تقرير نتائج المختبر",
                              font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        report_title.pack(pady=10)

        # معلومات التقرير
        info_frame = tk.Frame(preview_frame, bg='white')
        info_frame.pack(fill=tk.X, pady=10)

        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        tk.Label(info_frame, text=f"تاريخ التقرير: {current_date}",
                font=('Arial', 10), bg='white').pack(anchor='w')

        # جدول البيانات
        data_frame = tk.Frame(preview_frame, bg='white')
        data_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # إنشاء جدول للمعاينة مع الترتيب الصحيح
        preview_columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'جهة الإرسال', 'التحليل', 'النتيجة')
        preview_tree = ttk.Treeview(data_frame, columns=preview_columns,
                                   show='headings', height=15)

        for col in preview_columns:
            preview_tree.heading(col, text=col)
            preview_tree.column(col, width=90, anchor='center')

        # نسخ البيانات من الجدول الأصلي
        for item in self.reports_tree.get_children():
            values = self.reports_tree.item(item)['values']
            preview_tree.insert('', tk.END, values=values[:8])  # أول 8 أعمدة

        preview_tree.pack(fill=tk.BOTH, expand=True)

        # أزرار المعاينة
        buttons_frame = tk.Frame(preview_window, bg='white')
        buttons_frame.pack(fill=tk.X, pady=10)

        tk.Button(buttons_frame, text="طباعة", font=('Arial', 10, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=3,
                 command=lambda: self.print_report_data()).pack(side=tk.LEFT, padx=10)

        tk.Button(buttons_frame, text="إغلاق", font=('Arial', 10, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=3,
                 command=preview_window.destroy).pack(side=tk.RIGHT, padx=10)

    def print_report(self):
        """طباعة التقرير"""
        if not self.reports_tree.get_children():
            messagebox.showwarning("تحذير", "لا توجد بيانات للطباعة")
            return

        self.print_report_data()

    def print_report_data(self):
        """طباعة بيانات التقرير مع شعار وزارة الصحة"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            from reportlab.lib.units import inch
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            from reportlab.lib.utils import ImageReader

            # إنشاء ملف PDF
            filename = f"lab_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            c = canvas.Canvas(filename, pagesize=A4)
            width, height = A4

            # إضافة شعار وزارة الصحة في أعلى المنتصف
            logo_path = 'ministry_logo.png'
            if os.path.exists(logo_path):
                try:
                    logo = ImageReader(logo_path)
                    logo_width = 80
                    logo_height = 80
                    logo_x = (width - logo_width) / 2
                    logo_y = height - 100
                    c.drawImage(logo, logo_x, logo_y, width=logo_width, height=logo_height)
                    header_start_y = height - 120
                except:
                    header_start_y = height - 50
            else:
                header_start_y = height - 50

            # العناوين الرسمية
            c.setFont("Helvetica-Bold", 12)

            # العنوان العربي (يمين)
            arabic_text = [
                "جمهورية العراق",
                "وزارة الصحة",
                "قسم الصحة العامة",
                "مختبر الصحة العامة",
                "وحدة الفايروسات"
            ]

            y_pos = header_start_y
            for line in arabic_text:
                c.drawRightString(width - 50, y_pos, line)
                y_pos -= 15

            # العنوان الإنجليزي (يسار)
            english_text = [
                "Republic of Iraq",
                "Ministry of Health",
                "Public Health Department",
                "Public Health Laboratory",
                "Virus Unit"
            ]

            y_pos = header_start_y
            for line in english_text:
                c.drawString(50, y_pos, line)
                y_pos -= 15

            # عنوان التقرير
            c.setFont("Helvetica-Bold", 16)
            report_title_y = header_start_y - 100
            c.drawCentredText(width/2, report_title_y, "Laboratory Results Report")
            c.drawCentredText(width/2, report_title_y - 20, "تقرير نتائج المختبر")

            # التاريخ
            c.setFont("Helvetica", 10)
            current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            date_y = report_title_y - 50
            c.drawString(50, date_y, f"Report Date: {current_date}")
            c.drawRightString(width - 50, date_y, f"تاريخ التقرير: {current_date}")

            # رؤوس الجدول مع الترتيب الصحيح
            table_start_y = date_y - 40
            c.setFont("Helvetica-Bold", 8)
            headers = ["ID", "Name", "Age", "Gender", "Sample", "Sender", "Test", "Result"]
            x_positions = [50, 90, 150, 180, 210, 260, 320, 370]

            for i, header in enumerate(headers):
                c.drawString(x_positions[i], table_start_y, header)

            # خط تحت الرؤوس
            c.line(50, table_start_y - 5, 420, table_start_y - 5)

            # البيانات
            c.setFont("Helvetica", 7)
            y_position = table_start_y - 20

            for item in self.reports_tree.get_children():
                if y_position < 100:  # صفحة جديدة
                    c.showPage()
                    y_position = height - 50

                    # إعادة رسم رؤوس الجدول في الصفحة الجديدة
                    c.setFont("Helvetica-Bold", 8)
                    for i, header in enumerate(headers):
                        c.drawString(x_positions[i], y_position, header)
                    c.line(50, y_position - 5, 420, y_position - 5)
                    c.setFont("Helvetica", 7)
                    y_position -= 20

                values = self.reports_tree.item(item)['values']
                # ترتيب البيانات: الرقم الوطني، الاسم، العمر، الجنس، نوع العينة، جهة الإرسال، التحليل، النتيجة
                for i, value in enumerate(values[:8]):  # أول 8 أعمدة
                    text = str(value)[:12]  # تقصير النص إذا كان طويلاً
                    c.drawString(x_positions[i], y_position, text)

                y_position -= 12

            # معلومات المختبر في الأسفل
            c.setFont("Helvetica", 8)
            footer_y = 50
            c.drawString(50, footer_y, "Address: Dhi Qar / Nasiriyah / Sumer / Next to Al-Sufara Al-Arba'a Husseiniya")
            c.drawRightString(width - 50, footer_y, "العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة")

            # خط فاصل
            c.line(50, footer_y + 15, width - 50, footer_y + 15)

            c.save()

            # فتح الملف
            import os
            os.startfile(filename)

            messagebox.showinfo("نجح", f"تم إنشاء التقرير بنجاح مع شعار الوزارة: {filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")

    def export_excel(self):
        """تصدير إلى Excel"""
        try:
            # جمع البيانات من الجدول
            data = []
            for item in self.reports_tree.get_children():
                values = self.reports_tree.item(item)['values']
                data.append(values)

            if not data:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            # إنشاء DataFrame
            columns = ['الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة',
                      'جهة الإرسال', 'النتيجة', 'تاريخ النتيجة']
            df = pd.DataFrame(data, columns=columns)

            # حفظ الملف
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )

            if filename:
                df.to_excel(filename, index=False, engine='openpyxl')
                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى {filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")
    
    def show_settings_tab(self):
        """عرض تبويب الإعدادات"""
        self.clear_content_frame()

        # عنوان التبويب
        title = tk.Label(self.content_frame, text="الإعدادات",
                        font=('Arial', 16, 'bold'), bg='#ecf0f1')
        title.pack(pady=10)

        # إنشاء دفتر التبويبات للإعدادات
        settings_notebook = ttk.Notebook(self.content_frame)
        settings_notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # تبويب أنواع العينات
        self.create_sample_types_tab(settings_notebook)

        # تبويب جهات الإرسال
        self.create_sender_orgs_tab(settings_notebook)

        # تبويب التحاليل
        self.create_tests_tab(settings_notebook)

        # تبويب الفنيين
        self.create_technicians_tab(settings_notebook)

        # تبويب إعدادات التقرير
        self.create_report_settings_tab(settings_notebook)

    def create_sample_types_tab(self, parent):
        """إنشاء تبويب أنواع العينات"""
        frame = tk.Frame(parent, bg='#ecf0f1')
        parent.add(frame, text="أنواع العينات")

        # إطار الإدخال
        input_frame = tk.Frame(frame, bg='#ecf0f1')
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(input_frame, text="نوع العينة:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5)
        self.sample_type_entry = tk.StringVar()
        tk.Entry(input_frame, textvariable=self.sample_type_entry, width=30).grid(
            row=0, column=1, padx=5, pady=5)

        # أزرار العمليات
        buttons_frame = tk.Frame(input_frame, bg='#ecf0f1')
        buttons_frame.grid(row=0, column=2, padx=10)

        tk.Button(buttons_frame, text="إضافة", font=('Arial', 9, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=2,
                 command=lambda: self.add_setting('sample_types', self.sample_type_entry.get())).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="تعديل", font=('Arial', 9, 'bold'),
                 bg='#f39c12', fg='white', relief='raised', bd=2,
                 command=lambda: self.edit_setting('sample_types')).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="حذف", font=('Arial', 9, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=2,
                 command=lambda: self.delete_setting('sample_types')).pack(side=tk.LEFT, padx=2)

        # قائمة أنواع العينات
        self.sample_types_listbox = tk.Listbox(frame, height=10, font=('Arial', 10))
        self.sample_types_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.refresh_settings_list('sample_types', self.sample_types_listbox)

    def create_sender_orgs_tab(self, parent):
        """إنشاء تبويب جهات الإرسال"""
        frame = tk.Frame(parent, bg='#ecf0f1')
        parent.add(frame, text="جهات الإرسال")

        input_frame = tk.Frame(frame, bg='#ecf0f1')
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(input_frame, text="جهة الإرسال:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5)
        self.sender_org_entry = tk.StringVar()
        tk.Entry(input_frame, textvariable=self.sender_org_entry, width=30).grid(
            row=0, column=1, padx=5, pady=5)

        buttons_frame = tk.Frame(input_frame, bg='#ecf0f1')
        buttons_frame.grid(row=0, column=2, padx=10)

        tk.Button(buttons_frame, text="إضافة", font=('Arial', 9, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=2,
                 command=lambda: self.add_setting('sender_orgs', self.sender_org_entry.get())).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="تعديل", font=('Arial', 9, 'bold'),
                 bg='#f39c12', fg='white', relief='raised', bd=2,
                 command=lambda: self.edit_setting('sender_orgs')).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="حذف", font=('Arial', 9, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=2,
                 command=lambda: self.delete_setting('sender_orgs')).pack(side=tk.LEFT, padx=2)

        self.sender_orgs_listbox = tk.Listbox(frame, height=10, font=('Arial', 10))
        self.sender_orgs_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.refresh_settings_list('sender_orgs', self.sender_orgs_listbox)

    def create_tests_tab(self, parent):
        """إنشاء تبويب التحاليل"""
        frame = tk.Frame(parent, bg='#ecf0f1')
        parent.add(frame, text="التحاليل")

        input_frame = tk.Frame(frame, bg='#ecf0f1')
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(input_frame, text="التحليل:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5)
        self.test_entry = tk.StringVar()
        tk.Entry(input_frame, textvariable=self.test_entry, width=30).grid(
            row=0, column=1, padx=5, pady=5)

        buttons_frame = tk.Frame(input_frame, bg='#ecf0f1')
        buttons_frame.grid(row=0, column=2, padx=10)

        tk.Button(buttons_frame, text="إضافة", font=('Arial', 9, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=2,
                 command=lambda: self.add_setting('tests', self.test_entry.get())).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="تعديل", font=('Arial', 9, 'bold'),
                 bg='#f39c12', fg='white', relief='raised', bd=2,
                 command=lambda: self.edit_setting('tests')).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="حذف", font=('Arial', 9, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=2,
                 command=lambda: self.delete_setting('tests')).pack(side=tk.LEFT, padx=2)

        self.tests_listbox = tk.Listbox(frame, height=10, font=('Arial', 10))
        self.tests_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.refresh_settings_list('tests', self.tests_listbox)

    def create_technicians_tab(self, parent):
        """إنشاء تبويب الفنيين"""
        frame = tk.Frame(parent, bg='#ecf0f1')
        parent.add(frame, text="الفنيين")

        input_frame = tk.Frame(frame, bg='#ecf0f1')
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(input_frame, text="اسم الفني:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5)
        self.technician_entry = tk.StringVar()
        tk.Entry(input_frame, textvariable=self.technician_entry, width=30).grid(
            row=0, column=1, padx=5, pady=5)

        buttons_frame = tk.Frame(input_frame, bg='#ecf0f1')
        buttons_frame.grid(row=0, column=2, padx=10)

        tk.Button(buttons_frame, text="إضافة", font=('Arial', 9, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=2,
                 command=lambda: self.add_setting('technicians', self.technician_entry.get())).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="تعديل", font=('Arial', 9, 'bold'),
                 bg='#f39c12', fg='white', relief='raised', bd=2,
                 command=lambda: self.edit_setting('technicians')).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="حذف", font=('Arial', 9, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=2,
                 command=lambda: self.delete_setting('technicians')).pack(side=tk.LEFT, padx=2)

        self.technicians_listbox = tk.Listbox(frame, height=10, font=('Arial', 10))
        self.technicians_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.refresh_settings_list('technicians', self.technicians_listbox)

    def create_report_settings_tab(self, parent):
        """إنشاء تبويب إعدادات التقرير"""
        frame = tk.Frame(parent, bg='#ecf0f1')
        parent.add(frame, text="إعدادات التقرير")

        # معلومات المختبر
        info_frame = tk.LabelFrame(frame, text="معلومات المختبر",
                                  font=('Arial', 12, 'bold'), bg='#ecf0f1')
        info_frame.pack(fill=tk.X, padx=10, pady=10)

        # العنوان العربي
        tk.Label(info_frame, text="العنوان العربي:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5, sticky='nw')
        self.arabic_header = tk.Text(info_frame, height=5, width=40, font=('Arial', 9))
        self.arabic_header.grid(row=0, column=1, padx=5, pady=5)
        self.arabic_header.insert('1.0', '''جمهورية العراق
وزارة الصحة
قسم الصحة العامة
مختبر الصحة العامة
وحدة الفايروسات''')

        # العنوان الإنجليزي
        tk.Label(info_frame, text="العنوان الإنجليزي:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=2, padx=5, pady=5, sticky='nw')
        self.english_header = tk.Text(info_frame, height=5, width=40, font=('Arial', 9))
        self.english_header.grid(row=0, column=3, padx=5, pady=5)
        self.english_header.insert('1.0', '''Republic of Iraq
Ministry of Health
Public Health Department
Public Health Laboratory
Virus Unit''')

        # العنوان والإيميل
        tk.Label(info_frame, text="العنوان:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=1, column=0, padx=5, pady=5)
        self.lab_address = tk.StringVar(value="ذي قار /الناصرية /سومر/ مجاور حسينية السفراء الاربعة")
        tk.Entry(info_frame, textvariable=self.lab_address, width=50).grid(
            row=1, column=1, columnspan=2, padx=5, pady=5)

        tk.Label(info_frame, text="الإيميل:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=2, column=0, padx=5, pady=5)
        self.lab_email = tk.StringVar()
        tk.Entry(info_frame, textvariable=self.lab_email, width=30).grid(
            row=2, column=1, padx=5, pady=5)

        # زر حفظ الإعدادات
        tk.Button(info_frame, text="حفظ الإعدادات", font=('Arial', 10, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=3,
                 command=self.save_report_settings).grid(row=3, column=1, pady=10)

    def add_setting(self, category, value):
        """إضافة إعداد جديد"""
        if not value.strip():
            messagebox.showerror("خطأ", "يرجى إدخال القيمة")
            return

        try:
            self.cursor.execute('''
                INSERT INTO settings (category, name, value)
                VALUES (?, ?, ?)
            ''', (category, value, value))
            self.conn.commit()

            # تحديث القائمة المناسبة
            if category == 'sample_types':
                self.refresh_settings_list(category, self.sample_types_listbox)
                self.sample_type_entry.set("")
            elif category == 'sender_orgs':
                self.refresh_settings_list(category, self.sender_orgs_listbox)
                self.sender_org_entry.set("")
            elif category == 'tests':
                self.refresh_settings_list(category, self.tests_listbox)
                self.test_entry.set("")
            elif category == 'technicians':
                self.refresh_settings_list(category, self.technicians_listbox)
                self.technician_entry.set("")

            messagebox.showinfo("نجح", "تم إضافة الإعداد بنجاح")

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "هذا الإعداد موجود مسبقاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الإضافة: {str(e)}")

    def edit_setting(self, category):
        """تعديل إعداد"""
        messagebox.showinfo("قيد التطوير", "تعديل الإعدادات قيد التطوير")

    def delete_setting(self, category):
        """حذف إعداد"""
        # تحديد القائمة المناسبة
        if category == 'sample_types':
            listbox = self.sample_types_listbox
        elif category == 'sender_orgs':
            listbox = self.sender_orgs_listbox
        elif category == 'tests':
            listbox = self.tests_listbox
        elif category == 'technicians':
            listbox = self.technicians_listbox
        else:
            return

        selection = listbox.curselection()
        if not selection:
            messagebox.showerror("خطأ", "يرجى اختيار عنصر للحذف")
            return

        selected_value = listbox.get(selection[0])

        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف '{selected_value}'؟"):
            try:
                self.cursor.execute('''
                    DELETE FROM settings WHERE category = ? AND value = ?
                ''', (category, selected_value))
                self.conn.commit()

                self.refresh_settings_list(category, listbox)
                messagebox.showinfo("نجح", "تم حذف الإعداد بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    def refresh_settings_list(self, category, listbox):
        """تحديث قائمة الإعدادات"""
        listbox.delete(0, tk.END)
        self.cursor.execute("SELECT value FROM settings WHERE category = ?", (category,))
        values = [row[0] for row in self.cursor.fetchall()]
        for value in values:
            listbox.insert(tk.END, value)

    def save_report_settings(self):
        """حفظ إعدادات التقرير"""
        try:
            # حفظ العناوين والمعلومات
            settings_data = [
                ('report_settings', 'arabic_header', self.arabic_header.get('1.0', tk.END).strip()),
                ('report_settings', 'english_header', self.english_header.get('1.0', tk.END).strip()),
                ('report_settings', 'lab_address', self.lab_address.get()),
                ('report_settings', 'lab_email', self.lab_email.get())
            ]

            for category, name, value in settings_data:
                self.cursor.execute('''
                    INSERT OR REPLACE INTO settings (category, name, value)
                    VALUES (?, ?, ?)
                ''', (category, name, value))

            self.conn.commit()
            messagebox.showinfo("نجح", "تم حفظ إعدادات التقرير بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = LabManagementSystem(root)
    root.mainloop()
