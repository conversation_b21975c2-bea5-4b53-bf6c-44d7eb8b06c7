#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مختبر الصحة العامة المركزي - ذي قار
Central Public Health Laboratory - Dhi Qar
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import datetime
import os
from PIL import Image, ImageTk
import pandas as pd
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.utils import ImageReader
from reportlab.pdfbase import pdfutils
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
import win32print
import win32ui
from win32.lib import win32con
import calendar

class DatePicker:
    """ويدجت اختيار التاريخ"""
    def __init__(self, parent, date_var, title="اختيار التاريخ"):
        self.parent = parent
        self.date_var = date_var
        self.title = title

    def show_calendar(self):
        """عرض نافذة التقويم"""
        cal_window = tk.Toplevel(self.parent)
        cal_window.title(self.title)
        cal_window.geometry("300x250")
        cal_window.configure(bg='white')
        cal_window.resizable(False, False)

        # جعل النافذة في المقدمة
        cal_window.transient(self.parent)
        cal_window.grab_set()

        # التاريخ الحالي
        now = datetime.datetime.now()

        # متغيرات الشهر والسنة
        self.cal_month = tk.IntVar(value=now.month)
        self.cal_year = tk.IntVar(value=now.year)

        # إطار التحكم
        control_frame = tk.Frame(cal_window, bg='white')
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # أزرار التنقل
        tk.Button(control_frame, text="◀", font=('Arial', 12, 'bold'),
                 command=self.prev_month, bg='#3498db', fg='white',
                 width=3).pack(side=tk.LEFT)

        self.month_year_label = tk.Label(control_frame, text="",
                                        font=('Arial', 12, 'bold'), bg='white')
        self.month_year_label.pack(side=tk.LEFT, expand=True)

        tk.Button(control_frame, text="▶", font=('Arial', 12, 'bold'),
                 command=self.next_month, bg='#3498db', fg='white',
                 width=3).pack(side=tk.RIGHT)

        # إطار التقويم
        self.cal_frame = tk.Frame(cal_window, bg='white')
        self.cal_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # أزرار الإجراءات
        action_frame = tk.Frame(cal_window, bg='white')
        action_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Button(action_frame, text="اليوم", font=('Arial', 10, 'bold'),
                 bg='#27ae60', fg='white', command=self.select_today).pack(side=tk.LEFT, padx=5)

        tk.Button(action_frame, text="إلغاء", font=('Arial', 10, 'bold'),
                 bg='#e74c3c', fg='white', command=cal_window.destroy).pack(side=tk.RIGHT, padx=5)

        # رسم التقويم
        self.draw_calendar()

    def prev_month(self):
        """الشهر السابق"""
        if self.cal_month.get() == 1:
            self.cal_month.set(12)
            self.cal_year.set(self.cal_year.get() - 1)
        else:
            self.cal_month.set(self.cal_month.get() - 1)
        self.draw_calendar()

    def next_month(self):
        """الشهر التالي"""
        if self.cal_month.get() == 12:
            self.cal_month.set(1)
            self.cal_year.set(self.cal_year.get() + 1)
        else:
            self.cal_month.set(self.cal_month.get() + 1)
        self.draw_calendar()

    def draw_calendar(self):
        """رسم التقويم"""
        # مسح التقويم السابق
        for widget in self.cal_frame.winfo_children():
            widget.destroy()

        # تحديث تسمية الشهر والسنة
        months = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
        self.month_year_label.config(text=f"{months[self.cal_month.get()]} {self.cal_year.get()}")

        # أيام الأسبوع
        days = ['أح', 'إث', 'ثل', 'أر', 'خم', 'جم', 'سب']
        for i, day in enumerate(days):
            tk.Label(self.cal_frame, text=day, font=('Arial', 10, 'bold'),
                    bg='#34495e', fg='white', width=4).grid(row=0, column=i, padx=1, pady=1)

        # أيام الشهر
        cal = calendar.monthcalendar(self.cal_year.get(), self.cal_month.get())

        for week_num, week in enumerate(cal, 1):
            for day_num, day in enumerate(week):
                if day == 0:
                    tk.Label(self.cal_frame, text="", width=4, height=2,
                            bg='white').grid(row=week_num, column=day_num, padx=1, pady=1)
                else:
                    btn = tk.Button(self.cal_frame, text=str(day), width=4, height=2,
                                   font=('Arial', 9), bg='#ecf0f1', fg='black',
                                   command=lambda d=day: self.select_date(d))
                    btn.grid(row=week_num, column=day_num, padx=1, pady=1)

                    # تمييز اليوم الحالي
                    today = datetime.datetime.now()
                    if (day == today.day and self.cal_month.get() == today.month
                        and self.cal_year.get() == today.year):
                        btn.config(bg='#3498db', fg='white', font=('Arial', 9, 'bold'))

    def select_date(self, day):
        """اختيار التاريخ"""
        selected_date = f"{self.cal_year.get()}-{self.cal_month.get():02d}-{day:02d}"
        self.date_var.set(selected_date)

        # إغلاق النافذة
        for widget in self.parent.winfo_children():
            if isinstance(widget, tk.Toplevel):
                widget.destroy()
                break

    def select_today(self):
        """اختيار اليوم الحالي"""
        today = datetime.datetime.now()
        today_str = today.strftime("%Y-%m-%d")
        self.date_var.set(today_str)

        # إغلاق النافذة
        for widget in self.parent.winfo_children():
            if isinstance(widget, tk.Toplevel):
                widget.destroy()
                break

class LabManagementSystem:
    def __init__(self, root):
        self.root = root
        self.root.title("مختبر الصحة العامة المركزي - ذي قار | Central Public Health Laboratory - Dhi Qar")
        self.root.geometry("1400x800")
        self.root.configure(bg='#f0f8ff')

        # محاولة تعيين أيقونة
        try:
            if os.path.exists('icon.ico'):
                self.root.iconbitmap('icon.ico')
        except:
            pass

        # تعيين الحد الأدنى لحجم النافذة
        self.root.minsize(1200, 700)

        # إنشاء قاعدة البيانات
        self.init_database()

        # إنشاء الواجهة الرئيسية
        self.create_main_interface()

        # تحديث العدادات
        self.update_counters()

        # إضافة شريط الحالة
        self.create_status_bar()

        # تحميل إعدادات التقرير
        self.load_report_settings()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        self.conn = sqlite3.connect('lab_database.db')
        self.cursor = self.conn.cursor()
        
        # جدول البيانات الأساسية
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                national_id INTEGER UNIQUE,
                name TEXT NOT NULL,
                age INTEGER NOT NULL,
                gender TEXT NOT NULL,
                address TEXT NOT NULL,
                sample_type TEXT NOT NULL,
                phone TEXT NOT NULL,
                sender_org TEXT NOT NULL,
                sample_date DATE NOT NULL,
                receive_date DATE DEFAULT CURRENT_DATE,
                passport_no TEXT,
                receipt_no TEXT,
                tests TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الوجبات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS batches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_no INTEGER UNIQUE,
                start_date DATE,
                end_date DATE,
                technician TEXT,
                work_date DATE DEFAULT CURRENT_DATE,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول النتائج (محسن لدعم تحاليل متعددة)
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                batch_id INTEGER,
                test_name TEXT,
                result TEXT,
                result_date DATE DEFAULT CURRENT_DATE,
                FOREIGN KEY (patient_id) REFERENCES patients (id),
                FOREIGN KEY (batch_id) REFERENCES batches (id)
            )
        ''')
        
        # جدول الإعدادات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT,
                name TEXT,
                value TEXT,
                UNIQUE(category, name)
            )
        ''')
        
        # تحديث قاعدة البيانات إذا لزم الأمر
        self.update_database_schema()

        # إدراج البيانات الافتراضية
        self.insert_default_settings()
        self.conn.commit()

    def update_database_schema(self):
        """تحديث هيكل قاعدة البيانات"""
        try:
            # التحقق من وجود عمود test_name في جدول results
            self.cursor.execute("PRAGMA table_info(results)")
            columns = [row[1] for row in self.cursor.fetchall()]

            if 'test_name' not in columns:
                print("تحديث قاعدة البيانات: إضافة عمود test_name...")
                # إضافة عمود test_name
                self.cursor.execute("ALTER TABLE results ADD COLUMN test_name TEXT")
                print("✅ تم تحديث قاعدة البيانات بنجاح")

        except Exception as e:
            print(f"تحذير: لم يتم تحديث قاعدة البيانات: {e}")
    
    def insert_default_settings(self):
        """إدراج الإعدادات الافتراضية"""
        default_settings = [
            ('sample_types', 'Blood', 'دم'),
            ('sample_types', 'Urine', 'بول'),
            ('sample_types', 'Stool', 'براز'),
            ('sample_types', 'Sputum', 'بلغم'),
            ('sender_orgs', 'Hospital A', 'مستشفى أ'),
            ('sender_orgs', 'Clinic B', 'عيادة ب'),
            ('tests', 'COVID-19', 'كوفيد-19'),
            ('tests', 'Hepatitis', 'التهاب الكبد'),
            ('technicians', 'أحمد محمد', 'أحمد محمد'),
            ('technicians', 'فاطمة علي', 'فاطمة علي'),
        ]
        
        for category, name, value in default_settings:
            self.cursor.execute('''
                INSERT OR IGNORE INTO settings (category, name, value)
                VALUES (?, ?, ?)
            ''', (category, name, value))
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية مع تصميم عصري"""
        # إنشاء الإطار الرئيسي مع تدرج لوني
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الشريط الجانبي للتبويبات مع تصميم حديث
        sidebar_frame = tk.Frame(main_frame, bg='#1a252f', width=220)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        sidebar_frame.pack_propagate(False)

        # شعار وعنوان المختبر مع تصميم أنيق
        header_frame = tk.Frame(sidebar_frame, bg='#1a252f')
        header_frame.pack(fill=tk.X, pady=20)

        # أيقونة المختبر
        lab_icon = tk.Label(header_frame, text="🏥", font=('Arial', 24),
                           bg='#1a252f', fg='#3498db')
        lab_icon.pack()

        title_label = tk.Label(header_frame,
                              text="مختبر الصحة العامة\nالمركزي - ذي قار",
                              font=('Arial', 11, 'bold'),
                              fg='white', bg='#1a252f',
                              justify=tk.CENTER)
        title_label.pack(pady=(10, 0))

        subtitle_label = tk.Label(header_frame,
                                 text="Central Public Health Lab",
                                 font=('Arial', 8),
                                 fg='#bdc3c7', bg='#1a252f')
        subtitle_label.pack(pady=(5, 0))

        # خط فاصل
        separator = tk.Frame(sidebar_frame, bg='#34495e', height=2)
        separator.pack(fill=tk.X, padx=20, pady=10)

        # أزرار التبويبات
        self.create_sidebar_buttons(sidebar_frame)

        # معلومات النظام في أسفل الشريط الجانبي
        info_frame = tk.Frame(sidebar_frame, bg='#1a252f')
        info_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=20)

        version_label = tk.Label(info_frame, text="الإصدار 1.0",
                               font=('Arial', 8), fg='#7f8c8d', bg='#1a252f')
        version_label.pack()

        # إنشاء منطقة المحتوى مع تصميم محسن
        self.content_frame = tk.Frame(main_frame, bg='#f8f9fa')
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # متغير لتتبع التبويب النشط
        self.active_tab = None

        # عرض التبويب الأول افتراضياً
        self.show_data_entry_tab()
    
    def create_sidebar_buttons(self, parent):
        """إنشاء أزرار الشريط الجانبي مع تصميم عصري"""
        # إطار الأزرار
        buttons_frame = tk.Frame(parent, bg='#1a252f')
        buttons_frame.pack(fill=tk.X, padx=15)

        # تعريف الأزرار مع الأيقونات
        buttons_data = [
            ("📝 إدخال البيانات", self.show_data_entry_tab, "#3498db"),
            ("⚙️ العمل", self.show_work_tab, "#e67e22"),
            ("📊 النتائج", self.show_results_tab, "#2ecc71"),
            ("📋 التقارير والإحصائيات", self.show_reports_tab, "#9b59b6"),
            ("🔧 الإعدادات", self.show_settings_tab, "#34495e")
        ]

        self.sidebar_buttons = []

        for i, (text, command, color) in enumerate(buttons_data):
            # إنشاء إطار للزر
            btn_frame = tk.Frame(buttons_frame, bg='#1a252f')
            btn_frame.pack(fill=tk.X, pady=3)

            # إنشاء الزر مع تصميم حديث
            btn = tk.Button(btn_frame, text=text,
                           font=('Arial', 10, 'bold'),
                           bg=color, fg='white',
                           relief='flat', bd=0,
                           width=25, height=2,
                           cursor='hand2',
                           activebackground=self.lighten_color(color),
                           activeforeground='white',
                           command=lambda cmd=command, idx=i: self.on_tab_click(cmd, idx))
            btn.pack(fill=tk.X, padx=5)

            # تأثيرات التفاعل
            btn.bind("<Enter>", lambda e, b=btn, c=color: self.on_button_hover(b, c))
            btn.bind("<Leave>", lambda e, b=btn, c=color: self.on_button_leave(b, c))

            self.sidebar_buttons.append(btn)

        # تعيين الزر الأول كنشط
        if self.sidebar_buttons:
            self.set_active_button(0)

    def lighten_color(self, color):
        """تفتيح اللون للتأثير التفاعلي"""
        color_map = {
            "#3498db": "#5dade2",
            "#e67e22": "#f39c12",
            "#2ecc71": "#58d68d",
            "#9b59b6": "#bb8fce",
            "#34495e": "#5d6d7e"
        }
        return color_map.get(color, color)

    def on_button_hover(self, button, original_color):
        """تأثير عند تمرير الماوس على الزر"""
        if button != self.sidebar_buttons[self.active_tab] if hasattr(self, 'active_tab') else True:
            button.config(bg=self.lighten_color(original_color))

    def on_button_leave(self, button, original_color):
        """تأثير عند مغادرة الماوس للزر"""
        if button != self.sidebar_buttons[self.active_tab] if hasattr(self, 'active_tab') else True:
            button.config(bg=original_color)

    def on_tab_click(self, command, index):
        """عند النقر على تبويب"""
        self.set_active_button(index)
        command()

    def set_active_button(self, index):
        """تعيين الزر النشط"""
        # إعادة تعيين جميع الأزرار
        colors = ["#3498db", "#e67e22", "#2ecc71", "#9b59b6", "#34495e"]
        for i, btn in enumerate(self.sidebar_buttons):
            if i == index:
                btn.config(bg="#1abc9c", relief='solid', bd=2)  # لون مميز للزر النشط
            else:
                btn.config(bg=colors[i], relief='flat', bd=0)

        self.active_tab = index
    
    def clear_content_frame(self):
        """مسح محتوى الإطار الرئيسي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_data_entry_tab(self):
        """عرض تبويب إدخال البيانات"""
        self.clear_content_frame()

        # عنوان التبويب مع تصميم حديث
        title_frame = tk.Frame(self.content_frame, bg='#2c3e50', height=60)
        title_frame.pack(fill=tk.X, padx=0, pady=0)
        title_frame.pack_propagate(False)

        title = tk.Label(title_frame, text="📋 إدخال البيانات",
                        font=('Arial', 18, 'bold'), bg='#2c3e50', fg='white')
        title.pack(expand=True)

        # إطار رئيسي مع تصميم حديث
        main_container = tk.Frame(self.content_frame, bg='#ecf0f1')
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # إطار النموذج مع حدود وظلال
        form_container = tk.LabelFrame(main_container, text="معلومات المريض",
                                     font=('Arial', 12, 'bold'), bg='white',
                                     fg='#2c3e50', relief='ridge', bd=2)
        form_container.pack(fill=tk.X, pady=(0, 15))

        # متغيرات النموذج
        self.entry_vars = {}

        # الصف الأول - المعلومات الأساسية
        row1_frame = tk.Frame(form_container, bg='white')
        row1_frame.pack(fill=tk.X, padx=15, pady=10)

        # الرقم الوطني (عرض فقط)
        tk.Label(row1_frame, text="الرقم الوطني", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=0, sticky='w', padx=(0, 5))
        self.national_id_display = tk.StringVar()
        national_id_label = tk.Label(row1_frame, textvariable=self.national_id_display,
                                   font=('Arial', 10, 'bold'), bg='#e8f5e8', fg='#27ae60',
                                   relief='solid', bd=1, width=10, anchor='center')
        national_id_label.grid(row=1, column=0, padx=(0, 15), pady=(2, 0), sticky='ew')

        # الاسم
        tk.Label(row1_frame, text="الاسم*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=1, sticky='w', padx=(0, 5))
        self.entry_vars['name'] = tk.StringVar()
        name_entry = tk.Entry(row1_frame, textvariable=self.entry_vars['name'],
                             font=('Arial', 10), width=20, relief='solid', bd=1)
        name_entry.grid(row=1, column=1, padx=(0, 15), pady=(2, 0), sticky='ew')

        # العمر
        tk.Label(row1_frame, text="العمر*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=2, sticky='w', padx=(0, 5))
        self.entry_vars['age'] = tk.StringVar()
        age_entry = tk.Entry(row1_frame, textvariable=self.entry_vars['age'],
                            font=('Arial', 10), width=10, relief='solid', bd=1)
        age_entry.grid(row=1, column=2, padx=(0, 15), pady=(2, 0), sticky='ew')

        # الجنس
        tk.Label(row1_frame, text="الجنس*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=3, sticky='w', padx=(0, 5))
        self.entry_vars['gender'] = tk.StringVar()
        gender_combo = ttk.Combobox(row1_frame, textvariable=self.entry_vars['gender'],
                                   values=['M', 'F'], width=8, state='readonly')
        gender_combo.grid(row=1, column=3, padx=(0, 15), pady=(2, 0), sticky='ew')

        # رقم الهاتف
        tk.Label(row1_frame, text="رقم الهاتف*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=4, sticky='w', padx=(0, 5))
        self.entry_vars['phone'] = tk.StringVar()
        phone_entry = tk.Entry(row1_frame, textvariable=self.entry_vars['phone'],
                              font=('Arial', 10), width=15, relief='solid', bd=1)
        phone_entry.grid(row=1, column=4, padx=(0, 0), pady=(2, 0), sticky='ew')

        # تكوين الأعمدة للتوسع
        row1_frame.columnconfigure(0, weight=1)
        row1_frame.columnconfigure(1, weight=2)
        row1_frame.columnconfigure(2, weight=1)
        row1_frame.columnconfigure(3, weight=1)
        row1_frame.columnconfigure(4, weight=1)

        # تحديث الرقم الوطني التلقائي
        self.update_next_national_id()

        # الصف الثاني - العنوان ونوع العينة
        row2_frame = tk.Frame(form_container, bg='white')
        row2_frame.pack(fill=tk.X, padx=15, pady=10)

        # العنوان
        tk.Label(row2_frame, text="العنوان*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=0, sticky='w', padx=(0, 5))
        self.entry_vars['address'] = tk.StringVar()
        address_entry = tk.Entry(row2_frame, textvariable=self.entry_vars['address'],
                                font=('Arial', 10), width=30, relief='solid', bd=1)
        address_entry.grid(row=1, column=0, padx=(0, 15), pady=(2, 0), sticky='ew')

        # نوع العينة
        tk.Label(row2_frame, text="نوع العينة*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=1, sticky='w', padx=(0, 5))
        self.entry_vars['sample_type'] = tk.StringVar()
        sample_combo = ttk.Combobox(row2_frame, textvariable=self.entry_vars['sample_type'],
                                   width=15, state='readonly')
        sample_combo.grid(row=1, column=1, padx=(0, 15), pady=(2, 0), sticky='ew')
        self.update_combobox_values(sample_combo, "sample_type")

        # جهة الإرسال
        tk.Label(row2_frame, text="جهة الإرسال*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=2, sticky='w', padx=(0, 5))
        self.entry_vars['sender_org'] = tk.StringVar()
        sender_combo = ttk.Combobox(row2_frame, textvariable=self.entry_vars['sender_org'],
                                   width=15, state='readonly')
        sender_combo.grid(row=1, column=2, padx=(0, 0), pady=(2, 0), sticky='ew')
        self.update_combobox_values(sender_combo, "sender_org")

        # تكوين الأعمدة للتوسع
        row2_frame.columnconfigure(0, weight=2)
        row2_frame.columnconfigure(1, weight=1)
        row2_frame.columnconfigure(2, weight=1)

        # الصف الثالث - التواريخ والمعلومات الإضافية
        row3_frame = tk.Frame(form_container, bg='white')
        row3_frame.pack(fill=tk.X, padx=15, pady=10)

        # تاريخ سحب العينة مع تقويم
        date_label_frame = tk.Frame(row3_frame, bg='white')
        date_label_frame.grid(row=0, column=0, sticky='w', padx=(0, 5))

        tk.Label(date_label_frame, text="تاريخ سحب العينة*", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(side=tk.LEFT)

        self.entry_vars['sample_date'] = tk.StringVar()
        # تعيين التاريخ الحالي كافتراضي
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        self.entry_vars['sample_date'].set(current_date)

        date_frame = tk.Frame(row3_frame, bg='white')
        date_frame.grid(row=1, column=0, padx=(0, 15), pady=(2, 0), sticky='ew')

        date_entry = tk.Entry(date_frame, textvariable=self.entry_vars['sample_date'],
                             font=('Arial', 10), width=12, relief='solid', bd=1)
        date_entry.pack(side=tk.LEFT)

        # زر التقويم
        tk.Button(date_frame, text="📅", font=('Arial', 8), bg='#3498db', fg='white',
                 relief='flat', bd=0, cursor='hand2', width=3,
                 command=lambda: DatePicker(self.root, self.entry_vars['sample_date'],
                                          "اختيار تاريخ سحب العينة").show_calendar()).pack(side=tk.LEFT, padx=(2, 0))

        # زر التاريخ الحالي
        tk.Button(date_frame, text="اليوم", font=('Arial', 8), bg='#27ae60', fg='white',
                 relief='flat', bd=0, cursor='hand2', width=4,
                 command=self.set_current_date).pack(side=tk.LEFT, padx=(2, 0))

        # رقم الجواز
        tk.Label(row3_frame, text="رقم الجواز", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=1, sticky='w', padx=(0, 5))
        self.entry_vars['passport_no'] = tk.StringVar()
        passport_entry = tk.Entry(row3_frame, textvariable=self.entry_vars['passport_no'],
                                 font=('Arial', 10), width=15, relief='solid', bd=1)
        passport_entry.grid(row=1, column=1, padx=(0, 15), pady=(2, 0), sticky='ew')

        # رقم الوصل
        tk.Label(row3_frame, text="رقم الوصل", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=2, sticky='w', padx=(0, 5))
        self.entry_vars['receipt_no'] = tk.StringVar()
        receipt_entry = tk.Entry(row3_frame, textvariable=self.entry_vars['receipt_no'],
                                font=('Arial', 10), width=15, relief='solid', bd=1)
        receipt_entry.grid(row=1, column=2, padx=(0, 0), pady=(2, 0), sticky='ew')

        # تكوين الأعمدة للتوسع
        row3_frame.columnconfigure(0, weight=1)
        row3_frame.columnconfigure(1, weight=1)
        row3_frame.columnconfigure(2, weight=1)

        # الصف الرابع - التحاليل
        row4_frame = tk.Frame(form_container, bg='white')
        row4_frame.pack(fill=tk.X, padx=15, pady=10)

        tk.Label(row4_frame, text="التحاليل المطلوبة:", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(anchor='w')

        tests_frame = tk.Frame(row4_frame, bg='white', relief='solid', bd=1)
        tests_frame.pack(fill=tk.X, pady=(5, 0))

        self.tests_listbox = tk.Listbox(tests_frame, selectmode=tk.MULTIPLE,
                                       height=3, font=('Arial', 9), bg='#f8f9fa')
        scrollbar_tests = ttk.Scrollbar(tests_frame, orient=tk.VERTICAL, command=self.tests_listbox.yview)
        self.tests_listbox.configure(yscrollcommand=scrollbar_tests.set)

        self.tests_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_tests.pack(side=tk.RIGHT, fill=tk.Y)
        self.update_tests_listbox()

        # أزرار العمليات مع تصميم حديث
        buttons_container = tk.Frame(main_container, bg='#ecf0f1')
        buttons_container.pack(fill=tk.X, pady=(0, 15))

        button_style = {
            'font': ('Arial', 10, 'bold'),
            'relief': 'raised',
            'bd': 3,
            'width': 12,
            'height': 2,
            'cursor': 'hand2'
        }

        buttons_frame = tk.Frame(buttons_container, bg='#ecf0f1')
        buttons_frame.pack()

        tk.Button(buttons_frame, text="➕ إضافة", bg='#27ae60', fg='white',
                 command=self.add_patient, **button_style).pack(side=tk.LEFT, padx=8)

        tk.Button(buttons_frame, text="✏️ تعديل", bg='#f39c12', fg='white',
                 command=self.edit_patient, **button_style).pack(side=tk.LEFT, padx=8)

        tk.Button(buttons_frame, text="🗑️ حذف", bg='#e74c3c', fg='white',
                 command=self.delete_patient, **button_style).pack(side=tk.LEFT, padx=8)

        tk.Button(buttons_frame, text="🔍 بحث", bg='#3498db', fg='white',
                 command=self.search_patient, **button_style).pack(side=tk.LEFT, padx=8)

        tk.Button(buttons_frame, text="📊 استيراد Excel", bg='#9b59b6', fg='white',
                 command=self.import_excel, **button_style).pack(side=tk.LEFT, padx=8)

        # جدول عرض البيانات مع تصميم محسن
        self.create_patients_table_modern(main_container)
    
    def update_combobox_values(self, combo, category):
        """تحديث قيم القائمة المنسدلة من قاعدة البيانات"""
        if category == "sample_type":
            db_category = "sample_types"
        elif category == "sender_org":
            db_category = "sender_orgs"
        else:
            return
        
        self.cursor.execute("SELECT value FROM settings WHERE category = ?", (db_category,))
        values = [row[0] for row in self.cursor.fetchall()]
        combo['values'] = values
    
    def update_tests_listbox(self):
        """تحديث قائمة التحاليل"""
        self.tests_listbox.delete(0, tk.END)
        self.cursor.execute("SELECT value FROM settings WHERE category = 'tests'")
        tests = [row[0] for row in self.cursor.fetchall()]
        for test in tests:
            self.tests_listbox.insert(tk.END, test)
    
    def create_patients_table_modern(self, parent):
        """إنشاء جدول عرض المرضى مع تصميم حديث"""
        # إطار الجدول مع عنوان
        table_container = tk.LabelFrame(parent, text="قائمة المرضى",
                                       font=('Arial', 12, 'bold'), bg='white',
                                       fg='#2c3e50', relief='ridge', bd=2)
        table_container.pack(fill=tk.BOTH, expand=True)

        # إطار البحث السريع
        search_frame = tk.Frame(table_container, bg='white')
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(search_frame, text="🔍 بحث سريع:", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').pack(side=tk.LEFT, padx=(0, 10))

        self.quick_search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.quick_search_var,
                               font=('Arial', 10), width=30, relief='solid', bd=1)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_quick_search)

        tk.Button(search_frame, text="مسح", font=('Arial', 9), bg='#95a5a6', fg='white',
                 relief='raised', bd=2, command=self.clear_quick_search).pack(side=tk.LEFT)

        # إطار الجدول
        table_frame = tk.Frame(table_container, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # إنشاء Treeview مع تصميم محسن
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'تاريخ الاستلام')
        self.patients_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)

        # تعيين عناوين الأعمدة مع تحسين العرض
        column_widths = {'الرقم الوطني': 100, 'الاسم': 150, 'العمر': 60,
                        'الجنس': 60, 'نوع العينة': 120, 'تاريخ الاستلام': 120}

        for col in columns:
            self.patients_tree.heading(col, text=col, anchor='center')
            self.patients_tree.column(col, width=column_widths.get(col, 100),
                                    anchor='center', minwidth=50)

        # تنسيق الجدول
        style = ttk.Style()
        style.configure("Treeview", font=('Arial', 9))
        style.configure("Treeview.Heading", font=('Arial', 10, 'bold'))

        # شريط التمرير العمودي والأفقي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.patients_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.patients_tree.xview)

        self.patients_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.patients_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # تكوين التوسع
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # إضافة ألوان متناوبة للصفوف
        self.patients_tree.tag_configure('oddrow', background='#f8f9fa')
        self.patients_tree.tag_configure('evenrow', background='white')

        # تحديث البيانات
        self.refresh_patients_table()

    def on_quick_search(self, event):
        """البحث السريع في الجدول"""
        search_term = self.quick_search_var.get().lower()

        # مسح الجدول
        for item in self.patients_tree.get_children():
            self.patients_tree.delete(item)

        # جلب البيانات مع الفلترة
        if search_term:
            query = '''
                SELECT national_id, name, age, gender, sample_type, receive_date
                FROM patients
                WHERE LOWER(name) LIKE ? OR CAST(national_id AS TEXT) LIKE ?
                ORDER BY national_id DESC
            '''
            self.cursor.execute(query, (f'%{search_term}%', f'%{search_term}%'))
        else:
            query = '''
                SELECT national_id, name, age, gender, sample_type, receive_date
                FROM patients ORDER BY national_id DESC
            '''
            self.cursor.execute(query)

        # إضافة البيانات مع الألوان المتناوبة
        for i, row in enumerate(self.cursor.fetchall()):
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.patients_tree.insert('', tk.END, values=row, tags=(tag,))

    def clear_quick_search(self):
        """مسح البحث السريع"""
        self.quick_search_var.set("")
        self.refresh_patients_table()
    
    def refresh_patients_table(self):
        """تحديث جدول المرضى مع تحسينات"""
        # مسح البيانات الحالية
        for item in self.patients_tree.get_children():
            self.patients_tree.delete(item)

        # جلب البيانات من قاعدة البيانات
        self.cursor.execute('''
            SELECT national_id, name, age, gender, sample_type, receive_date
            FROM patients ORDER BY national_id DESC
        ''')

        # إضافة البيانات مع الألوان المتناوبة
        for i, row in enumerate(self.cursor.fetchall()):
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.patients_tree.insert('', tk.END, values=row, tags=(tag,))
    
    def add_patient(self):
        """إضافة مريض جديد مع رقم وطني تصاعدي"""
        # التحقق من الحقول المطلوبة
        required_fields = ['name', 'age', 'gender', 'address', 'sample_type', 'phone', 'sender_org', 'sample_date']

        for field in required_fields:
            if not self.entry_vars[field].get().strip():
                messagebox.showerror("خطأ", f"يرجى ملء حقل {field}")
                return

        # الحصول على التحاليل المحددة
        selected_tests = [self.tests_listbox.get(i) for i in self.tests_listbox.curselection()]
        tests_str = ','.join(selected_tests)

        # التحقق من تكرار الاسم
        patient_name = self.entry_vars['name'].get().strip()
        try:
            self.cursor.execute("SELECT COUNT(*) FROM patients WHERE LOWER(name) = LOWER(?)", (patient_name,))
            name_count = self.cursor.fetchone()[0]

            if name_count > 0:
                # إظهار رسالة تحذير مع خيارات
                response = messagebox.askyesnocancel(
                    "تحذير - اسم مكرر",
                    f"تم العثور على {name_count} مريض بنفس الاسم '{patient_name}'\n\n"
                    "هل تريد المتابعة؟\n\n"
                    "• نعم: إضافة المريض رغم تكرار الاسم\n"
                    "• لا: عرض المرضى بنفس الاسم\n"
                    "• إلغاء: العودة لتعديل البيانات",
                    icon='warning'
                )

                if response is None:  # إلغاء
                    return
                elif response is False:  # لا - عرض المرضى المكررين
                    self.show_duplicate_patients(patient_name)
                    return
                # إذا كان True (نعم) فسيتم المتابعة

        except Exception as e:
            print(f"خطأ في التحقق من تكرار الاسم: {e}")

        try:
            # الحصول على الرقم الوطني التالي
            self.cursor.execute("SELECT MAX(national_id) FROM patients")
            result = self.cursor.fetchone()
            next_national_id = (result[0] or 0) + 1

            # إدراج البيانات مع الرقم الوطني التصاعدي
            self.cursor.execute('''
                INSERT INTO patients (national_id, name, age, gender, address, sample_type, phone,
                                    sender_org, sample_date, passport_no, receipt_no, tests)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                next_national_id,
                self.entry_vars['name'].get(),
                int(self.entry_vars['age'].get()),
                self.entry_vars['gender'].get(),
                self.entry_vars['address'].get(),
                self.entry_vars['sample_type'].get(),
                self.entry_vars['phone'].get(),
                self.entry_vars['sender_org'].get(),
                self.entry_vars['sample_date'].get(),
                self.entry_vars['passport_no'].get(),
                self.entry_vars['receipt_no'].get(),
                tests_str
            ))

            self.conn.commit()

            # طباعة الاستيكر
            self.print_sticker()

            # تحديث الجدول والعدادات
            self.refresh_patients_table()
            self.update_counters()

            # تحديث الرقم الوطني التالي
            self.update_next_national_id()

            # مسح الحقول
            self.clear_entry_fields()

            # تحديث شريط الحالة
            self.update_status(f"تم إضافة المريض بنجاح - الرقم الوطني: {next_national_id}")

            messagebox.showinfo("نجح", f"تم إضافة المريض بنجاح\nالرقم الوطني: {next_national_id}\nتم طباعة الاستيكر مباشرة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة المريض: {str(e)}")

    def show_duplicate_patients(self, patient_name):
        """عرض المرضى بنفس الاسم"""
        try:
            # البحث عن المرضى بنفس الاسم
            self.cursor.execute('''
                SELECT national_id, name, age, gender, address, phone, sender_org, sample_date, tests
                FROM patients
                WHERE LOWER(name) = LOWER(?)
                ORDER BY sample_date DESC
            ''', (patient_name,))

            duplicate_patients = self.cursor.fetchall()

            if not duplicate_patients:
                messagebox.showinfo("معلومات", "لم يتم العثور على مرضى بنفس الاسم")
                return

            # إنشاء نافذة عرض المرضى المكررين
            duplicates_window = tk.Toplevel(self.root)
            duplicates_window.title(f"المرضى بنفس الاسم: {patient_name}")
            duplicates_window.geometry("900x500")
            duplicates_window.configure(bg='white')

            # جعل النافذة في المقدمة
            duplicates_window.transient(self.root)
            duplicates_window.grab_set()

            # عنوان النافذة
            title_frame = tk.Frame(duplicates_window, bg='#e74c3c', height=60)
            title_frame.pack(fill=tk.X)
            title_frame.pack_propagate(False)

            tk.Label(title_frame, text=f"⚠️ تحذير: تم العثور على {len(duplicate_patients)} مريض بنفس الاسم",
                    font=('Arial', 14, 'bold'), bg='#e74c3c', fg='white').pack(expand=True)

            # معلومات إضافية
            info_frame = tk.Frame(duplicates_window, bg='#f8f9fa')
            info_frame.pack(fill=tk.X, padx=10, pady=5)

            tk.Label(info_frame, text=f"الاسم المكرر: {patient_name}",
                    font=('Arial', 12, 'bold'), bg='#f8f9fa', fg='#2c3e50').pack(anchor='w')
            tk.Label(info_frame, text="يرجى مراجعة القائمة أدناه للتأكد من عدم وجود نفس المريض",
                    font=('Arial', 10), bg='#f8f9fa', fg='#7f8c8d').pack(anchor='w')

            # جدول المرضى المكررين
            table_frame = tk.Frame(duplicates_window, bg='white')
            table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'العنوان', 'الهاتف', 'جهة الإرسال', 'تاريخ العينة', 'التحاليل')
            duplicates_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)

            # تعيين عناوين الأعمدة
            column_widths = {
                'الرقم الوطني': 80, 'الاسم': 120, 'العمر': 60, 'الجنس': 60,
                'العنوان': 150, 'الهاتف': 100, 'جهة الإرسال': 120,
                'تاريخ العينة': 100, 'التحاليل': 150
            }

            for col in columns:
                duplicates_tree.heading(col, text=col, anchor='center')
                duplicates_tree.column(col, width=column_widths.get(col, 100),
                                     anchor='center', minwidth=50)

            # إضافة البيانات
            for patient in duplicate_patients:
                # تقصير النصوص الطويلة
                display_data = []
                for i, value in enumerate(patient):
                    if value is None:
                        display_data.append("")
                    elif len(str(value)) > 20:
                        display_data.append(str(value)[:17] + "...")
                    else:
                        display_data.append(str(value))

                duplicates_tree.insert('', tk.END, values=display_data)

            # شريط التمرير
            scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=duplicates_tree.yview)
            duplicates_tree.configure(yscrollcommand=scrollbar.set)

            duplicates_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # أزرار الإجراءات
            buttons_frame = tk.Frame(duplicates_window, bg='white')
            buttons_frame.pack(fill=tk.X, padx=10, pady=10)

            def continue_anyway():
                """المتابعة رغم التكرار"""
                if messagebox.askyesno("تأكيد", "هل تريد إضافة المريض رغم وجود مرضى بنفس الاسم؟"):
                    duplicates_window.destroy()
                    # استكمال عملية الإضافة
                    self.continue_add_patient()

            def cancel_add():
                """إلغاء الإضافة"""
                duplicates_window.destroy()

            tk.Button(buttons_frame, text="✅ متابعة الإضافة رغم التكرار",
                     font=('Arial', 11, 'bold'), bg='#f39c12', fg='white',
                     relief='raised', bd=3, cursor='hand2',
                     command=continue_anyway).pack(side=tk.LEFT, padx=10)

            tk.Button(buttons_frame, text="❌ إلغاء الإضافة",
                     font=('Arial', 11, 'bold'), bg='#e74c3c', fg='white',
                     relief='raised', bd=3, cursor='hand2',
                     command=cancel_add).pack(side=tk.RIGHT, padx=10)

            # معلومات إضافية في الأسفل
            footer_frame = tk.Frame(duplicates_window, bg='#ecf0f1')
            footer_frame.pack(fill=tk.X)

            tk.Label(footer_frame, text="💡 نصيحة: تأكد من البيانات الشخصية (العمر، العنوان، الهاتف) للتمييز بين المرضى",
                    font=('Arial', 9), bg='#ecf0f1', fg='#34495e').pack(pady=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عرض المرضى المكررين: {str(e)}")

    def continue_add_patient(self):
        """استكمال إضافة المريض بعد التأكيد"""
        try:
            # الحصول على التحاليل المحددة
            selected_tests = [self.tests_listbox.get(i) for i in self.tests_listbox.curselection()]
            tests_str = ','.join(selected_tests)

            # الحصول على الرقم الوطني التالي
            self.cursor.execute("SELECT MAX(national_id) FROM patients")
            result = self.cursor.fetchone()
            next_national_id = (result[0] or 0) + 1

            # إدراج البيانات مع الرقم الوطني التصاعدي
            self.cursor.execute('''
                INSERT INTO patients (national_id, name, age, gender, address, sample_type, phone,
                                    sender_org, sample_date, passport_no, receipt_no, tests)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                next_national_id,
                self.entry_vars['name'].get(),
                int(self.entry_vars['age'].get()),
                self.entry_vars['gender'].get(),
                self.entry_vars['address'].get(),
                self.entry_vars['sample_type'].get(),
                self.entry_vars['phone'].get(),
                self.entry_vars['sender_org'].get(),
                self.entry_vars['sample_date'].get(),
                self.entry_vars['passport_no'].get(),
                self.entry_vars['receipt_no'].get(),
                tests_str
            ))

            self.conn.commit()

            # طباعة الاستيكر
            self.print_sticker()

            # تحديث الجدول والعدادات
            self.refresh_patients_table()
            self.update_counters()

            # تحديث الرقم الوطني التالي
            self.update_next_national_id()

            # مسح الحقول
            self.clear_entry_fields()

            # تحديث شريط الحالة
            self.update_status(f"تم إضافة المريض بنجاح - الرقم الوطني: {next_national_id}")

            messagebox.showinfo("نجح", f"تم إضافة المريض بنجاح\nالرقم الوطني: {next_national_id}\nتم طباعة الاستيكر مباشرة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة المريض: {str(e)}")
    
    def print_sticker(self):
        """طباعة استيكر مباشرة - الاسم ونوع العينة فقط"""
        try:
            # الحصول على آخر مريض مضاف
            self.cursor.execute("SELECT * FROM patients ORDER BY id DESC LIMIT 1")
            patient = self.cursor.fetchone()

            if patient:
                # إنشاء استيكر مبسط - الاسم ونوع العينة فقط
                patient_name = patient[2]  # اسم المريض
                sample_type = patient[5]   # نوع العينة

                # استيكر مبسط مع الاسم ونوع العينة فقط
                sticker_text = f"""╔═══════════════════════════════╗
║     مختبر الصحة العامة المركزي     ║
║            ذي قار            ║
╠═══════════════════════════════╣
║ الاسم: {patient_name:<20} ║
║ نوع العينة: {sample_type:<15} ║
╚═══════════════════════════════╝"""

                # طباعة مباشرة بدون عرض أو ملف PDF
                self.direct_print_only(sticker_text)
                print("✅ تم إرسال الاستيكر للطباعة المباشرة")
            else:
                print("❌ لا توجد بيانات مريض للطباعة")

        except Exception as e:
            print(f"❌ خطأ في طباعة الاستيكر: {str(e)}")

    def direct_print_only(self, text):
        """طباعة مباشرة فقط - بدون عرض أو ملف PDF"""
        try:
            # البحث عن طابعة الاستيكر تلقائياً
            printers = win32print.EnumPrinters(2)
            sticker_printer = None

            # البحث عن طابعة تحتوي على كلمات مفتاحية للاستيكر
            sticker_keywords = ['label', 'sticker', 'zebra', 'dymo', 'brother', 'thermal']

            for printer in printers:
                printer_name = printer[2].lower()
                if any(keyword in printer_name for keyword in sticker_keywords):
                    sticker_printer = printer[2]
                    break

            # إذا لم توجد طابعة استيكر، استخدم الطابعة الافتراضية
            if not sticker_printer:
                sticker_printer = win32print.GetDefaultPrinter()

            # طباعة مباشرة فقط
            self.send_to_printer_directly(sticker_printer, text)

        except Exception as e:
            print(f"❌ خطأ في الطباعة المباشرة: {str(e)}")
            # في حالة الفشل، طباعة صامتة بدون عرض
            self.silent_print(text)

    def send_to_printer_directly(self, printer_name, text):
        """إرسال مباشر للطابعة بدون أي عرض"""
        try:
            # فتح الطابعة
            hprinter = win32print.OpenPrinter(printer_name)

            try:
                # إنشاء مهمة طباعة مباشرة
                job_id = win32print.StartDocPrinter(hprinter, 1, ("Lab Sticker", None, "RAW"))
                win32print.StartPagePrinter(hprinter)

                # تحويل النص إلى bytes للطباعة المباشرة
                text_bytes = text.encode('utf-8')
                win32print.WritePrinter(hprinter, text_bytes)

                # إنهاء الطباعة
                win32print.EndPagePrinter(hprinter)
                win32print.EndDocPrinter(hprinter)

                print(f"✅ تم إرسال الاستيكر مباشرة إلى: {printer_name}")

            finally:
                win32print.ClosePrinter(hprinter)

        except Exception as e:
            print(f"❌ فشل الإرسال المباشر: {str(e)}")
            # محاولة طباعة بديلة صامتة
            self.silent_print(text)

    def silent_print(self, text):
        """طباعة صامتة بدون عرض في حالة فشل الطباعة المباشرة"""
        try:
            import tempfile
            import os
            import subprocess

            # إنشاء ملف نصي مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(text)
                temp_filename = temp_file.name

            # طباعة صامتة بدون فتح نافذة
            subprocess.run(['notepad', '/p', temp_filename],
                          creationflags=subprocess.CREATE_NO_WINDOW,
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL)

            # حذف الملف المؤقت بعد قليل
            import threading
            def cleanup():
                import time
                time.sleep(3)  # انتظار 3 ثوان
                try:
                    os.unlink(temp_filename)
                except:
                    pass

            threading.Thread(target=cleanup, daemon=True).start()
            print("✅ تم استخدام الطباعة الصامتة")

        except Exception as e:
            print(f"❌ فشل في الطباعة الصامتة: {str(e)}")

    def print_to_sticker_printer(self, text):
        """طباعة مباشرة على طابعة الاستيكر"""
        try:
            # البحث عن طابعة الاستيكر تلقائياً
            printers = win32print.EnumPrinters(2)
            sticker_printer = None

            # البحث عن طابعة تحتوي على كلمات مفتاحية للاستيكر
            sticker_keywords = ['label', 'sticker', 'zebra', 'dymo', 'brother', 'thermal']

            for printer in printers:
                printer_name = printer[2].lower()
                if any(keyword in printer_name for keyword in sticker_keywords):
                    sticker_printer = printer[2]
                    break

            # إذا لم توجد طابعة استيكر، استخدم الطابعة الافتراضية
            if not sticker_printer:
                sticker_printer = win32print.GetDefaultPrinter()

            # طباعة مباشرة بدون إنشاء ملف PDF
            self.direct_print_sticker(sticker_printer, text)

            print(f"✅ تم إرسال الاستيكر مباشرة إلى الطابعة: {sticker_printer}")
            return True

        except Exception as e:
            print(f"❌ خطأ في الطباعة المباشرة: {str(e)}")
            # محاولة طباعة بديلة
            return self.fallback_print_sticker(text)

    def direct_print_sticker(self, printer_name, text):
        """طباعة مباشرة للاستيكر بدون ملف PDF"""
        try:
            # فتح الطابعة
            hprinter = win32print.OpenPrinter(printer_name)

            try:
                # إنشاء سياق الطباعة
                hdc = win32ui.CreateDC()
                hdc.CreatePrinterDC(printer_name)

                # بدء مهمة الطباعة
                hdc.StartDoc("Lab Sticker - Direct Print")
                hdc.StartPage()

                # إعداد الخط للطباعة
                font = win32ui.CreateFont({
                    "name": "Arial",
                    "height": 120,  # حجم الخط للاستيكر
                    "weight": 400
                })
                hdc.SelectObject(font)

                # تقسيم النص إلى أسطر وطباعة كل سطر
                lines = text.strip().split('\n')
                y_position = 100  # البداية من الأعلى
                line_height = 150  # المسافة بين الأسطر

                for line in lines:
                    if line.strip():  # تجاهل الأسطر الفارغة
                        hdc.TextOut(100, y_position, line)
                        y_position += line_height

                # إنهاء الطباعة
                hdc.EndPage()
                hdc.EndDoc()
                hdc.DeleteDC()

                print("✅ تم إرسال الاستيكر للطباعة المباشرة بنجاح")

            finally:
                win32print.ClosePrinter(hprinter)

        except Exception as e:
            raise Exception(f"فشل في الطباعة المباشرة: {str(e)}")

    def fallback_print_sticker(self, text):
        """طباعة بديلة في حالة فشل الطباعة المباشرة"""
        try:
            # محاولة طباعة نصية بسيطة
            import tempfile
            import os

            # إنشاء ملف نصي مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(text)
                temp_filename = temp_file.name

            # طباعة الملف النصي مباشرة
            os.system(f'notepad /p "{temp_filename}"')

            # حذف الملف المؤقت بعد قليل
            import threading
            def cleanup():
                import time
                time.sleep(5)  # انتظار 5 ثوان
                try:
                    os.unlink(temp_filename)
                except:
                    pass

            threading.Thread(target=cleanup, daemon=True).start()

            print("✅ تم استخدام الطباعة البديلة")
            return True

        except Exception as e:
            print(f"❌ فشل في الطباعة البديلة: {str(e)}")
            # لا عرض - طباعة فقط
            return False
    
    def update_next_national_id(self):
        """تحديث الرقم الوطني التالي"""
        try:
            self.cursor.execute("SELECT MAX(national_id) FROM patients")
            result = self.cursor.fetchone()
            next_id = (result[0] or 0) + 1
            self.national_id_display.set(f"#{next_id}")
        except:
            self.national_id_display.set("#1")

    def set_current_date(self):
        """تعيين التاريخ الحالي"""
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        self.entry_vars['sample_date'].set(current_date)

    def clear_entry_fields(self):
        """مسح حقول الإدخال"""
        for var in self.entry_vars.values():
            var.set("")
        # إعادة تعيين التاريخ الحالي
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        self.entry_vars['sample_date'].set(current_date)
        self.tests_listbox.selection_clear(0, tk.END)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة مع تصميم عصري"""
        self.status_bar = tk.Frame(self.root, bg='#1a252f', height=35)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_bar.pack_propagate(False)

        # إطار المعلومات اليسار
        left_frame = tk.Frame(self.status_bar, bg='#1a252f')
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=15)

        # معلومات الحالة مع أيقونة
        status_frame = tk.Frame(left_frame, bg='#1a252f')
        status_frame.pack(side=tk.LEFT, pady=8)

        tk.Label(status_frame, text="🟢", font=('Arial', 10),
                bg='#1a252f', fg='#2ecc71').pack(side=tk.LEFT)
        self.status_label = tk.Label(status_frame, text="جاهز",
                                    font=('Arial', 9, 'bold'), bg='#1a252f', fg='#ecf0f1')
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))

        # فاصل
        tk.Label(left_frame, text="│", font=('Arial', 12),
                bg='#1a252f', fg='#34495e').pack(side=tk.LEFT, padx=15)

        # عداد المرضى مع أيقونة
        patients_frame = tk.Frame(left_frame, bg='#1a252f')
        patients_frame.pack(side=tk.LEFT, pady=8)

        tk.Label(patients_frame, text="👥", font=('Arial', 10),
                bg='#1a252f').pack(side=tk.LEFT)
        self.patients_count_label = tk.Label(patients_frame, text="المرضى: 0",
                                           font=('Arial', 9), bg='#1a252f', fg='#3498db')
        self.patients_count_label.pack(side=tk.LEFT, padx=(5, 0))

        # فاصل
        tk.Label(left_frame, text="│", font=('Arial', 12),
                bg='#1a252f', fg='#34495e').pack(side=tk.LEFT, padx=15)

        # عداد الوجبات مع أيقونة
        batches_frame = tk.Frame(left_frame, bg='#1a252f')
        batches_frame.pack(side=tk.LEFT, pady=8)

        tk.Label(batches_frame, text="📦", font=('Arial', 10),
                bg='#1a252f').pack(side=tk.LEFT)
        self.batches_count_label = tk.Label(batches_frame, text="الوجبات: 0",
                                          font=('Arial', 9), bg='#1a252f', fg='#e67e22')
        self.batches_count_label.pack(side=tk.LEFT, padx=(5, 0))

        # إطار المعلومات اليمين
        right_frame = tk.Frame(self.status_bar, bg='#1a252f')
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=15)

        # التاريخ والوقت مع أيقونة
        datetime_frame = tk.Frame(right_frame, bg='#1a252f')
        datetime_frame.pack(side=tk.RIGHT, pady=8)

        tk.Label(datetime_frame, text="🕐", font=('Arial', 10),
                bg='#1a252f').pack(side=tk.LEFT)
        self.datetime_label = tk.Label(datetime_frame, text="",
                                     font=('Arial', 9), bg='#1a252f', fg='#bdc3c7')
        self.datetime_label.pack(side=tk.LEFT, padx=(5, 0))

        # تحديث التاريخ والوقت
        self.update_datetime()

    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.datetime.now()
        datetime_str = now.strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.config(text=datetime_str)

        # تحديث كل ثانية
        self.root.after(1000, self.update_datetime)

    def update_counters(self):
        """تحديث العدادات"""
        try:
            # عدد المرضى
            self.cursor.execute("SELECT COUNT(*) FROM patients")
            patients_count = self.cursor.fetchone()[0]
            self.patients_count_label.config(text=f"المرضى: {patients_count}")

            # عدد الوجبات
            self.cursor.execute("SELECT COUNT(*) FROM batches")
            batches_count = self.cursor.fetchone()[0]
            self.batches_count_label.config(text=f"الوجبات: {batches_count}")

        except:
            pass

    def update_status(self, message):
        """تحديث رسالة الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)
            self.root.update_idletasks()
    
    def edit_patient(self):
        """تعديل بيانات المريض"""
        selection = self.patients_tree.selection()
        if not selection:
            messagebox.showerror("خطأ", "يرجى اختيار مريض للتعديل")
            return

        # الحصول على بيانات المريض المحدد
        item = self.patients_tree.item(selection[0])
        national_id = item['values'][0]

        # جلب البيانات الكاملة من قاعدة البيانات
        self.cursor.execute("SELECT * FROM patients WHERE national_id = ?", (national_id,))
        patient = self.cursor.fetchone()

        if patient:
            # ملء الحقول بالبيانات الحالية
            self.entry_vars['name'].set(patient[2])
            self.entry_vars['age'].set(patient[3])
            self.entry_vars['gender'].set(patient[4])
            self.entry_vars['address'].set(patient[5])
            self.entry_vars['sample_type'].set(patient[6])
            self.entry_vars['phone'].set(patient[7])
            self.entry_vars['sender_org'].set(patient[8])
            self.entry_vars['sample_date'].set(patient[9])
            self.entry_vars['passport_no'].set(patient[11] or "")
            self.entry_vars['receipt_no'].set(patient[12] or "")

            # تحديد التحاليل
            if patient[13]:
                tests = patient[13].split(',')
                for i in range(self.tests_listbox.size()):
                    if self.tests_listbox.get(i) in tests:
                        self.tests_listbox.selection_set(i)

            # تغيير زر الإضافة إلى تحديث
            messagebox.showinfo("تعديل", "تم تحميل بيانات المريض. قم بالتعديل ثم اضغط 'تحديث'")

    def delete_patient(self):
        """حذف المريض"""
        selection = self.patients_tree.selection()
        if not selection:
            messagebox.showerror("خطأ", "يرجى اختيار مريض للحذف")
            return

        item = self.patients_tree.item(selection[0])
        national_id = item['values'][0]
        patient_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المريض '{patient_name}'؟"):
            try:
                # حذف النتائج المرتبطة أولاً
                self.cursor.execute('''
                    DELETE FROM results WHERE patient_id =
                    (SELECT id FROM patients WHERE national_id = ?)
                ''', (national_id,))

                # حذف المريض
                self.cursor.execute("DELETE FROM patients WHERE national_id = ?", (national_id,))
                self.conn.commit()

                # تحديث الجدول
                self.refresh_patients_table()
                messagebox.showinfo("نجح", "تم حذف المريض بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    def search_patient(self):
        """البحث عن المريض"""
        search_window = tk.Toplevel(self.root)
        search_window.title("البحث عن المريض")
        search_window.geometry("400x300")
        search_window.configure(bg='#ecf0f1')

        # حقول البحث
        tk.Label(search_window, text="البحث بالاسم:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5, sticky='e')
        search_name = tk.StringVar()
        tk.Entry(search_window, textvariable=search_name, width=25).grid(
            row=0, column=1, padx=5, pady=5)

        tk.Label(search_window, text="البحث بالرقم الوطني:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=1, column=0, padx=5, pady=5, sticky='e')
        search_national_id = tk.StringVar()
        tk.Entry(search_window, textvariable=search_national_id, width=25).grid(
            row=1, column=1, padx=5, pady=5)

        tk.Label(search_window, text="البحث بالهاتف:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=2, column=0, padx=5, pady=5, sticky='e')
        search_phone = tk.StringVar()
        tk.Entry(search_window, textvariable=search_phone, width=25).grid(
            row=2, column=1, padx=5, pady=5)

        def perform_search():
            query = "SELECT national_id, name, age, gender, sample_type, receive_date FROM patients WHERE 1=1"
            params = []

            if search_name.get():
                query += " AND name LIKE ?"
                params.append(f"%{search_name.get()}%")

            if search_national_id.get():
                query += " AND national_id = ?"
                params.append(int(search_national_id.get()))

            if search_phone.get():
                query += " AND phone LIKE ?"
                params.append(f"%{search_phone.get()}%")

            try:
                self.cursor.execute(query, params)
                results = self.cursor.fetchall()

                # عرض النتائج
                results_text = tk.Text(search_window, height=10, width=50)
                results_text.grid(row=4, column=0, columnspan=2, padx=10, pady=10)

                if results:
                    results_text.insert('1.0', "نتائج البحث:\n\n")
                    for result in results:
                        results_text.insert(tk.END, f"الرقم الوطني: {result[0]}\n")
                        results_text.insert(tk.END, f"الاسم: {result[1]}\n")
                        results_text.insert(tk.END, f"العمر: {result[2]}\n")
                        results_text.insert(tk.END, f"الجنس: {result[3]}\n")
                        results_text.insert(tk.END, f"نوع العينة: {result[4]}\n")
                        results_text.insert(tk.END, f"تاريخ الاستلام: {result[5]}\n")
                        results_text.insert(tk.END, "-" * 40 + "\n")
                else:
                    results_text.insert('1.0', "لم يتم العثور على نتائج")

                results_text.config(state=tk.DISABLED)

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

        tk.Button(search_window, text="بحث", font=('Arial', 10, 'bold'),
                 bg='#3498db', fg='white', relief='raised', bd=3,
                 command=perform_search).grid(row=3, column=0, columnspan=2, pady=10)

    def import_excel(self):
        """استيراد بيانات من Excel"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Excel",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            # قراءة ملف Excel
            df = pd.read_excel(file_path)

            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['الاسم', 'العمر', 'الجنس', 'العنوان', 'نوع العينة',
                              'رقم الهاتف', 'جهة الإرسال', 'تاريخ سحب العينة']

            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                messagebox.showerror("خطأ", f"الأعمدة التالية مفقودة: {', '.join(missing_columns)}")
                return

            # استيراد البيانات
            imported_count = 0
            errors = []

            for index, row in df.iterrows():
                try:
                    self.cursor.execute('''
                        INSERT INTO patients (name, age, gender, address, sample_type, phone,
                                            sender_org, sample_date, passport_no, receipt_no, tests)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        row['الاسم'],
                        int(row['العمر']),
                        row['الجنس'],
                        row['العنوان'],
                        row['نوع العينة'],
                        str(row['رقم الهاتف']),
                        row['جهة الإرسال'],
                        row['تاريخ سحب العينة'],
                        row.get('رقم الجواز', ''),
                        row.get('رقم الوصل', ''),
                        row.get('التحاليل', '')
                    ))

                    imported_count += 1

                    # طباعة استيكر لكل عينة
                    self.print_sticker()

                except Exception as e:
                    errors.append(f"الصف {index + 1}: {str(e)}")

            self.conn.commit()

            # تحديث الجدول
            self.refresh_patients_table()

            # عرض النتائج
            message = f"تم استيراد {imported_count} عينة بنجاح"
            if errors:
                message += f"\n\nأخطاء ({len(errors)}):\n" + "\n".join(errors[:5])
                if len(errors) > 5:
                    message += f"\n... و {len(errors) - 5} أخطاء أخرى"

            messagebox.showinfo("نتائج الاستيراد", message)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء استيراد الملف: {str(e)}")
    
    def show_work_tab(self):
        """عرض تبويب العمل"""
        self.clear_content_frame()

        # عنوان التبويب
        title = tk.Label(self.content_frame, text="العمل",
                        font=('Arial', 16, 'bold'), bg='#ecf0f1')
        title.pack(pady=10)

        # إطار البحث
        search_frame = tk.Frame(self.content_frame, bg='#ecf0f1')
        search_frame.pack(fill=tk.X, padx=20, pady=10)

        # تحديد فترة البحث مع تقويم
        tk.Label(search_frame, text="من تاريخ:", font=('Arial', 10, 'bold'),
                bg='#f8f9fa', fg='#2c3e50').grid(row=0, column=0, padx=5, pady=5, sticky='e')

        start_date_frame = tk.Frame(search_frame, bg='#f8f9fa')
        start_date_frame.grid(row=0, column=1, padx=5, pady=5)

        self.work_start_date = tk.StringVar()
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        self.work_start_date.set(current_date)

        start_entry = tk.Entry(start_date_frame, textvariable=self.work_start_date,
                              width=12, relief='solid', bd=1)
        start_entry.pack(side=tk.LEFT)
        tk.Button(start_date_frame, text="📅", font=('Arial', 8), bg='#3498db', fg='white',
                 relief='flat', bd=0, cursor='hand2',
                 command=lambda: DatePicker(self.root, self.work_start_date,
                                          "اختيار تاريخ البداية").show_calendar()).pack(side=tk.LEFT, padx=(2, 0))

        tk.Label(search_frame, text="إلى تاريخ:", font=('Arial', 10, 'bold'),
                bg='#f8f9fa', fg='#2c3e50').grid(row=0, column=2, padx=5, pady=5, sticky='e')

        end_date_frame = tk.Frame(search_frame, bg='#f8f9fa')
        end_date_frame.grid(row=0, column=3, padx=5, pady=5)

        self.work_end_date = tk.StringVar()
        self.work_end_date.set(current_date)

        end_entry = tk.Entry(end_date_frame, textvariable=self.work_end_date,
                            width=12, relief='solid', bd=1)
        end_entry.pack(side=tk.LEFT)
        tk.Button(end_date_frame, text="📅", font=('Arial', 8), bg='#3498db', fg='white',
                 relief='flat', bd=0, cursor='hand2',
                 command=lambda: DatePicker(self.root, self.work_end_date,
                                          "اختيار تاريخ النهاية").show_calendar()).pack(side=tk.LEFT, padx=(2, 0))

        tk.Button(search_frame, text="بحث", font=('Arial', 10, 'bold'),
                 bg='#3498db', fg='white', relief='raised', bd=3,
                 command=self.search_work_samples).grid(row=0, column=4, padx=10, pady=5)

        # معلومات الوجبة
        batch_frame = tk.Frame(self.content_frame, bg='#ecf0f1')
        batch_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(batch_frame, text="رقم الوجبة:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5)
        self.batch_no_var = tk.StringVar()
        tk.Label(batch_frame, textvariable=self.batch_no_var, font=('Arial', 10),
                bg='white', relief='sunken', width=10).grid(row=0, column=1, padx=5, pady=5)

        tk.Label(batch_frame, text="الفني المسؤول:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=2, padx=5, pady=5)
        self.work_technician = tk.StringVar()
        tech_combo = ttk.Combobox(batch_frame, textvariable=self.work_technician, width=20)
        tech_combo.grid(row=0, column=3, padx=5, pady=5)
        self.update_technicians_combo(tech_combo)

        tk.Button(batch_frame, text="إنشاء وجبة جديدة", font=('Arial', 10, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=3,
                 command=self.create_new_batch).grid(row=0, column=4, padx=10, pady=5)

        tk.Button(batch_frame, text="إرسال إلى النتائج", font=('Arial', 10, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=3,
                 command=self.send_to_results).grid(row=0, column=5, padx=10, pady=5)

        # جدول العينات
        self.create_work_samples_table()

        # تحديث رقم الوجبة التلقائي
        self.update_batch_number()

    def update_technicians_combo(self, combo):
        """تحديث قائمة الفنيين"""
        self.cursor.execute("SELECT value FROM settings WHERE category = 'technicians'")
        technicians = [row[0] for row in self.cursor.fetchall()]
        combo['values'] = technicians

    def search_work_samples(self):
        """البحث عن العينات للعمل"""
        start_date = self.work_start_date.get()
        end_date = self.work_end_date.get()

        if not start_date or not end_date:
            messagebox.showerror("خطأ", "يرجى تحديد فترة البحث")
            return

        # جلب العينات من قاعدة البيانات
        self.cursor.execute('''
            SELECT national_id, name, sample_type, receive_date, tests
            FROM patients
            WHERE receive_date BETWEEN ? AND ?
            ORDER BY receive_date
        ''', (start_date, end_date))

        samples = self.cursor.fetchall()

        # تحديث جدول العينات
        self.refresh_work_samples_table(samples)

    def create_work_samples_table(self):
        """إنشاء جدول عينات العمل"""
        table_frame = tk.Frame(self.content_frame, bg='#ecf0f1')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        columns = ('الرقم الوطني', 'الاسم', 'نوع العينة', 'تاريخ الاستلام', 'التحاليل')
        self.work_samples_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.work_samples_tree.heading(col, text=col)
            self.work_samples_tree.column(col, width=150, anchor='center')

        scrollbar_work = ttk.Scrollbar(table_frame, orient=tk.VERTICAL,
                                      command=self.work_samples_tree.yview)
        self.work_samples_tree.configure(yscrollcommand=scrollbar_work.set)

        self.work_samples_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_work.pack(side=tk.RIGHT, fill=tk.Y)

    def refresh_work_samples_table(self, samples=None):
        """تحديث جدول عينات العمل"""
        for item in self.work_samples_tree.get_children():
            self.work_samples_tree.delete(item)

        if samples:
            for sample in samples:
                self.work_samples_tree.insert('', tk.END, values=sample)

    def update_batch_number(self):
        """تحديث رقم الوجبة التلقائي"""
        self.cursor.execute("SELECT MAX(batch_no) FROM batches")
        result = self.cursor.fetchone()
        next_batch = (result[0] or 0) + 1
        self.batch_no_var.set(str(next_batch))

    def create_new_batch(self):
        """إنشاء وجبة جديدة"""
        if not self.work_technician.get():
            messagebox.showerror("خطأ", "يرجى اختيار الفني المسؤول")
            return

        try:
            batch_no = int(self.batch_no_var.get())
            self.cursor.execute('''
                INSERT INTO batches (batch_no, start_date, end_date, technician)
                VALUES (?, ?, ?, ?)
            ''', (batch_no, self.work_start_date.get(), self.work_end_date.get(),
                  self.work_technician.get()))

            self.conn.commit()
            messagebox.showinfo("نجح", f"تم إنشاء الوجبة رقم {batch_no} بنجاح")
            self.update_batch_number()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الوجبة: {str(e)}")

    def send_to_results(self):
        """إرسال الوجبة إلى النتائج"""
        batch_no = self.batch_no_var.get()
        if not batch_no:
            messagebox.showerror("خطأ", "لا توجد وجبة محددة")
            return

        try:
            self.cursor.execute('''
                UPDATE batches SET status = 'sent_to_results'
                WHERE batch_no = ?
            ''', (int(batch_no),))

            self.conn.commit()
            messagebox.showinfo("نجح", f"تم إرسال الوجبة رقم {batch_no} إلى النتائج")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الإرسال: {str(e)}")
    
    def show_results_tab(self):
        """عرض تبويب النتائج"""
        self.clear_content_frame()

        # عنوان التبويب مع تصميم حديث
        title_frame = tk.Frame(self.content_frame, bg='#2c3e50', height=60)
        title_frame.pack(fill=tk.X, padx=0, pady=0)
        title_frame.pack_propagate(False)

        title = tk.Label(title_frame, text="📊 النتائج",
                        font=('Arial', 18, 'bold'), bg='#2c3e50', fg='white')
        title.pack(expand=True)

        # إطار رئيسي
        main_container = tk.Frame(self.content_frame, bg='#f8f9fa')
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # إطار البحث بالوجبة
        search_container = tk.LabelFrame(main_container, text="البحث عن الوجبة",
                                       font=('Arial', 12, 'bold'), bg='white',
                                       fg='#2c3e50', relief='ridge', bd=2)
        search_container.pack(fill=tk.X, pady=(0, 15))

        search_frame = tk.Frame(search_container, bg='white')
        search_frame.pack(fill=tk.X, padx=15, pady=10)

        tk.Label(search_frame, text="رقم الوجبة:", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=0, padx=5, pady=5, sticky='e')

        self.results_batch_no = tk.StringVar()
        batch_entry = tk.Entry(search_frame, textvariable=self.results_batch_no,
                              font=('Arial', 10), width=15, relief='solid', bd=1)
        batch_entry.grid(row=0, column=1, padx=5, pady=5)

        tk.Button(search_frame, text="🔍 عرض الوجبة", font=('Arial', 10, 'bold'),
                 bg='#3498db', fg='white', relief='raised', bd=3, cursor='hand2',
                 command=self.load_batch_results).grid(row=0, column=2, padx=10, pady=5)

        tk.Button(search_frame, text="📋 عرض جميع الوجبات", font=('Arial', 10, 'bold'),
                 bg='#9b59b6', fg='white', relief='raised', bd=3, cursor='hand2',
                 command=self.show_all_batches).grid(row=0, column=3, padx=5, pady=5)

        # جدول العينات للنتائج
        self.create_results_table(main_container)

        # إطار إدخال النتائج
        input_container = tk.LabelFrame(main_container, text="إدخال النتيجة",
                                      font=('Arial', 12, 'bold'), bg='white',
                                      fg='#2c3e50', relief='ridge', bd=2)
        input_container.pack(fill=tk.X, pady=(15, 0))

        input_frame = tk.Frame(input_container, bg='white')
        input_frame.pack(fill=tk.X, padx=15, pady=10)

        # الصف الأول - النتيجة الفردية
        tk.Label(input_frame, text="النتيجة:", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=0, column=0, padx=5, pady=5, sticky='e')

        self.result_value = tk.StringVar()
        result_combo = ttk.Combobox(input_frame, textvariable=self.result_value,
                                   values=['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND'],
                                   width=15, state='readonly')
        result_combo.grid(row=0, column=1, padx=5, pady=5)

        tk.Button(input_frame, text="💾 حفظ/تحديث النتيجة", font=('Arial', 10, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=3, cursor='hand2',
                 command=self.save_result).grid(row=0, column=2, padx=10, pady=5)

        # خط فاصل
        separator = tk.Frame(input_frame, bg='#bdc3c7', height=1)
        separator.grid(row=1, column=0, columnspan=4, sticky='ew', padx=5, pady=10)

        # الصف الثاني - النتيجة المتعددة
        tk.Label(input_frame, text="نتيجة متعددة:", font=('Arial', 10, 'bold'),
                bg='white', fg='#2c3e50').grid(row=2, column=0, padx=5, pady=5, sticky='e')

        self.bulk_result_value = tk.StringVar()
        bulk_result_combo = ttk.Combobox(input_frame, textvariable=self.bulk_result_value,
                                        values=['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND'],
                                        width=15, state='readonly')
        bulk_result_combo.grid(row=2, column=1, padx=5, pady=5)

        tk.Button(input_frame, text="📋 حفظ للمتعدد", font=('Arial', 10, 'bold'),
                 bg='#3498db', fg='white', relief='raised', bd=3, cursor='hand2',
                 command=self.save_bulk_result).grid(row=2, column=2, padx=10, pady=5)

        tk.Button(input_frame, text="✅ تحديد الكل", font=('Arial', 9, 'bold'),
                 bg='#f39c12', fg='white', relief='raised', bd=2, cursor='hand2',
                 command=self.select_all_results).grid(row=2, column=3, padx=5, pady=5)

        # معلومات إضافية
        info_label = tk.Label(input_frame,
                             text="💡 اختر تحليل من الجدول أعلاه، ستظهر النتيجة الحالية (إن وجدت) ويمكنك تغييرها في أي وقت",
                             font=('Arial', 9), bg='white', fg='#7f8c8d')
        info_label.grid(row=3, column=0, columnspan=4, pady=(10, 0))

        # عداد التحاليل المحددة
        self.selected_count_label = tk.Label(input_frame, text="لم يتم تحديد أي تحليل",
                                           font=('Arial', 9, 'bold'), bg='white', fg='#e74c3c')
        self.selected_count_label.grid(row=4, column=0, columnspan=4, pady=(5, 0))

        # ربط حدث التحديد لتحديث العداد
        self.results_tree.bind('<<TreeviewSelect>>', self.update_selection_count)

    def create_results_table(self, parent):
        """إنشاء جدول النتائج المحسن"""
        # إطار الجدول مع عنوان
        table_container = tk.LabelFrame(parent, text="نتائج التحاليل",
                                       font=('Arial', 12, 'bold'), bg='white',
                                       fg='#2c3e50', relief='ridge', bd=2)
        table_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        columns = ('الرقم الوطني', 'الاسم', 'نوع العينة', 'التحليل', 'النتيجة', 'تاريخ النتيجة')
        # تمكين التحديد المتعدد
        self.results_tree = ttk.Treeview(table_container, columns=columns, show='headings',
                                        height=12, selectmode='extended')

        # تعيين عناوين الأعمدة مع تحسين العرض
        column_widths = {'الرقم الوطني': 100, 'الاسم': 150, 'نوع العينة': 120,
                        'التحليل': 120, 'النتيجة': 100, 'تاريخ النتيجة': 120}

        for col in columns:
            self.results_tree.heading(col, text=col, anchor='center')
            self.results_tree.column(col, width=column_widths.get(col, 100),
                                    anchor='center', minwidth=50)

        # شريط التمرير
        scrollbar_results = ttk.Scrollbar(table_container, orient=tk.VERTICAL,
                                         command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar_results.set)

        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar_results.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

        # إضافة ألوان متناوبة للصفوف
        self.results_tree.tag_configure('oddrow', background='#f8f9fa')
        self.results_tree.tag_configure('evenrow', background='white')
        self.results_tree.tag_configure('no_result', background='#fff3cd', foreground='#856404')
        self.results_tree.tag_configure('positive', background='#f8d7da', foreground='#721c24')
        self.results_tree.tag_configure('negative', background='#d4edda', foreground='#155724')

        # ربط حدث التحديد
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_select)

    def show_all_batches(self):
        """عرض جميع الوجبات المتاحة"""
        try:
            self.cursor.execute("SELECT batch_no, start_date, end_date, technician FROM batches ORDER BY batch_no DESC")
            batches = self.cursor.fetchall()

            if not batches:
                messagebox.showinfo("معلومات", "لا توجد وجبات متاحة. يرجى إنشاء وجبة من تبويب العمل أولاً.")
                return

            # إنشاء نافذة عرض الوجبات
            batches_window = tk.Toplevel(self.root)
            batches_window.title("الوجبات المتاحة")
            batches_window.geometry("600x400")
            batches_window.configure(bg='white')

            tk.Label(batches_window, text="الوجبات المتاحة",
                    font=('Arial', 14, 'bold'), bg='white').pack(pady=10)

            # جدول الوجبات
            columns = ('رقم الوجبة', 'تاريخ البداية', 'تاريخ النهاية', 'الفني')
            batches_tree = ttk.Treeview(batches_window, columns=columns, show='headings', height=15)

            for col in columns:
                batches_tree.heading(col, text=col)
                batches_tree.column(col, width=120, anchor='center')

            for batch in batches:
                batches_tree.insert('', tk.END, values=batch)

            batches_tree.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            # زر اختيار الوجبة
            def select_batch():
                selection = batches_tree.selection()
                if selection:
                    item = batches_tree.item(selection[0])
                    batch_no = item['values'][0]
                    self.results_batch_no.set(str(batch_no))
                    batches_window.destroy()
                    self.load_batch_results()
                else:
                    messagebox.showerror("خطأ", "يرجى اختيار وجبة")

            tk.Button(batches_window, text="اختيار الوجبة", font=('Arial', 10, 'bold'),
                     bg='#27ae60', fg='white', command=select_batch).pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عرض الوجبات: {str(e)}")

    def load_batch_results(self):
        """تحميل نتائج الوجبة مع دعم التحاليل المتعددة"""
        batch_no = self.results_batch_no.get()
        if not batch_no:
            messagebox.showerror("خطأ", "يرجى إدخال رقم الوجبة أو اختيارها من قائمة الوجبات")
            return

        try:
            # مسح الجدول أولاً
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            # التحقق من وجود الوجبة أولاً
            try:
                batch_no_int = int(batch_no)
            except ValueError:
                messagebox.showerror("خطأ", "رقم الوجبة يجب أن يكون رقماً صحيحاً")
                return

            self.cursor.execute("SELECT id, start_date, end_date, technician FROM batches WHERE batch_no = ?", (batch_no_int,))
            batch_info = self.cursor.fetchone()

            if not batch_info:
                messagebox.showwarning("تحذير", f"الوجبة رقم {batch_no} غير موجودة. يرجى إنشاؤها من تبويب العمل أولاً.")
                return

            batch_id, start_date, end_date, technician = batch_info

            # جلب المرضى في الوجبة حسب التاريخ - عرض عينات الوجبة فقط
            if start_date and end_date:
                # البحث عن العينات التي تنتمي لهذه الوجبة بالضبط
                self.cursor.execute('''
                    SELECT id, national_id, name, sample_type, tests, sample_date
                    FROM patients
                    WHERE sample_date BETWEEN ? AND ?
                    ORDER BY national_id
                ''', (start_date, end_date))
            else:
                # إذا لم تكن التواريخ محددة، عرض رسالة خطأ
                messagebox.showerror("خطأ", f"الوجبة رقم {batch_no} لا تحتوي على تواريخ صحيحة")
                return

            patients = self.cursor.fetchall()

            if not patients:
                messagebox.showinfo("معلومات", f"لا توجد عينات في الوجبة رقم {batch_no}\n"
                                              f"الفترة: {start_date} إلى {end_date}\n"
                                              f"تأكد من أن العينات تم إدخالها في هذه الفترة")
                return

            row_count = 0
            for patient in patients:
                patient_id, national_id, name, sample_type, tests_str, sample_date = patient

                if tests_str:
                    # تقسيم التحاليل
                    tests = [test.strip() for test in tests_str.split(',') if test.strip()]

                    for test in tests:
                        # البحث عن النتيجة الموجودة لهذا التحليل
                        self.cursor.execute('''
                            SELECT result, result_date FROM results
                            WHERE patient_id = ? AND test_name = ? AND batch_id = ?
                        ''', (patient_id, test, batch_id))

                        result_data = self.cursor.fetchone()

                        if result_data:
                            result, result_date = result_data
                        else:
                            result = 'لم يتم إدخال النتيجة'
                            result_date = ''

                        # تحديد اللون حسب النتيجة
                        if result == 'لم يتم إدخال النتيجة':
                            tag = 'no_result'
                        elif result.lower() == 'positive':
                            tag = 'positive'
                        elif result.lower() == 'negative':
                            tag = 'negative'
                        else:
                            tag = 'evenrow' if row_count % 2 == 0 else 'oddrow'

                        # إدراج الصف
                        item_id = self.results_tree.insert('', tk.END,
                                                          values=(national_id, name, sample_type, test, result, result_date),
                                                          tags=(tag, str(patient_id), test, str(batch_id)))
                        row_count += 1
                else:
                    # إذا لم تكن هناك تحاليل محددة
                    self.results_tree.insert('', tk.END,
                                            values=(national_id, name, sample_type, 'غير محدد', 'لم يتم إدخال النتيجة', ''),
                                            tags=('no_result', str(patient_id), 'غير محدد', str(batch_id)))
                    row_count += 1

            # حساب عدد العينات الفريدة
            unique_patients = len(patients)

            # رسالة تأكيد مفصلة
            messagebox.showinfo("نجح", f"تم تحميل الوجبة رقم {batch_no} بنجاح\n"
                                     f"عدد العينات: {unique_patients}\n"
                                     f"عدد التحاليل: {row_count}\n"
                                     f"الفترة: {start_date} إلى {end_date}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل النتائج: {str(e)}")

    def update_selection_count(self, event=None):
        """تحديث عداد التحاليل المحددة"""
        selection = self.results_tree.selection()
        count = len(selection)

        if count == 0:
            self.selected_count_label.config(text="لم يتم تحديد أي تحليل", fg='#e74c3c')
        elif count == 1:
            self.selected_count_label.config(text="تم تحديد تحليل واحد", fg='#27ae60')
        else:
            self.selected_count_label.config(text=f"تم تحديد {count} تحليل", fg='#3498db')

    def select_all_results(self):
        """تحديد جميع التحاليل"""
        # تحديد جميع العناصر في الجدول
        all_items = self.results_tree.get_children()
        for item in all_items:
            self.results_tree.selection_add(item)

        # تحديث العداد
        self.update_selection_count()

        messagebox.showinfo("تم", f"تم تحديد {len(all_items)} تحليل")

    def on_result_select(self, event):
        """عند تحديد تحليل في جدول النتائج - عرض النتيجة الحالية للتعديل"""
        selection = self.results_tree.selection()
        if len(selection) == 1:
            item = self.results_tree.item(selection[0])
            current_result = item['values'][4]  # النتيجة في العمود الخامس

            # عرض النتيجة الحالية في القائمة المنسدلة للتعديل
            if current_result != 'لم يتم إدخال النتيجة':
                self.result_value.set(current_result)
            else:
                self.result_value.set("")  # مسح القائمة إذا لم تكن هناك نتيجة

    def save_result(self):
        """حفظ أو تحديث النتيجة للتحليل المحدد"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showerror("خطأ", "يرجى تحديد تحليل")
            return

        if not self.result_value.get():
            messagebox.showerror("خطأ", "يرجى اختيار النتيجة")
            return

        try:
            # الحصول على معلومات التحليل المحدد
            item = self.results_tree.item(selection[0])
            values = item['values']
            tags = item['tags']

            if len(tags) < 4:
                messagebox.showerror("خطأ", "خطأ في بيانات التحليل")
                return

            patient_id = int(tags[1])  # معرف المريض
            test_name = tags[2]        # اسم التحليل
            batch_id = int(tags[3])    # معرف الوجبة
            current_result = values[4]  # النتيجة الحالية
            new_result = self.result_value.get()

            # التحقق من وجود نتيجة سابقة وتحديد نوع العملية
            if current_result != 'لم يتم إدخال النتيجة':
                # تحديث نتيجة موجودة
                action_message = f"تم تحديث نتيجة تحليل '{test_name}' من '{current_result}' إلى '{new_result}' بنجاح"
            else:
                # إضافة نتيجة جديدة
                action_message = f"تم حفظ نتيجة تحليل '{test_name}': '{new_result}' بنجاح"

            # إدراج أو تحديث النتيجة للتحليل المحدد
            self.cursor.execute('''
                INSERT OR REPLACE INTO results (patient_id, batch_id, test_name, result, result_date)
                VALUES (?, ?, ?, ?, DATE('now'))
            ''', (patient_id, batch_id, test_name, new_result))

            self.conn.commit()

            # تحديث الجدول
            self.load_batch_results()

            # مسح النتيجة المحددة
            self.result_value.set("")

            messagebox.showinfo("نجح", action_message)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ النتيجة: {str(e)}")

    def save_bulk_result(self):
        """حفظ النتيجة لعدة تحاليل محددة"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showerror("خطأ", "يرجى تحديد التحاليل المطلوبة")
            return

        if not self.bulk_result_value.get():
            messagebox.showerror("خطأ", "يرجى اختيار النتيجة")
            return

        # تأكيد العملية
        count = len(selection)
        if not messagebox.askyesno("تأكيد",
                                  f"هل تريد حفظ النتيجة '{self.bulk_result_value.get()}' لـ {count} تحليل؟"):
            return

        try:
            saved_count = 0
            failed_count = 0

            for item_id in selection:
                try:
                    # الحصول على معلومات التحليل المحدد
                    item = self.results_tree.item(item_id)
                    tags = item['tags']

                    if len(tags) < 4:
                        failed_count += 1
                        continue

                    patient_id = int(tags[1])  # معرف المريض
                    test_name = tags[2]        # اسم التحليل
                    batch_id = int(tags[3])    # معرف الوجبة

                    # إدراج أو تحديث النتيجة للتحليل المحدد
                    self.cursor.execute('''
                        INSERT OR REPLACE INTO results (patient_id, batch_id, test_name, result, result_date)
                        VALUES (?, ?, ?, ?, DATE('now'))
                    ''', (patient_id, batch_id, test_name, self.bulk_result_value.get()))

                    saved_count += 1

                except Exception as e:
                    print(f"خطأ في حفظ التحليل: {e}")
                    failed_count += 1

            self.conn.commit()

            # تحديث الجدول
            self.load_batch_results()

            # مسح النتيجة المحددة
            self.bulk_result_value.set("")

            # رسالة النتيجة
            if failed_count == 0:
                messagebox.showinfo("نجح", f"تم حفظ النتيجة '{self.bulk_result_value.get()}' لـ {saved_count} تحليل بنجاح")
            else:
                messagebox.showwarning("تحذير",
                                     f"تم حفظ {saved_count} تحليل بنجاح\nفشل في حفظ {failed_count} تحليل")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ النتائج المتعددة: {str(e)}")


    
    def show_reports_tab(self):
        """عرض تبويب التقارير والإحصائيات"""
        self.clear_content_frame()

        # عنوان التبويب
        title = tk.Label(self.content_frame, text="التقارير والإحصائيات",
                        font=('Arial', 16, 'bold'), bg='#ecf0f1')
        title.pack(pady=10)

        # إطار الفلترة
        filter_frame = tk.LabelFrame(self.content_frame, text="فلترة البيانات",
                                    font=('Arial', 12, 'bold'), bg='#ecf0f1')
        filter_frame.pack(fill=tk.X, padx=20, pady=10)

        # الفترة الزمنية مع تقويم
        tk.Label(filter_frame, text="من تاريخ:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1', fg='#2c3e50').grid(row=0, column=0, padx=5, pady=5, sticky='e')

        start_frame = tk.Frame(filter_frame, bg='#ecf0f1')
        start_frame.grid(row=0, column=1, padx=5, pady=5)

        self.report_start_date = tk.StringVar()

        start_entry = tk.Entry(start_frame, textvariable=self.report_start_date,
                              width=12, relief='solid', bd=1)
        start_entry.pack(side=tk.LEFT)
        tk.Button(start_frame, text="📅", font=('Arial', 8), bg='#3498db', fg='white',
                 relief='flat', bd=0, cursor='hand2',
                 command=lambda: DatePicker(self.root, self.report_start_date,
                                          "اختيار تاريخ البداية").show_calendar()).pack(side=tk.LEFT, padx=(2, 0))

        tk.Label(filter_frame, text="إلى تاريخ:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1', fg='#2c3e50').grid(row=0, column=2, padx=5, pady=5, sticky='e')

        end_frame = tk.Frame(filter_frame, bg='#ecf0f1')
        end_frame.grid(row=0, column=3, padx=5, pady=5)

        self.report_end_date = tk.StringVar()

        end_entry = tk.Entry(end_frame, textvariable=self.report_end_date,
                            width=12, relief='solid', bd=1)
        end_entry.pack(side=tk.LEFT)
        tk.Button(end_frame, text="📅", font=('Arial', 8), bg='#3498db', fg='white',
                 relief='flat', bd=0, cursor='hand2',
                 command=lambda: DatePicker(self.root, self.report_end_date,
                                          "اختيار تاريخ النهاية").show_calendar()).pack(side=tk.LEFT, padx=(2, 0))

        # الاسم
        tk.Label(filter_frame, text="الاسم:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=1, column=0, padx=5, pady=5, sticky='e')
        self.report_name = tk.StringVar()
        tk.Entry(filter_frame, textvariable=self.report_name, width=20).grid(
            row=1, column=1, padx=5, pady=5)

        # نوع العينة
        tk.Label(filter_frame, text="نوع العينة:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=1, column=2, padx=5, pady=5, sticky='e')
        self.report_sample_type = tk.StringVar()
        sample_combo = ttk.Combobox(filter_frame, textvariable=self.report_sample_type, width=15)
        sample_combo.grid(row=1, column=3, padx=5, pady=5)
        self.update_combobox_values(sample_combo, "sample_type")

        # الجنس
        tk.Label(filter_frame, text="الجنس:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=2, column=0, padx=5, pady=5, sticky='e')
        self.report_gender = tk.StringVar()
        gender_combo = ttk.Combobox(filter_frame, textvariable=self.report_gender,
                                   values=['', 'M', 'F'], width=15)
        gender_combo.grid(row=2, column=1, padx=5, pady=5)

        # جهة الإرسال
        tk.Label(filter_frame, text="جهة الإرسال:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=2, column=2, padx=5, pady=5, sticky='e')
        self.report_sender = tk.StringVar()
        sender_combo = ttk.Combobox(filter_frame, textvariable=self.report_sender, width=15)
        sender_combo.grid(row=2, column=3, padx=5, pady=5)
        self.update_combobox_values(sender_combo, "sender_org")

        # أزرار العمليات
        buttons_frame = tk.Frame(filter_frame, bg='#ecf0f1')
        buttons_frame.grid(row=3, column=0, columnspan=4, pady=10)

        button_style = {
            'font': ('Arial', 10, 'bold'),
            'relief': 'raised',
            'bd': 3,
            'width': 12,
            'height': 2
        }

        tk.Button(buttons_frame, text="بحث", bg='#3498db', fg='white',
                 command=self.search_reports, **button_style).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="معاينة التقرير", bg='#9b59b6', fg='white',
                 command=self.preview_report, **button_style).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="طباعة التقرير", bg='#27ae60', fg='white',
                 command=self.print_report, **button_style).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="تصدير Excel", bg='#f39c12', fg='white',
                 command=self.export_excel, **button_style).pack(side=tk.LEFT, padx=5)

        # جدول النتائج
        self.create_reports_table()

        # تحميل البيانات تلقائياً عند فتح التبويب
        self.load_all_reports_data()

    def create_reports_table(self):
        """إنشاء جدول التقارير مع الترتيب الصحيح للأعمدة"""
        table_frame = tk.Frame(self.content_frame, bg='#ecf0f1')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # ترتيب الأعمدة الصحيح: الرقم الوطني، الاسم، العمر، الجنس، نوع العينة، جهة الإرسال، التحليل، النتيجة، تاريخ النتيجة
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة',
                  'جهة الإرسال', 'التحليل', 'النتيجة', 'تاريخ النتيجة')
        self.reports_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعيين عرض مناسب لكل عمود
        column_widths = {
            'الرقم الوطني': 100,
            'الاسم': 150,
            'العمر': 60,
            'الجنس': 60,
            'نوع العينة': 120,
            'جهة الإرسال': 150,
            'التحليل': 120,
            'النتيجة': 100,
            'تاريخ النتيجة': 120
        }

        for col in columns:
            self.reports_tree.heading(col, text=col, anchor='center')
            self.reports_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # شريط التمرير العمودي والأفقي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL,
                                   command=self.reports_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL,
                                   command=self.reports_tree.xview)

        self.reports_tree.configure(yscrollcommand=v_scrollbar.set,
                                   xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.reports_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # تكوين التوسع
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

    def load_all_reports_data(self):
        """تحميل جميع البيانات في جدول التقارير"""
        try:
            print("📊 تحميل جميع البيانات في جدول التقارير...")

            # استعلام لجلب جميع البيانات
            query = '''
                SELECT p.national_id, p.name, p.age, p.gender, p.sample_type,
                       p.sender_org,
                       COALESCE(r.test_name, 'غير محدد') as test_name,
                       COALESCE(r.result, 'لا توجد نتيجة') as result,
                       COALESCE(r.result_date, '') as result_date
                FROM patients p
                LEFT JOIN results r ON p.id = r.patient_id
                ORDER BY p.receive_date DESC, p.national_id, r.test_name
            '''

            self.cursor.execute(query)
            results = self.cursor.fetchall()

            # مسح البيانات الحالية
            for item in self.reports_tree.get_children():
                self.reports_tree.delete(item)

            # إضافة البيانات الجديدة
            for result in results:
                self.reports_tree.insert('', tk.END, values=result)

            print(f"✅ تم تحميل {len(results)} سجل في جدول التقارير")

            # تحديث شريط الحالة إذا كان موجوداً
            if hasattr(self, 'status_label'):
                self.status_label.config(text=f"تم تحميل {len(results)} سجل في التقارير")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات التقارير: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def search_reports(self):
        """البحث في التقارير مع دعم التحاليل المتعددة"""
        # بناء استعلام SQL ديناميكي للتحاليل المتعددة
        # ترتيب الأعمدة: الرقم الوطني، الاسم، العمر، الجنس، نوع العينة، جهة الإرسال، التحليل، النتيجة، تاريخ النتيجة
        query = '''
            SELECT p.national_id, p.name, p.age, p.gender, p.sample_type,
                   p.sender_org,
                   COALESCE(r.test_name, 'غير محدد') as test_name,
                   COALESCE(r.result, 'لا توجد نتيجة') as result,
                   COALESCE(r.result_date, '') as result_date
            FROM patients p
            LEFT JOIN results r ON p.id = r.patient_id
            WHERE 1=1
        '''
        params = []

        # إضافة شروط البحث
        if self.report_start_date.get():
            query += " AND p.receive_date >= ?"
            params.append(self.report_start_date.get())

        if self.report_end_date.get():
            query += " AND p.receive_date <= ?"
            params.append(self.report_end_date.get())

        if self.report_name.get():
            query += " AND p.name LIKE ?"
            params.append(f"%{self.report_name.get()}%")

        if self.report_sample_type.get():
            query += " AND p.sample_type = ?"
            params.append(self.report_sample_type.get())

        if self.report_gender.get():
            query += " AND p.gender = ?"
            params.append(self.report_gender.get())

        if self.report_sender.get():
            query += " AND p.sender_org = ?"
            params.append(self.report_sender.get())

        query += " ORDER BY p.receive_date DESC, p.national_id, r.test_name"

        try:
            self.cursor.execute(query, params)
            results = self.cursor.fetchall()

            # تحديث الجدول
            for item in self.reports_tree.get_children():
                self.reports_tree.delete(item)

            for result in results:
                self.reports_tree.insert('', tk.END, values=result)

            messagebox.showinfo("نتائج البحث", f"تم العثور على {len(results)} نتيجة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def preview_report(self):
        """معاينة التقرير"""
        # التحقق من وجود بيانات
        if not self.reports_tree.get_children():
            messagebox.showwarning("تحذير", "لا توجد بيانات لمعاينة التقرير")
            return

        # إنشاء نافذة المعاينة
        preview_window = tk.Toplevel(self.root)
        preview_window.title("معاينة التقرير")
        preview_window.geometry("800x600")
        preview_window.configure(bg='white')

        # إطار المعاينة
        preview_frame = tk.Frame(preview_window, bg='white')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # التحقق من إعداد إظهار الشعار وعرضه في أعلى منتصف النافذة
        try:
            self.cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'show_logo'")
            show_logo_result = self.cursor.fetchone()
            show_logo = show_logo_result[0].lower() == 'true' if show_logo_result else True

            if show_logo:
                # إطار الشعار في أعلى منتصف النافذة
                logo_frame = tk.Frame(preview_frame, bg='white')
                logo_frame.pack(pady=(0, 10))

                # محاولة عرض الشعار
                try:
                    self.cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'logo_path'")
                    logo_result = self.cursor.fetchone()
                    logo_path = logo_result[0] if logo_result else 'ministry_logo.png'

                    if os.path.exists(logo_path):
                        from PIL import Image, ImageTk
                        img = Image.open(logo_path)
                        img.thumbnail((80, 80), Image.Resampling.LANCZOS)
                        photo = ImageTk.PhotoImage(img)

                        logo_label = tk.Label(logo_frame, image=photo, bg='white')
                        logo_label.image = photo  # الاحتفاظ بمرجع للصورة
                        logo_label.pack()
                    else:
                        tk.Label(logo_frame, text="🏥 شعار الوزارة",
                                font=('Arial', 12, 'bold'), bg='white', fg='#2c3e50').pack()
                except Exception as e:
                    tk.Label(logo_frame, text="🏥 شعار الوزارة",
                            font=('Arial', 12, 'bold'), bg='white', fg='#2c3e50').pack()
        except Exception as e:
            print(f"خطأ في عرض الشعار: {e}")

        # عنوان التقرير
        header_frame = tk.Frame(preview_frame, bg='white')
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # العنوان العربي
        arabic_header = tk.Label(header_frame,
                               text="جمهورية العراق\nوزارة الصحة\nقسم الصحة العامة\nمختبر الصحة العامة\nوحدة الفايروسات",
                               font=('Arial', 12, 'bold'), bg='white', fg='#2c3e50',
                               justify=tk.CENTER)
        arabic_header.pack(side=tk.RIGHT, padx=20)

        # العنوان الإنجليزي
        english_header = tk.Label(header_frame,
                                text="Republic of Iraq\nMinistry of Health\nPublic Health Department\nPublic Health Laboratory\nVirus Unit",
                                font=('Arial', 12, 'bold'), bg='white', fg='#2c3e50',
                                justify=tk.CENTER)
        english_header.pack(side=tk.LEFT, padx=20)

        # عنوان التقرير
        report_title = tk.Label(preview_frame, text="تقرير نتائج المختبر",
                              font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        report_title.pack(pady=10)

        # معلومات التقرير
        info_frame = tk.Frame(preview_frame, bg='white')
        info_frame.pack(fill=tk.X, pady=10)

        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        tk.Label(info_frame, text=f"تاريخ التقرير: {current_date}",
                font=('Arial', 10), bg='white').pack(anchor='w')

        # جدول البيانات
        data_frame = tk.Frame(preview_frame, bg='white')
        data_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # إنشاء جدول للمعاينة مع الترتيب الصحيح
        preview_columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'جهة الإرسال', 'التحليل', 'النتيجة')
        preview_tree = ttk.Treeview(data_frame, columns=preview_columns,
                                   show='headings', height=12)

        for col in preview_columns:
            preview_tree.heading(col, text=col)
            preview_tree.column(col, width=90, anchor='center')

        # نسخ البيانات من الجدول الأصلي
        for item in self.reports_tree.get_children():
            values = self.reports_tree.item(item)['values']
            preview_tree.insert('', tk.END, values=values[:8])  # أول 8 أعمدة

        preview_tree.pack(fill=tk.BOTH, expand=True)

        # أزرار المعاينة
        buttons_frame = tk.Frame(preview_window, bg='white')
        buttons_frame.pack(fill=tk.X, pady=10)

        tk.Button(buttons_frame, text="طباعة", font=('Arial', 10, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=3,
                 command=lambda: self.print_report_data()).pack(side=tk.LEFT, padx=10)

        tk.Button(buttons_frame, text="إغلاق", font=('Arial', 10, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=3,
                 command=preview_window.destroy).pack(side=tk.RIGHT, padx=10)

    def print_report(self):
        """طباعة التقرير"""
        try:
            # التحقق من وجود البيانات
            if not hasattr(self, 'reports_tree') or not self.reports_tree.get_children():
                messagebox.showwarning("تحذير", "لا توجد بيانات للطباعة. يرجى البحث عن البيانات أولاً.")
                return

            # عد البيانات
            data_count = len(self.reports_tree.get_children())
            print(f"📄 بدء طباعة التقرير مع {data_count} صف من البيانات")

            # استدعاء دالة الطباعة
            self.print_report_data()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء بدء الطباعة: {str(e)}")
            print(f"❌ خطأ في print_report: {e}")

    def print_report_data(self):
        """طباعة بيانات التقرير باللغة العربية مع شعار وزارة الصحة"""
        try:
            # محاولة استخدام fpdf2 للنصوص العربية (الأفضل)
            try:
                from fpdf import FPDF
                import arabic_reshaper
                from bidi.algorithm import get_display
                print("✅ استخدام fpdf2 لدعم النصوص العربية")
                return self.create_arabic_report_with_fpdf()
            except ImportError as e:
                print(f"⚠️ fpdf2 أو مكتبات النصوص العربية غير متوفرة: {e}")
                pass

            # محاولة استخدام matplotlib للنصوص العربية
            try:
                import matplotlib.pyplot as plt
                import matplotlib.patches as patches
                from matplotlib.backends.backend_pdf import PdfPages
                print("✅ استخدام matplotlib لدعم النصوص العربية")
                return self.create_arabic_report_with_matplotlib()
            except ImportError:
                print("⚠️ matplotlib غير متوفر، استخدام الطريقة البديلة")
                pass

            # محاولة استخدام WeasyPrint للنصوص العربية
            try:
                import weasyprint
                from weasyprint import HTML, CSS
                print("✅ استخدام WeasyPrint لدعم النصوص العربية")
                return self.create_arabic_report_with_weasyprint()
            except ImportError:
                print("⚠️ WeasyPrint غير متوفر، استخدام الطريقة البديلة")
                pass

            # الطريقة البديلة: إنشاء صور للنصوص العربية
            try:
                from PIL import Image, ImageDraw, ImageFont
                print("✅ استخدام PIL لإنشاء صور النصوص العربية")
                return self.create_arabic_report_with_images()
            except ImportError:
                print("⚠️ PIL غير متوفر، استخدام reportlab مع تحسينات")
                pass

            # الطريقة الأخيرة: reportlab مع تحسينات
            try:
                from reportlab.lib.pagesizes import A4
                from reportlab.pdfgen import canvas
                from reportlab.lib.units import inch
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
                from reportlab.lib.utils import ImageReader
            except ImportError as import_error:
                messagebox.showerror("خطأ", f"مكتبة reportlab غير متوفرة: {import_error}")
                return

            # إنشاء ملف PDF
            filename = f"تقرير_المختبر_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            print(f"📄 إنشاء ملف PDF: {filename}")

            c = canvas.Canvas(filename, pagesize=A4)
            width, height = A4
            print(f"📐 أبعاد الصفحة: {width:.1f} x {height:.1f}")

            # تحميل خط يدعم النصوص العربية
            arabic_font_name = "ArabicFont"
            font_loaded = False

            try:
                # قائمة الخطوط العربية المتاحة
                arabic_fonts = [
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/tahoma.ttf",
                    "C:/Windows/Fonts/calibri.ttf",
                    "C:/Windows/Fonts/times.ttf",
                    "C:/Windows/Fonts/verdana.ttf"
                ]

                for font_path in arabic_fonts:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont(arabic_font_name, font_path))
                            font_loaded = True
                            print(f"✅ تم تحميل الخط العربي: {font_path}")
                            break
                        except Exception as font_error:
                            print(f"⚠️ فشل تحميل الخط: {font_path} - {font_error}")
                            continue

                if not font_loaded:
                    print("⚠️ استخدام Helvetica كبديل")
                    arabic_font_name = "Helvetica"

            except Exception as e:
                print(f"⚠️ خطأ في تحميل الخط العربي: {e}")
                arabic_font_name = "Helvetica"

            # دالة محسنة لمعالجة النص العربي
            def process_arabic_text(text):
                """معالجة النص العربي ليكون متوافقاً مع reportlab"""
                try:
                    if not text or not isinstance(text, str):
                        return str(text) if text else ""

                    # فحص إذا كان النص يحتوي على أحرف عربية
                    has_arabic = any('\u0600' <= char <= '\u06FF' for char in text)

                    if has_arabic:
                        # إعادة تشكيل النص العربي
                        reshaped_text = arabic_reshaper.reshape(text)
                        # تطبيق خوارزمية bidi للعرض الصحيح
                        bidi_text = get_display(reshaped_text)
                        return bidi_text
                    else:
                        return text

                except Exception as e:
                    print(f"⚠️ خطأ في معالجة النص العربي: {e}")
                    return text

            # دالة لرسم النص مع معالجة الأخطاء
            def draw_text_safe(canvas_obj, x, y, text, font_name, font_size):
                """رسم النص مع معالجة الأخطاء"""
                try:
                    processed_text = process_arabic_text(text)
                    canvas_obj.setFont(font_name, font_size)
                    canvas_obj.drawString(x, y, processed_text)
                    return True
                except Exception as e:
                    try:
                        # محاولة مع Helvetica كبديل
                        canvas_obj.setFont("Helvetica", font_size)
                        canvas_obj.drawString(x, y, processed_text)
                        return True
                    except:
                        print(f"⚠️ فشل في رسم النص: {text} - {e}")
                        return False

            # دالة لحساب عرض النص
            def get_text_width_safe(canvas_obj, text, font_name, font_size):
                """حساب عرض النص مع معالجة الأخطاء"""
                try:
                    processed_text = process_arabic_text(text)
                    return canvas_obj.stringWidth(processed_text, font_name, font_size)
                except:
                    try:
                        return canvas_obj.stringWidth(processed_text, "Helvetica", font_size)
                    except:
                        return 100  # قيمة افتراضية

            # إضافة الشعار في أعلى منتصف التقرير
            header_start_y = height - 50  # القيمة الافتراضية بدون شعار

            try:
                # الحصول على إعداد إظهار الشعار
                self.cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'show_logo'")
                show_logo_result = self.cursor.fetchone()
                show_logo = show_logo_result[0].lower() == 'true' if show_logo_result else True

                if show_logo:
                    # الحصول على مسار الشعار من الإعدادات
                    self.cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'logo_path'")
                    logo_result = self.cursor.fetchone()
                    logo_path = logo_result[0] if logo_result else 'ministry_logo.png'

                    # التحقق من وجود الملف
                    if os.path.exists(logo_path):
                        try:
                            # تحميل الشعار
                            logo = ImageReader(logo_path)

                            # حساب أبعاد الشعار للتقرير العربي
                            logo_width = 100
                            logo_height = 100

                            # حساب الموضع في أعلى منتصف الورقة تماماً
                            page_center_x = width / 2  # منتصف الصفحة
                            logo_x = page_center_x - (logo_width / 2)  # منتصف الشعار في منتصف الصفحة
                            logo_y = height - 120  # أعلى الورقة مع مسافة مناسبة

                            # رسم الشعار في أعلى منتصف الورقة
                            c.drawImage(logo, logo_x, logo_y, width=logo_width, height=logo_height)

                            # تحديد موضع بداية النص بعد الشعار
                            header_start_y = height - 140

                            print(f"✅ تم إضافة الشعار في أعلى منتصف التقرير العربي: X={logo_x:.1f}, Y={logo_y:.1f}")

                        except Exception as logo_error:
                            print(f"⚠️ خطأ في رسم الشعار: {logo_error}")
                            # رسم نص بديل للشعار باللغة العربية
                            text = "وزارة الصحة - جمهورية العراق"
                            text_width = get_text_width_safe(c, text, arabic_font_name + "-Bold", 14)
                            text_x = (width - text_width) / 2
                            draw_text_safe(c, text_x, height - 80, text, arabic_font_name + "-Bold", 14)
                            header_start_y = height - 100
                    else:
                        print(f"⚠️ ملف الشعار غير موجود: {logo_path}")
                        # رسم نص بديل باللغة العربية
                        text = "وزارة الصحة - جمهورية العراق"
                        text_width = get_text_width_safe(c, text, arabic_font_name + "-Bold", 14)
                        text_x = (width - text_width) / 2
                        draw_text_safe(c, text_x, height - 80, text, arabic_font_name + "-Bold", 14)
                        header_start_y = height - 100
                else:
                    print("ℹ️ تم إخفاء الشعار حسب الإعدادات")
                    # بدء النص من الأعلى مباشرة بدون شعار
                    header_start_y = height - 50

            except Exception as e:
                print(f"⚠️ خطأ عام في معالجة الشعار: {e}")
                # رسم نص بديل باللغة العربية
                text = "وزارة الصحة - جمهورية العراق"
                text_width = get_text_width_safe(c, text, arabic_font_name + "-Bold", 14)
                text_x = (width - text_width) / 2
                draw_text_safe(c, text_x, height - 80, text, arabic_font_name + "-Bold", 14)
                header_start_y = height - 100

            # العناوين الرسمية باللغة العربية
            main_header = [
                "جمهورية العراق",
                "وزارة الصحة",
                "قسم الصحة العامة - محافظة ذي قار",
                "مختبر الصحة العامة المركزي",
                "وحدة الفايروسات والأحياء المجهرية"
            ]

            y_pos = header_start_y
            for line in main_header:
                # حساب عرض النص لوضعه في المنتصف
                text_width = get_text_width_safe(c, line, arabic_font_name + "-Bold", 14)
                text_x = (width - text_width) / 2
                draw_text_safe(c, text_x, y_pos, line, arabic_font_name + "-Bold", 14)
                y_pos -= 18

            # خط فاصل تحت العنوان
            c.setStrokeColorRGB(0.2, 0.2, 0.2)
            c.setLineWidth(2)
            c.line(80, y_pos - 10, width - 80, y_pos - 10)

            # تحديث موضع بداية المحتوى
            content_start_y = y_pos - 30

            # عنوان التقرير باللغة العربية
            report_title_y = content_start_y - 20

            # عنوان التقرير الرئيسي
            main_title = "تقرير نتائج الفحوصات المختبرية"
            title_width = get_text_width_safe(c, main_title, arabic_font_name + "-Bold", 18)
            draw_text_safe(c, (width - title_width) / 2, report_title_y, main_title, arabic_font_name + "-Bold", 18)

            # عنوان فرعي
            sub_title = "Laboratory Test Results Report"
            sub_title_width = get_text_width_safe(c, sub_title, arabic_font_name + "-Bold", 14)
            draw_text_safe(c, (width - sub_title_width) / 2, report_title_y - 25, sub_title, arabic_font_name + "-Bold", 14)

            # معلومات التاريخ والوقت باللغة العربية
            current_date = datetime.datetime.now()

            # تنسيق التاريخ بالعربية
            arabic_date = current_date.strftime("%Y/%m/%d")
            arabic_time = current_date.strftime("%H:%M:%S")

            date_y = report_title_y - 60

            # تاريخ إنشاء التقرير
            date_text = f"تاريخ إنشاء التقرير: {arabic_date}"
            date_width = get_text_width_safe(c, date_text, arabic_font_name, 12)
            draw_text_safe(c, (width - date_width) / 2, date_y, date_text, arabic_font_name, 12)

            # وقت إنشاء التقرير
            time_text = f"وقت الإنشاء: {arabic_time}"
            time_width = get_text_width_safe(c, time_text, arabic_font_name, 12)
            draw_text_safe(c, (width - time_width) / 2, date_y - 20, time_text, arabic_font_name, 12)

            # رؤوس الجدول باللغة العربية
            table_start_y = date_y - 60

            # رؤوس الأعمدة باللغة العربية
            arabic_headers = ["الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحليل", "النتيجة"]

            # مواضع الأعمدة محسوبة للنص العربي
            x_positions = [50, 120, 200, 240, 280, 340, 420, 480]

            # رسم رؤوس الجدول
            for i, header in enumerate(arabic_headers):
                if i < len(x_positions):
                    draw_text_safe(c, x_positions[i], table_start_y, header, arabic_font_name + "-Bold", 10)

            # خط تحت الرؤوس
            c.setStrokeColorRGB(0, 0, 0)
            c.setLineWidth(1)
            c.line(40, table_start_y - 8, width - 40, table_start_y - 8)

            # البيانات باللغة العربية
            y_position = table_start_y - 25
            row_count = 0

            for item in self.reports_tree.get_children():
                if y_position < 120:  # صفحة جديدة
                    c.showPage()
                    y_position = height - 80

                    # إعادة رسم رؤوس الجدول في الصفحة الجديدة
                    for i, header in enumerate(arabic_headers):
                        if i < len(x_positions):
                            draw_text_safe(c, x_positions[i], y_position, header, arabic_font_name + "-Bold", 10)
                    c.line(40, y_position - 8, width - 40, y_position - 8)
                    y_position -= 25

                values = self.reports_tree.item(item)['values']
                row_count += 1

                # ترتيب البيانات: الرقم الوطني، الاسم، العمر، الجنس، نوع العينة، جهة الإرسال، التحليل، النتيجة
                for i, value in enumerate(values[:8]):  # أول 8 أعمدة
                    if i < len(x_positions):
                        # تحويل القيم للعربية حسب الحاجة
                        text = str(value)
                        if i == 3:  # الجنس
                            text = "ذكر" if text == "M" else "أنثى" if text == "F" else text
                        elif len(text) > 15:  # تقصير النص الطويل
                            text = text[:15] + "..."

                        # رسم النص باستخدام الدالة الآمنة
                        draw_text_safe(c, x_positions[i], y_position, text, arabic_font_name, 9)

                y_position -= 15

            # إحصائيات التقرير
            stats_y = y_position - 30
            stats_text = f"إجمالي عدد السجلات في التقرير: {row_count}"
            stats_width = get_text_width_safe(c, stats_text, arabic_font_name + "-Bold", 12)
            draw_text_safe(c, (width - stats_width) / 2, stats_y, stats_text, arabic_font_name + "-Bold", 12)

            # معلومات المختبر في التذييل باللغة العربية
            footer_y = 80

            # العنوان
            address_text = "العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة"
            address_width = get_text_width_safe(c, address_text, arabic_font_name, 10)
            draw_text_safe(c, (width - address_width) / 2, footer_y, address_text, arabic_font_name, 10)

            # معلومات إضافية
            info_text = "مختبر الصحة العامة المركزي - وحدة الفايروسات والأحياء المجهرية"
            info_width = get_text_width_safe(c, info_text, arabic_font_name, 9)
            draw_text_safe(c, (width - info_width) / 2, footer_y - 15, info_text, arabic_font_name, 9)

            # خط فاصل علوي
            c.setStrokeColorRGB(0.3, 0.3, 0.3)
            c.setLineWidth(1)
            c.line(60, footer_y + 20, width - 60, footer_y + 20)

            # رقم الصفحة
            page_text = "صفحة 1"
            page_width = get_text_width_safe(c, page_text, arabic_font_name, 8)
            draw_text_safe(c, (width - page_width) / 2, 30, page_text, arabic_font_name, 8)

            # حفظ الملف
            c.save()
            print(f"💾 تم حفظ تقرير PDF باللغة العربية: {filename}")

            # التحقق من إنشاء الملف
            if os.path.exists(filename):
                print(f"✅ تم إنشاء التقرير العربي بنجاح: {filename}")

                # فتح الملف
                try:
                    os.startfile(filename)
                    print(f"📂 تم فتح التقرير العربي: {filename}")
                except Exception as open_error:
                    print(f"⚠️ لا يمكن فتح الملف تلقائياً: {open_error}")

                # عد البيانات المطبوعة
                data_count = len(self.reports_tree.get_children())
                messagebox.showinfo("نجح", f"تم إنشاء التقرير العربي بنجاح! 🎉\n\n📄 اسم الملف: {filename}\n📊 عدد السجلات: {data_count}\n🏥 الشعار: {'مُضاف' if show_logo else 'مُخفى'}\n🌟 التقرير باللغة العربية كاملاً")
            else:
                messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {filename}")

        except Exception as e:
            error_msg = f"حدث خطأ أثناء إنشاء التقرير العربي:\n{str(e)}\n\nتأكد من:\n1. توفر مكتبة reportlab\n2. وجود صلاحيات الكتابة\n3. عدم فتح ملف بنفس الاسم\n4. وجود ملف الشعار (إذا كان مفعلاً)"
            messagebox.showerror("خطأ", error_msg)
            print(f"❌ خطأ في إنشاء التقرير العربي: {e}")

    def create_arabic_report_with_fpdf(self):
        """إنشاء تقرير عربي باستخدام fpdf2"""
        try:
            from fpdf import FPDF
            import arabic_reshaper
            from bidi.algorithm import get_display
            import datetime

            print("📄 إنشاء تقرير عربي باستخدام fpdf2...")

            # إنشاء كلاس PDF مخصص للنصوص العربية
            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__()
                    self.add_page()

                    # تحميل خط عربي
                    try:
                        # محاولة تحميل خط Arial
                        self.add_font('Arial', '', 'C:/Windows/Fonts/arial.ttf')
                        self.add_font('Arial', 'B', 'C:/Windows/Fonts/arialbd.ttf')
                        self.arabic_font = 'Arial'
                        print("✅ تم تحميل خط Arial")
                    except:
                        try:
                            # محاولة تحميل خط Tahoma
                            self.add_font('Tahoma', '', 'C:/Windows/Fonts/tahoma.ttf')
                            self.add_font('Tahoma', 'B', 'C:/Windows/Fonts/tahomabd.ttf')
                            self.arabic_font = 'Tahoma'
                            print("✅ تم تحميل خط Tahoma")
                        except:
                            # استخدام خط افتراضي
                            self.arabic_font = 'Arial'
                            print("⚠️ استخدام خط افتراضي")

                def process_arabic_text(self, text):
                    """معالجة النص العربي"""
                    try:
                        if not text:
                            return ""

                        # فحص إذا كان النص يحتوي على عربي
                        has_arabic = any('\u0600' <= char <= '\u06FF' for char in str(text))

                        if has_arabic:
                            # إعادة تشكيل النص العربي
                            reshaped_text = arabic_reshaper.reshape(str(text))
                            # تطبيق خوارزمية bidi
                            bidi_text = get_display(reshaped_text)
                            return bidi_text
                        else:
                            return str(text)
                    except Exception as e:
                        print(f"⚠️ خطأ في معالجة النص: {e}")
                        return str(text)

                def add_arabic_text(self, x, y, text, size=12, style=''):
                    """إضافة نص عربي"""
                    try:
                        from fpdf.enums import XPos, YPos
                        processed_text = self.process_arabic_text(text)
                        self.set_font(self.arabic_font, style, size)
                        self.set_xy(x, y)
                        self.cell(0, 10, processed_text, 0, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='C')
                    except Exception as e:
                        print(f"⚠️ خطأ في إضافة النص: {e}")
                        # محاولة بدون معالجة
                        try:
                            from fpdf.enums import XPos, YPos
                            self.set_font('Arial', style, size)
                            self.set_xy(x, y)
                            self.cell(0, 10, str(text), 0, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='C')
                        except:
                            # استخدام الطريقة القديمة كبديل
                            self.set_font('Arial', style, size)
                            self.set_xy(x, y)
                            self.cell(0, 10, str(text), 0, 1, 'C')

            # إنشاء PDF
            pdf = ArabicPDF()

            # إضافة الشعار
            try:
                self.cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'logo_path'")
                logo_result = self.cursor.fetchone()
                logo_path = logo_result[0] if logo_result else 'ministry_logo.png'

                if os.path.exists(logo_path):
                    # إضافة الشعار في أعلى منتصف الصفحة
                    pdf.image(logo_path, x=85, y=10, w=40, h=40)
                    print("✅ تم إضافة الشعار")
                    start_y = 60
                else:
                    start_y = 20
            except:
                start_y = 20

            # العناوين الرسمية
            y_pos = start_y
            titles = [
                ("جمهورية العراق", 16, 'B'),
                ("وزارة الصحة", 14, 'B'),
                ("قسم الصحة العامة - محافظة ذي قار", 12, 'B'),
                ("مختبر الصحة العامة المركزي", 12, 'B'),
                ("وحدة الفايروسات والأحياء المجهرية", 10, 'B')
            ]

            for title, size, style in titles:
                pdf.add_arabic_text(0, y_pos, title, size, style)
                y_pos += 12
                print(f"✅ تم إضافة: {title}")

            # عنوان التقرير
            y_pos += 10
            pdf.add_arabic_text(0, y_pos, "تقرير نتائج الفحوصات المختبرية", 18, 'B')
            y_pos += 15
            try:
                from fpdf.enums import XPos, YPos
                pdf.set_font('Arial', 'B', 14)
                pdf.set_xy(0, y_pos)
                pdf.cell(0, 10, "Laboratory Test Results Report", 0, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='C')
            except:
                pdf.set_font('Arial', 'B', 14)
                pdf.set_xy(0, y_pos)
                pdf.cell(0, 10, "Laboratory Test Results Report", 0, 1, 'C')

            # التاريخ والوقت
            current_date = datetime.datetime.now()
            arabic_date = current_date.strftime("%Y/%m/%d")
            arabic_time = current_date.strftime("%H:%M:%S")

            y_pos += 20
            pdf.add_arabic_text(0, y_pos, f"تاريخ إنشاء التقرير: {arabic_date}", 12)
            y_pos += 10
            pdf.add_arabic_text(0, y_pos, f"وقت الإنشاء: {arabic_time}", 12)

            # جدول البيانات
            y_pos += 20
            self.add_data_table_fpdf(pdf, y_pos)

            # حفظ الملف
            filename = f"تقرير_المختبر_fpdf_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            pdf.output(filename)

            if os.path.exists(filename):
                print(f"✅ تم إنشاء التقرير بـ fpdf2: {filename}")

                # فتح الملف
                try:
                    os.startfile(filename)
                    print("📂 تم فتح التقرير")
                except:
                    print("📂 يمكنك فتح التقرير يدوياً")

                messagebox.showinfo("نجح", f"تم إنشاء التقرير العربي بنجاح!\n{filename}")
                return True
            else:
                print("❌ فشل في إنشاء التقرير")
                return False

        except Exception as e:
            print(f"❌ خطأ في fpdf2: {e}")
            import traceback
            traceback.print_exc()
            return False

    def add_data_table_fpdf(self, pdf, start_y):
        """إضافة جدول البيانات لـ fpdf"""
        try:
            # رؤوس الجدول
            headers = ["الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحليل", "النتيجة"]

            # عرض الأعمدة
            col_widths = [25, 30, 15, 15, 20, 30, 25, 25]

            # رسم رؤوس الجدول
            pdf.set_xy(10, start_y)
            pdf.set_font(pdf.arabic_font, 'B', 8)

            x_pos = 10
            for i, header in enumerate(headers):
                processed_header = pdf.process_arabic_text(header)
                pdf.set_xy(x_pos, start_y)
                try:
                    from fpdf.enums import XPos, YPos
                    pdf.cell(col_widths[i], 8, processed_header, 1, new_x=XPos.RIGHT, new_y=YPos.TOP, align='C')
                except:
                    pdf.cell(col_widths[i], 8, processed_header, 1, 0, 'C')
                x_pos += col_widths[i]

            # الحصول على البيانات
            report_data = []
            for item in self.reports_tree.get_children():
                values = self.reports_tree.item(item)['values']
                report_data.append(values)

            # رسم البيانات
            y_pos = start_y + 8
            pdf.set_font(pdf.arabic_font, '', 7)

            for row_data in report_data[:15]:  # أول 15 صف
                x_pos = 10
                for i, value in enumerate(row_data[:8]):
                    if i < len(col_widths):
                        # تحويل الجنس للعربية
                        if i == 3:  # الجنس
                            value = "ذكر" if str(value) == "M" else "أنثى" if str(value) == "F" else str(value)

                        # تقصير النص الطويل
                        text = str(value)[:12] + "..." if len(str(value)) > 12 else str(value)
                        processed_text = pdf.process_arabic_text(text)

                        pdf.set_xy(x_pos, y_pos)
                        try:
                            from fpdf.enums import XPos, YPos
                            pdf.cell(col_widths[i], 6, processed_text, 1, new_x=XPos.RIGHT, new_y=YPos.TOP, align='C')
                        except:
                            pdf.cell(col_widths[i], 6, processed_text, 1, 0, 'C')
                        x_pos += col_widths[i]

                y_pos += 6
                if y_pos > 250:  # تجنب تجاوز الصفحة
                    break

            # إحصائيات التقرير
            y_pos += 15
            pdf.add_arabic_text(0, y_pos, f"إجمالي عدد السجلات في التقرير: {len(report_data)}", 12, 'B')

            # التذييل
            y_pos += 20
            pdf.add_arabic_text(0, y_pos, "العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة", 10)
            y_pos += 8
            pdf.add_arabic_text(0, y_pos, "مختبر الصحة العامة المركزي - وحدة الفايروسات والأحياء المجهرية", 9)
            y_pos += 8
            pdf.add_arabic_text(0, y_pos, "صفحة 1", 8)

            print("✅ تم إضافة جدول البيانات")

        except Exception as e:
            print(f"❌ خطأ في إضافة الجدول: {e}")

    def create_arabic_report_with_weasyprint(self):
        """إنشاء تقرير عربي باستخدام WeasyPrint"""
        try:
            import weasyprint
            from weasyprint import HTML, CSS
            import datetime
            import base64

            print("📄 إنشاء تقرير عربي باستخدام WeasyPrint...")

            # إنشاء HTML للتقرير العربي
            html_content = self.generate_arabic_html_report()

            # إنشاء CSS للتنسيق العربي
            css_content = self.generate_arabic_css()

            # إنشاء ملف PDF
            filename = f"تقرير_المختبر_عربي_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

            # تحويل HTML إلى PDF
            html_doc = HTML(string=html_content)
            css_doc = CSS(string=css_content)

            html_doc.write_pdf(filename, stylesheets=[css_doc])

            if os.path.exists(filename):
                print(f"✅ تم إنشاء التقرير العربي: {filename}")

                # فتح الملف
                try:
                    os.startfile(filename)
                    print("📂 تم فتح التقرير العربي")
                except:
                    print("📂 يمكنك فتح التقرير يدوياً")

                messagebox.showinfo("نجح", f"تم إنشاء التقرير العربي بنجاح!\n{filename}")
                return True
            else:
                print("❌ فشل في إنشاء التقرير")
                return False

        except Exception as e:
            print(f"❌ خطأ في WeasyPrint: {e}")
            return False

    def generate_arabic_html_report(self):
        """إنشاء محتوى HTML للتقرير العربي"""
        try:
            import datetime
            import base64

            # الحصول على بيانات التقرير
            report_data = []
            for item in self.reports_tree.get_children():
                values = self.reports_tree.item(item)['values']
                report_data.append(values)

            # الحصول على معلومات الشعار
            logo_base64 = ""
            try:
                self.cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'logo_path'")
                logo_result = self.cursor.fetchone()
                logo_path = logo_result[0] if logo_result else 'ministry_logo.png'

                if os.path.exists(logo_path):
                    with open(logo_path, "rb") as img_file:
                        logo_base64 = base64.b64encode(img_file.read()).decode('utf-8')
            except:
                pass

            # تاريخ ووقت التقرير
            current_date = datetime.datetime.now()
            arabic_date = current_date.strftime("%Y/%m/%d")
            arabic_time = current_date.strftime("%H:%M:%S")

            # إنشاء HTML
            html = f"""<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير نتائج الفحوصات المختبرية</title>
</head>
<body>
    <div class="report-container">
        <!-- الشعار -->
        {f'<div class="logo-container"><img src="data:image/png;base64,{logo_base64}" alt="شعار وزارة الصحة" class="logo"></div>' if logo_base64 else ''}

        <!-- العناوين الرسمية -->
        <div class="header-section">
            <h1 class="main-title">جمهورية العراق</h1>
            <h2 class="sub-title">وزارة الصحة</h2>
            <h3 class="dept-title">قسم الصحة العامة - محافظة ذي قار</h3>
            <h3 class="lab-title">مختبر الصحة العامة المركزي</h3>
            <h4 class="unit-title">وحدة الفايروسات والأحياء المجهرية</h4>
        </div>

        <!-- عنوان التقرير -->
        <div class="report-title-section">
            <h2 class="report-title">تقرير نتائج الفحوصات المختبرية</h2>
            <h3 class="report-subtitle">Laboratory Test Results Report</h3>
        </div>

        <!-- معلومات التاريخ -->
        <div class="date-section">
            <p class="date-info">تاريخ إنشاء التقرير: {arabic_date}</p>
            <p class="time-info">وقت الإنشاء: {arabic_time}</p>
        </div>

        <!-- جدول البيانات -->
        <div class="table-section">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>الرقم الوطني</th>
                        <th>الاسم</th>
                        <th>العمر</th>
                        <th>الجنس</th>
                        <th>نوع العينة</th>
                        <th>جهة الإرسال</th>
                        <th>التحليل</th>
                        <th>النتيجة</th>
                    </tr>
                </thead>
                <tbody>"""

            # إضافة البيانات
            for values in report_data:
                html += "\n                    <tr>"
                for i, value in enumerate(values[:8]):
                    # تحويل الجنس للعربية
                    if i == 3:  # الجنس
                        value = "ذكر" if str(value) == "M" else "أنثى" if str(value) == "F" else str(value)
                    html += f"\n                        <td>{str(value)}</td>"
                html += "\n                    </tr>"

            # إكمال HTML
            html += f"""
                </tbody>
            </table>
        </div>

        <!-- إحصائيات التقرير -->
        <div class="stats-section">
            <p class="stats-text">إجمالي عدد السجلات في التقرير: {len(report_data)}</p>
        </div>

        <!-- التذييل -->
        <div class="footer-section">
            <p class="address">العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة</p>
            <p class="lab-info">مختبر الصحة العامة المركزي - وحدة الفايروسات والأحياء المجهرية</p>
            <p class="page-number">صفحة 1</p>
        </div>
    </div>
</body>
</html>"""

            return html

        except Exception as e:
            print(f"❌ خطأ في إنشاء HTML: {e}")
            return ""

    def generate_arabic_css(self):
        """إنشاء CSS للتنسيق العربي"""
        css = """
        @page {
            size: A4;
            margin: 2cm;
        }

        body {
            font-family: 'Arial', 'Tahoma', 'Times New Roman', sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .report-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo {
            width: 100px;
            height: 100px;
            object-fit: contain;
        }

        .header-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .main-title {
            font-size: 18px;
            font-weight: bold;
            margin: 5px 0;
            color: #000;
        }

        .sub-title {
            font-size: 16px;
            font-weight: bold;
            margin: 5px 0;
            color: #000;
        }

        .dept-title, .lab-title {
            font-size: 14px;
            font-weight: bold;
            margin: 5px 0;
            color: #000;
        }

        .unit-title {
            font-size: 12px;
            font-weight: bold;
            margin: 5px 0;
            color: #000;
        }

        .report-title-section {
            text-align: center;
            margin: 30px 0;
        }

        .report-title {
            font-size: 20px;
            font-weight: bold;
            color: #000;
            margin: 10px 0;
        }

        .report-subtitle {
            font-size: 16px;
            font-weight: bold;
            color: #666;
            margin: 10px 0;
        }

        .date-section {
            text-align: center;
            margin: 20px 0;
        }

        .date-info, .time-info {
            font-size: 12px;
            margin: 5px 0;
            color: #333;
        }

        .table-section {
            margin: 30px 0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 10px;
        }

        .data-table th {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            font-weight: bold;
            color: #000;
        }

        .data-table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: center;
            color: #333;
        }

        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .stats-section {
            text-align: center;
            margin: 30px 0;
        }

        .stats-text {
            font-size: 14px;
            font-weight: bold;
            color: #000;
        }

        .footer-section {
            text-align: center;
            margin-top: 40px;
            border-top: 1px solid #ccc;
            padding-top: 20px;
        }

        .address {
            font-size: 11px;
            margin: 5px 0;
            color: #333;
        }

        .lab-info {
            font-size: 10px;
            margin: 5px 0;
            color: #666;
        }

        .page-number {
            font-size: 10px;
            margin: 10px 0;
            color: #999;
        }
        """
        return css

    def create_arabic_report_with_images(self):
        """إنشاء تقرير عربي باستخدام صور للنصوص"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            import datetime

            print("📄 إنشاء تقرير عربي باستخدام صور النصوص...")

            # إنشاء ملف PDF
            filename = f"تقرير_المختبر_صور_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            c = canvas.Canvas(filename, pagesize=A4)
            width, height = A4

            # إنشاء صور للنصوص العربية
            arabic_texts = [
                "جمهورية العراق",
                "وزارة الصحة",
                "قسم الصحة العامة - محافظة ذي قار",
                "مختبر الصحة العامة المركزي",
                "وحدة الفايروسات والأحياء المجهرية",
                "تقرير نتائج الفحوصات المختبرية"
            ]

            y_pos = height - 100

            for text in arabic_texts:
                # إنشاء صورة للنص العربي
                img_path = self.create_arabic_text_image(text, 14)
                if img_path and os.path.exists(img_path):
                    # إدراج الصورة في PDF
                    c.drawImage(img_path, width/2 - 100, y_pos, width=200, height=30)
                    # حذف الصورة المؤقتة
                    os.remove(img_path)
                y_pos -= 35

            # حفظ الملف
            c.save()

            if os.path.exists(filename):
                print(f"✅ تم إنشاء التقرير بالصور: {filename}")
                try:
                    os.startfile(filename)
                except:
                    pass
                messagebox.showinfo("نجح", f"تم إنشاء التقرير بالصور!\n{filename}")
                return True

            return False

        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير بالصور: {e}")
            return False

    def create_arabic_text_image(self, text, font_size):
        """إنشاء صورة للنص العربي"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import tempfile

            # إنشاء صورة
            img_width = 400
            img_height = 50
            img = Image.new('RGB', (img_width, img_height), color='white')
            draw = ImageDraw.Draw(img)

            # محاولة تحميل خط عربي
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
            except:
                try:
                    font = ImageFont.truetype("C:/Windows/Fonts/tahoma.ttf", font_size)
                except:
                    font = ImageFont.load_default()

            # حساب موضع النص
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            x = (img_width - text_width) // 2
            y = (img_height - text_height) // 2

            # رسم النص
            draw.text((x, y), text, fill='black', font=font)

            # حفظ الصورة المؤقتة
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            img.save(temp_file.name)
            temp_file.close()

            return temp_file.name

        except Exception as e:
            print(f"❌ خطأ في إنشاء صورة النص: {e}")
            return None

    def create_arabic_report_with_matplotlib(self):
        """إنشاء تقرير عربي باستخدام matplotlib"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches
            from matplotlib.backends.backend_pdf import PdfPages
            import matplotlib.font_manager as fm
            import datetime

            print("📄 إنشاء تقرير عربي باستخدام matplotlib...")

            # إعداد matplotlib للنصوص العربية
            plt.rcParams['font.family'] = ['Arial', 'Tahoma', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # إنشاء ملف PDF
            filename = f"تقرير_المختبر_matplotlib_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

            with PdfPages(filename) as pdf:
                # إنشاء صفحة جديدة
                fig, ax = plt.subplots(figsize=(8.27, 11.69))  # A4 size
                ax.set_xlim(0, 10)
                ax.set_ylim(0, 14)
                ax.axis('off')

                # الحصول على بيانات التقرير
                report_data = []
                for item in self.reports_tree.get_children():
                    values = self.reports_tree.item(item)['values']
                    report_data.append(values)

                # رسم الشعار (إذا كان متوفراً)
                try:
                    self.cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'logo_path'")
                    logo_result = self.cursor.fetchone()
                    logo_path = logo_result[0] if logo_result else 'ministry_logo.png'

                    if os.path.exists(logo_path):
                        from matplotlib.image import imread
                        logo_img = imread(logo_path)
                        ax.imshow(logo_img, extent=[4, 6, 12.5, 13.5], aspect='auto')
                        print("✅ تم إضافة الشعار")
                except:
                    print("⚠️ لم يتم العثور على الشعار")

                # العناوين الرسمية
                y_pos = 12
                titles = [
                    ("جمهورية العراق", 16, 'bold'),
                    ("وزارة الصحة", 14, 'bold'),
                    ("قسم الصحة العامة - محافظة ذي قار", 12, 'bold'),
                    ("مختبر الصحة العامة المركزي", 12, 'bold'),
                    ("وحدة الفايروسات والأحياء المجهرية", 10, 'bold')
                ]

                for title, size, weight in titles:
                    ax.text(5, y_pos, title, ha='center', va='center',
                           fontsize=size, fontweight=weight,
                           fontfamily=['Arial', 'Tahoma'])
                    y_pos -= 0.4

                # عنوان التقرير
                y_pos -= 0.5
                ax.text(5, y_pos, "تقرير نتائج الفحوصات المختبرية",
                       ha='center', va='center', fontsize=18, fontweight='bold',
                       fontfamily=['Arial', 'Tahoma'])
                y_pos -= 0.3
                ax.text(5, y_pos, "Laboratory Test Results Report",
                       ha='center', va='center', fontsize=14, fontweight='bold')

                # التاريخ والوقت
                current_date = datetime.datetime.now()
                arabic_date = current_date.strftime("%Y/%m/%d")
                arabic_time = current_date.strftime("%H:%M:%S")

                y_pos -= 0.8
                ax.text(5, y_pos, f"تاريخ إنشاء التقرير: {arabic_date}",
                       ha='center', va='center', fontsize=12,
                       fontfamily=['Arial', 'Tahoma'])
                y_pos -= 0.3
                ax.text(5, y_pos, f"وقت الإنشاء: {arabic_time}",
                       ha='center', va='center', fontsize=12,
                       fontfamily=['Arial', 'Tahoma'])

                # رسم جدول البيانات
                y_pos -= 1
                self.draw_data_table_matplotlib(ax, report_data, y_pos)

                # إحصائيات التقرير
                y_pos = 2.5
                ax.text(5, y_pos, f"إجمالي عدد السجلات في التقرير: {len(report_data)}",
                       ha='center', va='center', fontsize=14, fontweight='bold',
                       fontfamily=['Arial', 'Tahoma'])

                # التذييل
                y_pos = 1.5
                ax.text(5, y_pos, "العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة",
                       ha='center', va='center', fontsize=10,
                       fontfamily=['Arial', 'Tahoma'])
                y_pos -= 0.3
                ax.text(5, y_pos, "مختبر الصحة العامة المركزي - وحدة الفايروسات والأحياء المجهرية",
                       ha='center', va='center', fontsize=9,
                       fontfamily=['Arial', 'Tahoma'])
                y_pos -= 0.3
                ax.text(5, y_pos, "صفحة 1",
                       ha='center', va='center', fontsize=8,
                       fontfamily=['Arial', 'Tahoma'])

                # حفظ الصفحة
                pdf.savefig(fig, bbox_inches='tight')
                plt.close(fig)

            if os.path.exists(filename):
                print(f"✅ تم إنشاء التقرير بـ matplotlib: {filename}")

                # فتح الملف
                try:
                    os.startfile(filename)
                    print("📂 تم فتح التقرير")
                except:
                    print("📂 يمكنك فتح التقرير يدوياً")

                messagebox.showinfo("نجح", f"تم إنشاء التقرير العربي بنجاح!\n{filename}")
                return True
            else:
                print("❌ فشل في إنشاء التقرير")
                return False

        except Exception as e:
            print(f"❌ خطأ في matplotlib: {e}")
            return False

    def draw_data_table_matplotlib(self, ax, data, start_y):
        """رسم جدول البيانات باستخدام matplotlib"""
        try:
            # رؤوس الجدول
            headers = ["الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحليل", "النتيجة"]

            # مواضع الأعمدة
            col_positions = [0.5, 1.5, 2.5, 3.5, 4.5, 6, 7.5, 9]
            col_widths = [0.8, 0.8, 0.6, 0.6, 0.8, 1.2, 1.2, 1]

            # رسم رؤوس الجدول
            y_pos = start_y
            for i, header in enumerate(headers):
                if i < len(col_positions):
                    # رسم خلفية الرأس
                    rect = patches.Rectangle((col_positions[i] - col_widths[i]/2, y_pos - 0.15),
                                           col_widths[i], 0.3,
                                           linewidth=1, edgecolor='black', facecolor='lightgray')
                    ax.add_patch(rect)

                    # رسم النص
                    ax.text(col_positions[i], y_pos, header,
                           ha='center', va='center', fontsize=8, fontweight='bold',
                           fontfamily=['Arial', 'Tahoma'])

            # رسم البيانات
            y_pos -= 0.4
            for row_data in data[:10]:  # أول 10 صفوف فقط لتوفير المساحة
                for i, value in enumerate(row_data[:8]):
                    if i < len(col_positions):
                        # تحويل الجنس للعربية
                        if i == 3:  # الجنس
                            value = "ذكر" if str(value) == "M" else "أنثى" if str(value) == "F" else str(value)

                        # رسم خلفية الخلية
                        rect = patches.Rectangle((col_positions[i] - col_widths[i]/2, y_pos - 0.1),
                                               col_widths[i], 0.2,
                                               linewidth=0.5, edgecolor='gray', facecolor='white')
                        ax.add_patch(rect)

                        # رسم النص
                        text = str(value)[:15] + "..." if len(str(value)) > 15 else str(value)
                        ax.text(col_positions[i], y_pos, text,
                               ha='center', va='center', fontsize=7,
                               fontfamily=['Arial', 'Tahoma'])

                y_pos -= 0.25
                if y_pos < 3:  # توقف إذا وصلنا لأسفل الصفحة
                    break

        except Exception as e:
            print(f"❌ خطأ في رسم الجدول: {e}")

    def export_excel(self):
        """تصدير إلى Excel"""
        try:
            # التحقق من وجود البيانات في الجدول
            if not hasattr(self, 'reports_tree') or not self.reports_tree.get_children():
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير. يرجى البحث عن البيانات أولاً.")
                return

            # جمع البيانات من الجدول
            data = []
            for item in self.reports_tree.get_children():
                values = self.reports_tree.item(item)['values']
                data.append(values)

            if not data:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            print(f"📊 جمع {len(data)} صف من البيانات للتصدير")

            # التحقق من توفر pandas و openpyxl
            try:
                import pandas as pd
                import openpyxl
            except ImportError as import_error:
                messagebox.showerror("خطأ", f"مكتبة مطلوبة غير متوفرة: {import_error}\nيرجى تثبيت pandas و openpyxl")
                return

            # إنشاء DataFrame مع أسماء الأعمدة الصحيحة
            columns = ['الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة',
                      'جهة الإرسال', 'التحليل', 'النتيجة', 'تاريخ النتيجة']

            # التأكد من أن عدد الأعمدة يتطابق مع البيانات
            if data and len(data[0]) != len(columns):
                # تعديل عدد الأعمدة حسب البيانات الفعلية
                columns = columns[:len(data[0])]

            df = pd.DataFrame(data, columns=columns)

            # حفظ الملف
            filename = filedialog.asksaveasfilename(
                title="حفظ ملف Excel",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname=f"lab_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if filename:
                # حفظ الملف مع معالجة الأخطاء
                df.to_excel(filename, index=False, engine='openpyxl')

                # التحقق من إنشاء الملف
                if os.path.exists(filename):
                    messagebox.showinfo("نجح", f"تم تصدير البيانات بنجاح إلى:\n{filename}\n\nعدد الصفوف: {len(data)}")

                    # فتح الملف
                    try:
                        os.startfile(filename)
                    except:
                        pass
                else:
                    messagebox.showerror("خطأ", "فشل في إنشاء الملف")

        except Exception as e:
            error_msg = f"حدث خطأ أثناء التصدير:\n{str(e)}\n\nتأكد من:\n1. توفر مكتبة pandas و openpyxl\n2. عدم فتح الملف في برنامج آخر\n3. وجود صلاحيات الكتابة"
            messagebox.showerror("خطأ", error_msg)
            print(f"❌ خطأ في تصدير Excel: {e}")
    
    def show_settings_tab(self):
        """عرض تبويب الإعدادات"""
        self.clear_content_frame()

        # عنوان التبويب
        title = tk.Label(self.content_frame, text="الإعدادات",
                        font=('Arial', 16, 'bold'), bg='#ecf0f1')
        title.pack(pady=10)

        # إنشاء دفتر التبويبات للإعدادات
        settings_notebook = ttk.Notebook(self.content_frame)
        settings_notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # تبويب أنواع العينات
        self.create_sample_types_tab(settings_notebook)

        # تبويب جهات الإرسال
        self.create_sender_orgs_tab(settings_notebook)

        # تبويب التحاليل
        self.create_tests_tab(settings_notebook)

        # تبويب الفنيين
        self.create_technicians_tab(settings_notebook)

        # تبويب إعدادات التقرير
        self.create_report_settings_tab(settings_notebook)

    def create_sample_types_tab(self, parent):
        """إنشاء تبويب أنواع العينات"""
        frame = tk.Frame(parent, bg='#ecf0f1')
        parent.add(frame, text="أنواع العينات")

        # إطار الإدخال
        input_frame = tk.Frame(frame, bg='#ecf0f1')
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(input_frame, text="نوع العينة:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5)
        self.sample_type_entry = tk.StringVar()
        tk.Entry(input_frame, textvariable=self.sample_type_entry, width=30).grid(
            row=0, column=1, padx=5, pady=5)

        # أزرار العمليات
        buttons_frame = tk.Frame(input_frame, bg='#ecf0f1')
        buttons_frame.grid(row=0, column=2, padx=10)

        tk.Button(buttons_frame, text="إضافة", font=('Arial', 9, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=2,
                 command=lambda: self.add_setting('sample_types', self.sample_type_entry.get())).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="تعديل", font=('Arial', 9, 'bold'),
                 bg='#f39c12', fg='white', relief='raised', bd=2,
                 command=lambda: self.edit_setting('sample_types')).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="حذف", font=('Arial', 9, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=2,
                 command=lambda: self.delete_setting('sample_types')).pack(side=tk.LEFT, padx=2)

        # قائمة أنواع العينات
        self.sample_types_listbox = tk.Listbox(frame, height=10, font=('Arial', 10))
        self.sample_types_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.refresh_settings_list('sample_types', self.sample_types_listbox)

    def create_sender_orgs_tab(self, parent):
        """إنشاء تبويب جهات الإرسال"""
        frame = tk.Frame(parent, bg='#ecf0f1')
        parent.add(frame, text="جهات الإرسال")

        input_frame = tk.Frame(frame, bg='#ecf0f1')
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(input_frame, text="جهة الإرسال:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5)
        self.sender_org_entry = tk.StringVar()
        tk.Entry(input_frame, textvariable=self.sender_org_entry, width=30).grid(
            row=0, column=1, padx=5, pady=5)

        buttons_frame = tk.Frame(input_frame, bg='#ecf0f1')
        buttons_frame.grid(row=0, column=2, padx=10)

        tk.Button(buttons_frame, text="إضافة", font=('Arial', 9, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=2,
                 command=lambda: self.add_setting('sender_orgs', self.sender_org_entry.get())).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="تعديل", font=('Arial', 9, 'bold'),
                 bg='#f39c12', fg='white', relief='raised', bd=2,
                 command=lambda: self.edit_setting('sender_orgs')).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="حذف", font=('Arial', 9, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=2,
                 command=lambda: self.delete_setting('sender_orgs')).pack(side=tk.LEFT, padx=2)

        self.sender_orgs_listbox = tk.Listbox(frame, height=10, font=('Arial', 10))
        self.sender_orgs_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.refresh_settings_list('sender_orgs', self.sender_orgs_listbox)

    def create_tests_tab(self, parent):
        """إنشاء تبويب التحاليل"""
        frame = tk.Frame(parent, bg='#ecf0f1')
        parent.add(frame, text="التحاليل")

        input_frame = tk.Frame(frame, bg='#ecf0f1')
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(input_frame, text="التحليل:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5)
        self.test_entry = tk.StringVar()
        tk.Entry(input_frame, textvariable=self.test_entry, width=30).grid(
            row=0, column=1, padx=5, pady=5)

        buttons_frame = tk.Frame(input_frame, bg='#ecf0f1')
        buttons_frame.grid(row=0, column=2, padx=10)

        tk.Button(buttons_frame, text="إضافة", font=('Arial', 9, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=2,
                 command=lambda: self.add_setting('tests', self.test_entry.get())).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="تعديل", font=('Arial', 9, 'bold'),
                 bg='#f39c12', fg='white', relief='raised', bd=2,
                 command=lambda: self.edit_setting('tests')).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="حذف", font=('Arial', 9, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=2,
                 command=lambda: self.delete_setting('tests')).pack(side=tk.LEFT, padx=2)

        self.tests_listbox = tk.Listbox(frame, height=10, font=('Arial', 10))
        self.tests_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.refresh_settings_list('tests', self.tests_listbox)

    def create_technicians_tab(self, parent):
        """إنشاء تبويب الفنيين"""
        frame = tk.Frame(parent, bg='#ecf0f1')
        parent.add(frame, text="الفنيين")

        input_frame = tk.Frame(frame, bg='#ecf0f1')
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(input_frame, text="اسم الفني:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5)
        self.technician_entry = tk.StringVar()
        tk.Entry(input_frame, textvariable=self.technician_entry, width=30).grid(
            row=0, column=1, padx=5, pady=5)

        buttons_frame = tk.Frame(input_frame, bg='#ecf0f1')
        buttons_frame.grid(row=0, column=2, padx=10)

        tk.Button(buttons_frame, text="إضافة", font=('Arial', 9, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=2,
                 command=lambda: self.add_setting('technicians', self.technician_entry.get())).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="تعديل", font=('Arial', 9, 'bold'),
                 bg='#f39c12', fg='white', relief='raised', bd=2,
                 command=lambda: self.edit_setting('technicians')).pack(side=tk.LEFT, padx=2)

        tk.Button(buttons_frame, text="حذف", font=('Arial', 9, 'bold'),
                 bg='#e74c3c', fg='white', relief='raised', bd=2,
                 command=lambda: self.delete_setting('technicians')).pack(side=tk.LEFT, padx=2)

        self.technicians_listbox = tk.Listbox(frame, height=10, font=('Arial', 10))
        self.technicians_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.refresh_settings_list('technicians', self.technicians_listbox)

    def create_report_settings_tab(self, parent):
        """إنشاء تبويب إعدادات التقرير"""
        frame = tk.Frame(parent, bg='#ecf0f1')
        parent.add(frame, text="إعدادات التقرير")

        # إعدادات الشعار
        logo_frame = tk.LabelFrame(frame, text="شعار التقرير",
                                  font=('Arial', 12, 'bold'), bg='#ecf0f1')
        logo_frame.pack(fill=tk.X, padx=10, pady=10)

        # مسار الشعار الحالي
        self.logo_path = tk.StringVar(value="ministry_logo.png")

        tk.Label(logo_frame, text="الشعار الحالي:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5, sticky='w')

        logo_info_frame = tk.Frame(logo_frame, bg='#ecf0f1')
        logo_info_frame.grid(row=0, column=1, padx=5, pady=5, sticky='w')

        self.logo_label = tk.Label(logo_info_frame, text="ministry_logo.png",
                                  font=('Arial', 9), bg='#ecf0f1', fg='#27ae60')
        self.logo_label.pack(side=tk.LEFT)

        # خيار إظهار/إخفاء الشعار
        logo_display_frame = tk.Frame(logo_frame, bg='#ecf0f1')
        logo_display_frame.grid(row=1, column=0, columnspan=2, pady=5)

        self.show_logo = tk.BooleanVar(value=True)
        tk.Checkbutton(logo_display_frame, text="إظهار الشعار في التقارير",
                      variable=self.show_logo, font=('Arial', 10, 'bold'),
                      bg='#ecf0f1', fg='#2c3e50').pack(anchor='w')

        # أزرار إدارة الشعار
        logo_buttons_frame = tk.Frame(logo_frame, bg='#ecf0f1')
        logo_buttons_frame.grid(row=2, column=0, columnspan=2, pady=10)

        tk.Button(logo_buttons_frame, text="📁 اختيار شعار جديد", font=('Arial', 10, 'bold'),
                 bg='#3498db', fg='white', relief='raised', bd=3,
                 command=self.select_logo).pack(side=tk.LEFT, padx=5)

        tk.Button(logo_buttons_frame, text="👁️ معاينة الشعار", font=('Arial', 10, 'bold'),
                 bg='#9b59b6', fg='white', relief='raised', bd=3,
                 command=self.preview_logo).pack(side=tk.LEFT, padx=5)

        tk.Button(logo_buttons_frame, text="🔄 استخدام الشعار الافتراضي", font=('Arial', 10, 'bold'),
                 bg='#f39c12', fg='white', relief='raised', bd=3,
                 command=self.reset_default_logo).pack(side=tk.LEFT, padx=5)

        # معلومات المختبر
        info_frame = tk.LabelFrame(frame, text="معلومات المختبر",
                                  font=('Arial', 12, 'bold'), bg='#ecf0f1')
        info_frame.pack(fill=tk.X, padx=10, pady=10)

        # العنوان العربي
        tk.Label(info_frame, text="العنوان العربي:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=0, padx=5, pady=5, sticky='nw')
        self.arabic_header = tk.Text(info_frame, height=5, width=40, font=('Arial', 9))
        self.arabic_header.grid(row=0, column=1, padx=5, pady=5)
        self.arabic_header.insert('1.0', '''جمهورية العراق
وزارة الصحة
قسم الصحة العامة
مختبر الصحة العامة
وحدة الفايروسات''')

        # العنوان الإنجليزي
        tk.Label(info_frame, text="العنوان الإنجليزي:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=0, column=2, padx=5, pady=5, sticky='nw')
        self.english_header = tk.Text(info_frame, height=5, width=40, font=('Arial', 9))
        self.english_header.grid(row=0, column=3, padx=5, pady=5)
        self.english_header.insert('1.0', '''Republic of Iraq
Ministry of Health
Public Health Department
Public Health Laboratory
Virus Unit''')

        # العنوان والإيميل
        tk.Label(info_frame, text="العنوان:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=1, column=0, padx=5, pady=5)
        self.lab_address = tk.StringVar(value="ذي قار /الناصرية /سومر/ مجاور حسينية السفراء الاربعة")
        tk.Entry(info_frame, textvariable=self.lab_address, width=50).grid(
            row=1, column=1, columnspan=2, padx=5, pady=5)

        tk.Label(info_frame, text="الإيميل:", font=('Arial', 10, 'bold'),
                bg='#ecf0f1').grid(row=2, column=0, padx=5, pady=5)
        self.lab_email = tk.StringVar()
        tk.Entry(info_frame, textvariable=self.lab_email, width=30).grid(
            row=2, column=1, padx=5, pady=5)

        # زر حفظ الإعدادات
        tk.Button(info_frame, text="💾 حفظ جميع الإعدادات", font=('Arial', 10, 'bold'),
                 bg='#27ae60', fg='white', relief='raised', bd=3,
                 command=self.save_report_settings).grid(row=3, column=1, pady=10)

    def add_setting(self, category, value):
        """إضافة إعداد جديد"""
        if not value.strip():
            messagebox.showerror("خطأ", "يرجى إدخال القيمة")
            return

        try:
            self.cursor.execute('''
                INSERT INTO settings (category, name, value)
                VALUES (?, ?, ?)
            ''', (category, value, value))
            self.conn.commit()

            # تحديث القائمة المناسبة
            if category == 'sample_types':
                self.refresh_settings_list(category, self.sample_types_listbox)
                self.sample_type_entry.set("")
            elif category == 'sender_orgs':
                self.refresh_settings_list(category, self.sender_orgs_listbox)
                self.sender_org_entry.set("")
            elif category == 'tests':
                self.refresh_settings_list(category, self.tests_listbox)
                self.test_entry.set("")
            elif category == 'technicians':
                self.refresh_settings_list(category, self.technicians_listbox)
                self.technician_entry.set("")

            messagebox.showinfo("نجح", "تم إضافة الإعداد بنجاح")

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "هذا الإعداد موجود مسبقاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الإضافة: {str(e)}")

    def edit_setting(self, category):
        """تعديل إعداد"""
        messagebox.showinfo("قيد التطوير", "تعديل الإعدادات قيد التطوير")

    def delete_setting(self, category):
        """حذف إعداد"""
        # تحديد القائمة المناسبة
        if category == 'sample_types':
            listbox = self.sample_types_listbox
        elif category == 'sender_orgs':
            listbox = self.sender_orgs_listbox
        elif category == 'tests':
            listbox = self.tests_listbox
        elif category == 'technicians':
            listbox = self.technicians_listbox
        else:
            return

        selection = listbox.curselection()
        if not selection:
            messagebox.showerror("خطأ", "يرجى اختيار عنصر للحذف")
            return

        selected_value = listbox.get(selection[0])

        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف '{selected_value}'؟"):
            try:
                self.cursor.execute('''
                    DELETE FROM settings WHERE category = ? AND value = ?
                ''', (category, selected_value))
                self.conn.commit()

                self.refresh_settings_list(category, listbox)
                messagebox.showinfo("نجح", "تم حذف الإعداد بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    def refresh_settings_list(self, category, listbox):
        """تحديث قائمة الإعدادات"""
        listbox.delete(0, tk.END)
        self.cursor.execute("SELECT value FROM settings WHERE category = ?", (category,))
        values = [row[0] for row in self.cursor.fetchall()]
        for value in values:
            listbox.insert(tk.END, value)

    def select_logo(self):
        """اختيار شعار جديد"""
        file_path = filedialog.askopenfilename(
            title="اختيار شعار التقرير",
            filetypes=[
                ("صور", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            try:
                # نسخ الملف إلى مجلد البرنامج
                import shutil
                new_logo_path = "custom_logo.png"

                # تحويل الصورة إلى PNG إذا لزم الأمر
                from PIL import Image
                img = Image.open(file_path)
                # تغيير حجم الصورة إذا كانت كبيرة جداً
                if img.width > 300 or img.height > 300:
                    img.thumbnail((300, 300), Image.Resampling.LANCZOS)
                img.save(new_logo_path, "PNG")

                self.logo_path.set(new_logo_path)
                self.logo_label.config(text=os.path.basename(file_path), fg='#27ae60')

                messagebox.showinfo("نجح", f"تم اختيار الشعار الجديد: {os.path.basename(file_path)}")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل الشعار: {str(e)}")

    def preview_logo(self):
        """معاينة الشعار"""
        logo_file = self.logo_path.get()

        if not os.path.exists(logo_file):
            messagebox.showerror("خطأ", "ملف الشعار غير موجود")
            return

        try:
            # إنشاء نافذة المعاينة
            preview_window = tk.Toplevel(self.root)
            preview_window.title("معاينة الشعار")
            preview_window.geometry("400x400")
            preview_window.configure(bg='white')

            # تحميل وعرض الصورة
            from PIL import Image, ImageTk
            img = Image.open(logo_file)

            # تغيير حجم الصورة للمعاينة
            img.thumbnail((300, 300), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(img)

            tk.Label(preview_window, text="معاينة الشعار",
                    font=('Arial', 14, 'bold')).pack(pady=10)

            img_label = tk.Label(preview_window, image=photo, bg='white')
            img_label.image = photo  # الاحتفاظ بمرجع للصورة
            img_label.pack(pady=20)

            tk.Label(preview_window, text=f"الملف: {os.path.basename(logo_file)}",
                    font=('Arial', 10)).pack(pady=5)

            tk.Button(preview_window, text="إغلاق", font=('Arial', 10, 'bold'),
                     bg='#e74c3c', fg='white', command=preview_window.destroy).pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء معاينة الشعار: {str(e)}")

    def reset_default_logo(self):
        """استخدام الشعار الافتراضي"""
        if messagebox.askyesno("تأكيد", "هل تريد استخدام الشعار الافتراضي؟"):
            self.logo_path.set("ministry_logo.png")
            self.logo_label.config(text="ministry_logo.png (افتراضي)", fg='#3498db')
            messagebox.showinfo("نجح", "تم تعيين الشعار الافتراضي")

    def save_report_settings(self):
        """حفظ إعدادات التقرير"""
        try:
            # حفظ العناوين والمعلومات
            settings_data = [
                ('report_settings', 'arabic_header', self.arabic_header.get('1.0', tk.END).strip()),
                ('report_settings', 'english_header', self.english_header.get('1.0', tk.END).strip()),
                ('report_settings', 'lab_address', self.lab_address.get()),
                ('report_settings', 'lab_email', self.lab_email.get()),
                ('report_settings', 'logo_path', self.logo_path.get()),
                ('report_settings', 'show_logo', str(self.show_logo.get()))
            ]

            for category, name, value in settings_data:
                self.cursor.execute('''
                    INSERT OR REPLACE INTO settings (category, name, value)
                    VALUES (?, ?, ?)
                ''', (category, name, value))

            self.conn.commit()
            messagebox.showinfo("نجح", "تم حفظ جميع إعدادات التقرير بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")

    def load_report_settings(self):
        """تحميل إعدادات التقرير المحفوظة"""
        try:
            # تحميل إعداد إظهار الشعار
            self.cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'show_logo'")
            result = self.cursor.fetchone()
            if result:
                show_logo_value = result[0].lower() == 'true'
                if hasattr(self, 'show_logo'):
                    self.show_logo.set(show_logo_value)

            # تحميل مسار الشعار
            self.cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'logo_path'")
            result = self.cursor.fetchone()
            if result and hasattr(self, 'logo_path'):
                self.logo_path.set(result[0])

        except Exception as e:
            print(f"خطأ في تحميل إعدادات التقرير: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = LabManagementSystem(root)
    root.mainloop()
