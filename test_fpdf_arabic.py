#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار fpdf2 للنصوص العربية
FPDF2 Arabic Text Test
"""

import os
import datetime

def test_fpdf_arabic():
    """اختبار fpdf2 مع النصوص العربية"""
    print("🔍 اختبار fpdf2 للنصوص العربية...")
    
    try:
        from fpdf import FPDF
        import arabic_reshaper
        from bidi.algorithm import get_display
        print("✅ fpdf2: متوفر")
        print("✅ arabic_reshaper: متوفر")
        print("✅ python-bidi: متوفر")
        
        # إنشاء كلاس PDF مخصص للنصوص العربية
        class ArabicPDF(FPDF):
            def __init__(self):
                super().__init__()
                self.add_page()
                
                # تحميل خط عربي
                try:
                    # محاولة تحميل خط Arial
                    self.add_font('Arial', '', 'C:/Windows/Fonts/arial.ttf', uni=True)
                    self.add_font('Arial', 'B', 'C:/Windows/Fonts/arialbd.ttf', uni=True)
                    self.arabic_font = 'Arial'
                    print("✅ تم تحميل خط Arial")
                except:
                    try:
                        # محاولة تحميل خط Tahoma
                        self.add_font('Tahoma', '', 'C:/Windows/Fonts/tahoma.ttf', uni=True)
                        self.add_font('Tahoma', 'B', 'C:/Windows/Fonts/tahomabd.ttf', uni=True)
                        self.arabic_font = 'Tahoma'
                        print("✅ تم تحميل خط Tahoma")
                    except:
                        # استخدام خط افتراضي
                        self.arabic_font = 'Arial'
                        print("⚠️ استخدام خط افتراضي")
            
            def process_arabic_text(self, text):
                """معالجة النص العربي"""
                try:
                    if not text:
                        return ""
                    
                    # فحص إذا كان النص يحتوي على عربي
                    has_arabic = any('\u0600' <= char <= '\u06FF' for char in str(text))
                    
                    if has_arabic:
                        # إعادة تشكيل النص العربي
                        reshaped_text = arabic_reshaper.reshape(str(text))
                        # تطبيق خوارزمية bidi
                        bidi_text = get_display(reshaped_text)
                        return bidi_text
                    else:
                        return str(text)
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة النص: {e}")
                    return str(text)
            
            def add_arabic_text(self, x, y, text, size=12, style=''):
                """إضافة نص عربي في المنتصف"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    self.set_xy(x, y)
                    self.cell(0, 10, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص: {e}")
                    # محاولة بدون معالجة
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(0, 10, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_arabic_text_right(self, x, y, text, size=12, style=''):
                """إضافة نص عربي في الجهة اليمنى مع محاذاة وسط"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليمنى (95 وحدة عرض)
                    self.cell(95, 8, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص العربي الأيمن: {e}")
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(95, 8, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_english_text_left(self, x, y, text, size=12, style=''):
                """إضافة نص إنجليزي في الجهة اليسرى مع محاذاة وسط"""
                try:
                    self.set_font('Arial', style, size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليسرى (95 وحدة عرض)
                    self.cell(95, 8, str(text), 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص الإنجليزي الأيسر: {e}")
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(95, 8, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_footer_text_left(self, x, y, text, size=9):
                """إضافة نص في التذييل الأيسر"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, '', size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليسرى (95 وحدة عرض)
                    self.cell(95, 6, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة تذييل أيسر: {e}")
                    try:
                        self.set_font('Arial', '', size)
                        self.set_xy(x, y)
                        self.cell(95, 6, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_footer_text_right(self, x, y, text, size=9):
                """إضافة نص في التذييل الأيمن"""
                try:
                    self.set_font('Arial', '', size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليمنى (95 وحدة عرض)
                    self.cell(95, 6, str(text), 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة تذييل أيمن: {e}")
                    try:
                        self.set_font('Arial', '', size)
                        self.set_xy(x, y)
                        self.cell(95, 6, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_arabic_text_right_compact(self, x, y, text, size=12, style=''):
                """إضافة نص عربي في الجهة اليمنى مع تباعد مضغوط"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليمنى (95 وحدة عرض) مع ارتفاع مضغوط
                    self.cell(95, 5, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص العربي الأيمن المضغوط: {e}")
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(95, 5, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_english_text_left_compact(self, x, y, text, size=12, style=''):
                """إضافة نص إنجليزي في الجهة اليسرى مع تباعد مضغوط"""
                try:
                    self.set_font('Arial', style, size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليسرى (95 وحدة عرض) مع ارتفاع مضغوط
                    self.cell(95, 5, str(text), 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص الإنجليزي الأيسر المضغوط: {e}")
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(95, 5, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_footer_single_line(self, y, address_text, email_text, size=9):
                """إضافة تذييل في سطر واحد"""
                try:
                    # العنوان في الجهة اليسرى
                    processed_address = self.process_arabic_text(address_text)
                    self.set_font(self.arabic_font, '', size)
                    self.set_xy(10, y)
                    self.cell(95, 6, processed_address, 0, 0, 'C')

                    # الإيميل في الجهة اليمنى
                    self.set_font('Arial', '', size)
                    self.set_xy(105, y)
                    self.cell(95, 6, str(email_text), 0, 1, 'C')

                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة التذييل المفرد: {e}")
                    return False

            def add_header_footer_line(self, y, address_text, email_text, size=9):
                """إضافة العنوان والإيميل في أعلى الصفحة"""
                try:
                    # العنوان في الجهة اليسرى (بداية من 0.5 سم)
                    processed_address = self.process_arabic_text(address_text)
                    self.set_font(self.arabic_font, '', size)
                    self.set_xy(14, y)  # 0.5 سم = 14 نقطة
                    self.cell(90, 6, processed_address, 0, 0, 'L')

                    # الإيميل في الجهة اليمنى (بداية من 0.5 سم من اليمين)
                    self.set_font('Arial', '', size)
                    self.set_xy(110, y)  # من اليمين مع مسافة 0.5 سم
                    self.cell(90, 6, str(email_text), 0, 1, 'R')

                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة العنوان والإيميل العلوي: {e}")
                    return False

            def add_arabic_text_right_margin(self, margin, y, text, size=12, style=''):
                """إضافة نص عربي في الجهة اليمنى مع هامش محدد"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    # حساب الموضع من اليمين مع الهامش
                    x_pos = 210 - margin - 90  # عرض الصفحة - الهامش - عرض النص
                    self.set_xy(x_pos, y)
                    self.cell(90, 5, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص العربي بهامش: {e}")
                    return False

            def add_english_text_left_margin(self, margin, y, text, size=12, style=''):
                """إضافة نص إنجليزي في الجهة اليسرى مع هامش محدد"""
                try:
                    self.set_font('Arial', style, size)
                    # الموضع من اليسار مع الهامش
                    self.set_xy(margin, y)
                    self.cell(90, 5, str(text), 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص الإنجليزي بهامش: {e}")
                    return False

            def add_arabic_text_center(self, y, text, size=12, style=''):
                """إضافة نص عربي في الوسط"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    # محاذاة في الوسط - عرض الصفحة كامل
                    self.set_xy(0, y)
                    self.cell(210, 6, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص العربي الوسط: {e}")
                    return False

            def add_english_text_center(self, y, text, size=12, style=''):
                """إضافة نص إنجليزي في الوسط"""
                try:
                    self.set_font('Arial', style, size)
                    # محاذاة في الوسط - عرض الصفحة كامل
                    self.set_xy(0, y)
                    self.cell(210, 6, str(text), 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص الإنجليزي الوسط: {e}")
                    return False

            def add_data_table_enhanced(self, start_y):
                """إضافة جدول البيانات المحسن مع إرجاع موضع النهاية"""
                try:
                    # رؤوس الجدول
                    headers = ["الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحليل", "النتيجة"]

                    # عرض الأعمدة محسن
                    col_widths = [24, 28, 14, 14, 18, 28, 22, 22]

                    # حساب العرض الإجمالي للجدول
                    total_table_width = sum(col_widths)

                    # حساب موضع البداية لتوسيط الجدول (عرض الصفحة 210)
                    table_start_x = (210 - total_table_width) / 2

                    # رسم رؤوس الجدول في منتصف الورقة
                    self.set_xy(table_start_x, start_y)
                    self.set_font(self.arabic_font, 'B', 9)

                    x_pos = table_start_x
                    for i, header in enumerate(headers):
                        processed_header = self.process_arabic_text(header)
                        self.set_xy(x_pos, start_y)
                        self.cell(col_widths[i], 8, processed_header, 1, 0, 'C')
                        x_pos += col_widths[i]

                    # بيانات تجريبية
                    test_data = [
                        ["12345678901", "أحمد محمد علي", "35", "ذكر", "دم", "مستشفى الناصرية", "CBC", "طبيعي"],
                        ["12345678902", "فاطمة حسن", "28", "أنثى", "بول", "مركز صحي السوق", "Urine", "طبيعي"],
                        ["12345678903", "علي أحمد", "42", "ذكر", "براز", "مستشفى الحبوبي", "Stool", "طبيعي"],
                        ["12345678904", "زينب كاظم", "31", "أنثى", "دم", "مستشفى الناصرية", "CBC", "طبيعي"],
                        ["12345678905", "محمد جاسم", "45", "ذكر", "بول", "مركز صحي الشطرة", "Urine", "طبيعي"],
                        ["12345678906", "سارة أحمد", "29", "أنثى", "دم", "مستشفى الحبوبي", "CBC", "طبيعي"],
                        ["12345678907", "حسام علي", "38", "ذكر", "بول", "مركز صحي الناصرية", "Urine", "طبيعي"],
                        ["12345678908", "نور فاطمة", "26", "أنثى", "دم", "مستشفى الناصرية", "CBC", "طبيعي"],
                        ["12345678909", "كريم محمد", "33", "ذكر", "بول", "مركز صحي السوق", "Urine", "طبيعي"],
                        ["12345678910", "هدى علي", "41", "أنثى", "براز", "مستشفى الحبوبي", "Stool", "طبيعي"],
                        ["12345678911", "عمار حسن", "37", "ذكر", "دم", "مستشفى الناصرية", "CBC", "طبيعي"],
                        ["12345678912", "ريم أحمد", "24", "أنثى", "بول", "مركز صحي الشطرة", "Urine", "طبيعي"]
                    ]

                    # رسم البيانات
                    y_pos = start_y + 8
                    self.set_font(self.arabic_font, '', 8)

                    rows_added = 0
                    for row_data in test_data[:12]:  # أول 12 صف لضمان المساحة للتذييل
                        x_pos = table_start_x  # نفس موضع بداية الجدول
                        for i, value in enumerate(row_data):
                            if i < len(col_widths):
                                # تقصير النص الطويل للوضوح
                                text = str(value)[:12] + "..." if len(str(value)) > 12 else str(value)
                                processed_text = self.process_arabic_text(text)
                                self.set_xy(x_pos, y_pos)
                                self.cell(col_widths[i], 7, processed_text, 1, 0, 'C')
                                x_pos += col_widths[i]
                        y_pos += 7
                        rows_added += 1
                        if y_pos > 240:  # تجنب تجاوز الصفحة مع ترك مساحة للتذييل
                            break

                    print(f"✅ تم رسم الجدول المحسن ({rows_added} صف)")

                    # إحصائيات التقرير
                    y_pos += 10
                    self.add_arabic_text(0, y_pos, f"إجمالي عدد السجلات في التقرير: {len(test_data)}", 11, 'B')

                    return y_pos + 10  # إرجاع موضع نهاية الجدول

                except Exception as e:
                    print(f"❌ خطأ في إضافة الجدول المحسن: {e}")
                    return start_y + 100  # إرجاع موضع افتراضي في حالة الخطأ

            def add_footer_single_line_fixed(self, y, address_text, email_text, size=9):
                """إضافة تذييل في سطر واحد مع تثبيت الموضع"""
                try:
                    # العنوان في الجهة اليسرى
                    processed_address = self.process_arabic_text(address_text)
                    self.set_font(self.arabic_font, '', size)
                    self.set_xy(10, y)
                    self.cell(90, 6, processed_address, 0, 0, 'L')

                    # الإيميل في الجهة اليمنى
                    self.set_font('Arial', '', size)
                    self.set_xy(110, y)
                    self.cell(90, 6, str(email_text), 0, 1, 'R')

                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة التذييل المثبت: {e}")
                    return False
        
        # إنشاء PDF تجريبي
        pdf = ArabicPDF()

        # إضافة العناوين العربية في الوسط (بعد مسافة قليلة جداً من بداية الصفحة)
        arabic_titles = [
            ("جمهورية العراق", 14, 'B'),
            ("وزارة الصحة", 12, 'B'),
            ("قسم الصحة العامة", 11, 'B'),
            ("مختبر الصحة العامة", 11, 'B'),
            ("وحدة الفايروسات", 10, 'B')
        ]

        y_pos = 8  # مسافة قليلة جداً من بداية الصفحة
        for title, size, style in arabic_titles:
            # محاذاة في الوسط
            success = pdf.add_arabic_text_center(y_pos, title, size, style)
            if success:
                print(f"✅ تم إضافة العنوان العربي في الوسط: {title}")
            else:
                print(f"❌ فشل في إضافة العنوان العربي: {title}")
            y_pos += 7  # مسافة ثابتة بين السطور

        # إضافة العناوين الإنجليزية في الوسط (تحت العربية)
        english_titles = [
            ("Republic of Iraq", 14, 'B'),
            ("Ministry of Health", 12, 'B'),
            ("Public Health Department", 11, 'B'),
            ("Public Health Laboratory", 11, 'B'),
            ("Virus Unit", 10, 'B')
        ]

        y_pos += 5  # مسافة صغيرة بين العربية والإنجليزية
        for title, size, style in english_titles:
            # محاذاة في الوسط
            success = pdf.add_english_text_center(y_pos, title, size, style)
            if success:
                print(f"✅ تم إضافة العنوان الإنجليزي في الوسط: {title}")
            else:
                print(f"❌ فشل في إضافة العنوان الإنجليزي: {title}")
            y_pos += 7  # نفس المسافة للعناوين الإنجليزية
        
        # إضافة الشعار في منتصف الصفحة بعد العناوين
        # محاكاة الشعار بمستطيل أصغر
        pdf.set_fill_color(200, 200, 200)
        pdf.rect(90, y_pos + 5, 30, 30, 'F')
        pdf.set_font('Arial', 'B', 7)
        pdf.set_xy(90, y_pos + 18)
        pdf.cell(30, 8, "LOGO", 0, 1, 'C')
        print("✅ تم إضافة الشعار في منتصف الصفحة")

        start_y = y_pos + 45  # بداية المحتوى بعد الشعار

        # عنوان التقرير
        pdf.add_arabic_text(0, start_y, "تقرير نتائج الفحوصات المختبرية", 16, 'B')
        start_y += 12
        pdf.set_font('Arial', 'B', 12)
        pdf.set_xy(0, start_y)
        pdf.cell(0, 8, "Laboratory Test Results Report", 0, 1, 'C')
        start_y += 10

        # التاريخ والوقت
        current_date = datetime.datetime.now()
        arabic_date = current_date.strftime("%Y/%m/%d")
        arabic_time = current_date.strftime("%H:%M:%S")

        pdf.add_arabic_text(0, start_y, f"تاريخ إنشاء التقرير: {arabic_date}", 10)
        start_y += 8
        pdf.add_arabic_text(0, start_y, f"وقت الإنشاء: {arabic_time}", 10)
        start_y += 15
        
        # جدول تجريبي محسن
        table_end_y = pdf.add_data_table_enhanced(start_y)

        # التذييل في أسفل الصفحة (العنوان والإيميل) - في نفس الصفحة
        footer_y = max(table_end_y + 15, 270)  # على الأقل 15 نقطة بعد الجدول أو في الموضع 270
        address_text = "ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة"
        email_text = "<EMAIL>"
        success = pdf.add_footer_single_line_fixed(footer_y, address_text, email_text, 9)
        if success:
            print("✅ تم إضافة التذييل المثبت في أسفل الصفحة")
        else:
            print("❌ فشل في إضافة التذييل")

        # رقم الصفحة في أسفل الصفحة
        pdf.add_arabic_text(0, footer_y + 8, "صفحة 1", 8)
        print("✅ تم إضافة رقم الصفحة")
        
        # حفظ الملف
        filename = f"اختبار_fpdf_عربي_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        pdf.output(filename)
        
        if os.path.exists(filename):
            print(f"✅ تم إنشاء ملف PDF عربي: {filename}")
            
            # فتح الملف
            try:
                os.startfile(filename)
                print("📂 تم فتح الملف للمراجعة")
            except:
                print("📂 يمكنك فتح الملف يدوياً")
            
            return True
        else:
            print("❌ فشل في إنشاء الملف")
            return False
            
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("💡 لتثبيت المكتبات المطلوبة:")
        print("   py -m pip install fpdf2")
        print("   py -m pip install arabic-reshaper")
        print("   py -m pip install python-bidi")
        return False
    except Exception as e:
        print(f"❌ خطأ في fpdf2: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🇸🇦 اختبار fpdf2 للنصوص العربية")
    print("=" * 60)
    
    # اختبار fpdf2
    fpdf_test = test_fpdf_arabic()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"fpdf2 (النصوص العربية): {'✅ نجح' if fpdf_test else '❌ فشل'}")
    
    if fpdf_test:
        print("\n🎉 fpdf2 يعمل بشكل ممتاز!")
        print("✅ النصوص العربية تظهر بشكل صحيح")
        print("✅ الخطوط العربية تعمل بشكل مثالي")
        print("✅ الجداول والتنسيق يعملان بشكل احترافي")
        print("✅ معالجة النصوص العربية تلقائية")
        print("\n🚀 البرنامج سيستخدم fpdf2 لإنشاء التقارير العربية!")
        print("\n📋 المزايا:")
        print("   🔤 دعم كامل للخطوط العربية")
        print("   📝 معالجة تلقائية للنصوص العربية")
        print("   📊 جداول احترافية")
        print("   🖼️ دعم الصور والشعارات")
        print("   ⚡ سرعة عالية في الإنشاء")
    else:
        print("\n❌ fpdf2 لا يعمل")
        print("💡 تأكد من تثبيت المكتبات:")
        print("   py -m pip install fpdf2")
        print("   py -m pip install arabic-reshaper")
        print("   py -m pip install python-bidi")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
