#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار fpdf2 للنصوص العربية
FPDF2 Arabic Text Test
"""

import os
import datetime

def test_fpdf_arabic():
    """اختبار fpdf2 مع النصوص العربية"""
    print("🔍 اختبار fpdf2 للنصوص العربية...")
    
    try:
        from fpdf import FPDF
        import arabic_reshaper
        from bidi.algorithm import get_display
        print("✅ fpdf2: متوفر")
        print("✅ arabic_reshaper: متوفر")
        print("✅ python-bidi: متوفر")
        
        # إنشاء كلاس PDF مخصص للنصوص العربية
        class ArabicPDF(FPDF):
            def __init__(self):
                super().__init__()
                self.add_page()
                
                # تحميل خط عربي
                try:
                    # محاولة تحميل خط Arial
                    self.add_font('Arial', '', 'C:/Windows/Fonts/arial.ttf', uni=True)
                    self.add_font('Arial', 'B', 'C:/Windows/Fonts/arialbd.ttf', uni=True)
                    self.arabic_font = 'Arial'
                    print("✅ تم تحميل خط Arial")
                except:
                    try:
                        # محاولة تحميل خط Tahoma
                        self.add_font('Tahoma', '', 'C:/Windows/Fonts/tahoma.ttf', uni=True)
                        self.add_font('Tahoma', 'B', 'C:/Windows/Fonts/tahomabd.ttf', uni=True)
                        self.arabic_font = 'Tahoma'
                        print("✅ تم تحميل خط Tahoma")
                    except:
                        # استخدام خط افتراضي
                        self.arabic_font = 'Arial'
                        print("⚠️ استخدام خط افتراضي")
            
            def process_arabic_text(self, text):
                """معالجة النص العربي"""
                try:
                    if not text:
                        return ""
                    
                    # فحص إذا كان النص يحتوي على عربي
                    has_arabic = any('\u0600' <= char <= '\u06FF' for char in str(text))
                    
                    if has_arabic:
                        # إعادة تشكيل النص العربي
                        reshaped_text = arabic_reshaper.reshape(str(text))
                        # تطبيق خوارزمية bidi
                        bidi_text = get_display(reshaped_text)
                        return bidi_text
                    else:
                        return str(text)
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة النص: {e}")
                    return str(text)
            
            def add_arabic_text(self, x, y, text, size=12, style=''):
                """إضافة نص عربي"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    self.set_xy(x, y)
                    self.cell(0, 10, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص: {e}")
                    # محاولة بدون معالجة
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(0, 10, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False
        
        # إنشاء PDF تجريبي
        pdf = ArabicPDF()
        
        # العناوين الرسمية
        y_pos = 20
        titles = [
            ("جمهورية العراق", 16, 'B'),
            ("وزارة الصحة", 14, 'B'),
            ("قسم الصحة العامة - محافظة ذي قار", 12, 'B'),
            ("مختبر الصحة العامة المركزي", 12, 'B'),
            ("وحدة الفايروسات والأحياء المجهرية", 10, 'B')
        ]
        
        for title, size, style in titles:
            success = pdf.add_arabic_text(0, y_pos, title, size, style)
            if success:
                print(f"✅ تم إضافة: {title}")
            else:
                print(f"❌ فشل في إضافة: {title}")
            y_pos += 12
        
        # عنوان التقرير
        y_pos += 10
        pdf.add_arabic_text(0, y_pos, "تقرير نتائج الفحوصات المختبرية", 18, 'B')
        y_pos += 15
        pdf.set_font('Arial', 'B', 14)
        pdf.set_xy(0, y_pos)
        pdf.cell(0, 10, "Laboratory Test Results Report", 0, 1, 'C')
        
        # التاريخ والوقت
        current_date = datetime.datetime.now()
        arabic_date = current_date.strftime("%Y/%m/%d")
        arabic_time = current_date.strftime("%H:%M:%S")
        
        y_pos += 20
        pdf.add_arabic_text(0, y_pos, f"تاريخ إنشاء التقرير: {arabic_date}", 12)
        y_pos += 10
        pdf.add_arabic_text(0, y_pos, f"وقت الإنشاء: {arabic_time}", 12)
        
        # جدول تجريبي
        y_pos += 20
        headers = ["الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحليل", "النتيجة"]
        col_widths = [25, 30, 15, 15, 20, 30, 25, 25]
        
        # رسم رؤوس الجدول
        pdf.set_xy(10, y_pos)
        pdf.set_font(pdf.arabic_font, 'B', 8)
        
        x_pos = 10
        for i, header in enumerate(headers):
            processed_header = pdf.process_arabic_text(header)
            pdf.set_xy(x_pos, y_pos)
            pdf.cell(col_widths[i], 8, processed_header, 1, 0, 'C')
            x_pos += col_widths[i]
        print("✅ تم رسم رؤوس الجدول")
        
        # بيانات تجريبية
        test_data = [
            ["5001", "أحمد محمد علي", "35", "ذكر", "دم", "مستشفى بغداد", "كوفيد-19", "سلبي"],
            ["5002", "فاطمة حسن", "28", "أنثى", "بول", "مستشفى البصرة", "التهاب الكبد", "إيجابي"],
            ["5003", "علي أحمد", "42", "ذكر", "دم", "مستشفى الموصل", "كوفيد-19", "سلبي"]
        ]
        
        # رسم البيانات
        y_pos += 8
        pdf.set_font(pdf.arabic_font, '', 7)
        
        for row_data in test_data:
            x_pos = 10
            for i, value in enumerate(row_data):
                if i < len(col_widths):
                    # تحويل الجنس للعربية
                    if i == 3:  # الجنس
                        value = "ذكر" if str(value) == "M" else "أنثى" if str(value) == "F" else str(value)
                    
                    processed_text = pdf.process_arabic_text(str(value))
                    pdf.set_xy(x_pos, y_pos)
                    pdf.cell(col_widths[i], 6, processed_text, 1, 0, 'C')
                    x_pos += col_widths[i]
            
            y_pos += 6
        print("✅ تم رسم البيانات")
        
        # إحصائيات التقرير
        y_pos += 15
        pdf.add_arabic_text(0, y_pos, f"إجمالي عدد السجلات في التقرير: {len(test_data)}", 12, 'B')
        
        # التذييل
        y_pos += 20
        pdf.add_arabic_text(0, y_pos, "العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة", 10)
        y_pos += 8
        pdf.add_arabic_text(0, y_pos, "مختبر الصحة العامة المركزي - وحدة الفايروسات والأحياء المجهرية", 9)
        y_pos += 8
        pdf.add_arabic_text(0, y_pos, "صفحة 1", 8)
        
        # حفظ الملف
        filename = f"اختبار_fpdf_عربي_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        pdf.output(filename)
        
        if os.path.exists(filename):
            print(f"✅ تم إنشاء ملف PDF عربي: {filename}")
            
            # فتح الملف
            try:
                os.startfile(filename)
                print("📂 تم فتح الملف للمراجعة")
            except:
                print("📂 يمكنك فتح الملف يدوياً")
            
            return True
        else:
            print("❌ فشل في إنشاء الملف")
            return False
            
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("💡 لتثبيت المكتبات المطلوبة:")
        print("   py -m pip install fpdf2")
        print("   py -m pip install arabic-reshaper")
        print("   py -m pip install python-bidi")
        return False
    except Exception as e:
        print(f"❌ خطأ في fpdf2: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🇸🇦 اختبار fpdf2 للنصوص العربية")
    print("=" * 60)
    
    # اختبار fpdf2
    fpdf_test = test_fpdf_arabic()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"fpdf2 (النصوص العربية): {'✅ نجح' if fpdf_test else '❌ فشل'}")
    
    if fpdf_test:
        print("\n🎉 fpdf2 يعمل بشكل ممتاز!")
        print("✅ النصوص العربية تظهر بشكل صحيح")
        print("✅ الخطوط العربية تعمل بشكل مثالي")
        print("✅ الجداول والتنسيق يعملان بشكل احترافي")
        print("✅ معالجة النصوص العربية تلقائية")
        print("\n🚀 البرنامج سيستخدم fpdf2 لإنشاء التقارير العربية!")
        print("\n📋 المزايا:")
        print("   🔤 دعم كامل للخطوط العربية")
        print("   📝 معالجة تلقائية للنصوص العربية")
        print("   📊 جداول احترافية")
        print("   🖼️ دعم الصور والشعارات")
        print("   ⚡ سرعة عالية في الإنشاء")
    else:
        print("\n❌ fpdf2 لا يعمل")
        print("💡 تأكد من تثبيت المكتبات:")
        print("   py -m pip install fpdf2")
        print("   py -m pip install arabic-reshaper")
        print("   py -m pip install python-bidi")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
