#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار fpdf2 للنصوص العربية
FPDF2 Arabic Text Test
"""

import os
import datetime

def test_fpdf_arabic():
    """اختبار fpdf2 مع النصوص العربية"""
    print("🔍 اختبار fpdf2 للنصوص العربية...")
    
    try:
        from fpdf import FPDF
        import arabic_reshaper
        from bidi.algorithm import get_display
        print("✅ fpdf2: متوفر")
        print("✅ arabic_reshaper: متوفر")
        print("✅ python-bidi: متوفر")
        
        # إنشاء كلاس PDF مخصص للنصوص العربية
        class ArabicPDF(FPDF):
            def __init__(self):
                super().__init__()
                self.add_page()
                
                # تحميل خط عربي
                try:
                    # محاولة تحميل خط Arial
                    self.add_font('Arial', '', 'C:/Windows/Fonts/arial.ttf', uni=True)
                    self.add_font('Arial', 'B', 'C:/Windows/Fonts/arialbd.ttf', uni=True)
                    self.arabic_font = 'Arial'
                    print("✅ تم تحميل خط Arial")
                except:
                    try:
                        # محاولة تحميل خط Tahoma
                        self.add_font('Tahoma', '', 'C:/Windows/Fonts/tahoma.ttf', uni=True)
                        self.add_font('Tahoma', 'B', 'C:/Windows/Fonts/tahomabd.ttf', uni=True)
                        self.arabic_font = 'Tahoma'
                        print("✅ تم تحميل خط Tahoma")
                    except:
                        # استخدام خط افتراضي
                        self.arabic_font = 'Arial'
                        print("⚠️ استخدام خط افتراضي")
            
            def process_arabic_text(self, text):
                """معالجة النص العربي"""
                try:
                    if not text:
                        return ""
                    
                    # فحص إذا كان النص يحتوي على عربي
                    has_arabic = any('\u0600' <= char <= '\u06FF' for char in str(text))
                    
                    if has_arabic:
                        # إعادة تشكيل النص العربي
                        reshaped_text = arabic_reshaper.reshape(str(text))
                        # تطبيق خوارزمية bidi
                        bidi_text = get_display(reshaped_text)
                        return bidi_text
                    else:
                        return str(text)
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة النص: {e}")
                    return str(text)
            
            def add_arabic_text(self, x, y, text, size=12, style=''):
                """إضافة نص عربي في المنتصف"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    self.set_xy(x, y)
                    self.cell(0, 10, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص: {e}")
                    # محاولة بدون معالجة
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(0, 10, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_arabic_text_right(self, x, y, text, size=12, style=''):
                """إضافة نص عربي في الجهة اليمنى مع محاذاة وسط"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليمنى (95 وحدة عرض)
                    self.cell(95, 8, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص العربي الأيمن: {e}")
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(95, 8, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_english_text_left(self, x, y, text, size=12, style=''):
                """إضافة نص إنجليزي في الجهة اليسرى مع محاذاة وسط"""
                try:
                    self.set_font('Arial', style, size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليسرى (95 وحدة عرض)
                    self.cell(95, 8, str(text), 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص الإنجليزي الأيسر: {e}")
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(95, 8, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_footer_text_left(self, x, y, text, size=9):
                """إضافة نص في التذييل الأيسر"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, '', size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليسرى (95 وحدة عرض)
                    self.cell(95, 6, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة تذييل أيسر: {e}")
                    try:
                        self.set_font('Arial', '', size)
                        self.set_xy(x, y)
                        self.cell(95, 6, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_footer_text_right(self, x, y, text, size=9):
                """إضافة نص في التذييل الأيمن"""
                try:
                    self.set_font('Arial', '', size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليمنى (95 وحدة عرض)
                    self.cell(95, 6, str(text), 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة تذييل أيمن: {e}")
                    try:
                        self.set_font('Arial', '', size)
                        self.set_xy(x, y)
                        self.cell(95, 6, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_arabic_text_right_compact(self, x, y, text, size=12, style=''):
                """إضافة نص عربي في الجهة اليمنى مع تباعد مضغوط"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليمنى (95 وحدة عرض) مع ارتفاع مضغوط
                    self.cell(95, 5, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص العربي الأيمن المضغوط: {e}")
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(95, 5, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_english_text_left_compact(self, x, y, text, size=12, style=''):
                """إضافة نص إنجليزي في الجهة اليسرى مع تباعد مضغوط"""
                try:
                    self.set_font('Arial', style, size)
                    self.set_xy(x, y)
                    # عرض النص في الجهة اليسرى (95 وحدة عرض) مع ارتفاع مضغوط
                    self.cell(95, 5, str(text), 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص الإنجليزي الأيسر المضغوط: {e}")
                    try:
                        self.set_font('Arial', style, size)
                        self.set_xy(x, y)
                        self.cell(95, 5, str(text), 0, 1, 'C')
                        return True
                    except:
                        return False

            def add_footer_single_line(self, y, address_text, email_text, size=9):
                """إضافة تذييل في سطر واحد"""
                try:
                    # العنوان في الجهة اليسرى
                    processed_address = self.process_arabic_text(address_text)
                    self.set_font(self.arabic_font, '', size)
                    self.set_xy(10, y)
                    self.cell(95, 6, processed_address, 0, 0, 'C')

                    # الإيميل في الجهة اليمنى
                    self.set_font('Arial', '', size)
                    self.set_xy(105, y)
                    self.cell(95, 6, str(email_text), 0, 1, 'C')

                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة التذييل المفرد: {e}")
                    return False

            def add_header_footer_line(self, y, address_text, email_text, size=9):
                """إضافة العنوان والإيميل في أعلى الصفحة"""
                try:
                    # العنوان في الجهة اليسرى (بداية من 0.5 سم)
                    processed_address = self.process_arabic_text(address_text)
                    self.set_font(self.arabic_font, '', size)
                    self.set_xy(14, y)  # 0.5 سم = 14 نقطة
                    self.cell(90, 6, processed_address, 0, 0, 'L')

                    # الإيميل في الجهة اليمنى (بداية من 0.5 سم من اليمين)
                    self.set_font('Arial', '', size)
                    self.set_xy(110, y)  # من اليمين مع مسافة 0.5 سم
                    self.cell(90, 6, str(email_text), 0, 1, 'R')

                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة العنوان والإيميل العلوي: {e}")
                    return False

            def add_arabic_text_right_margin(self, margin, y, text, size=12, style=''):
                """إضافة نص عربي في الجهة اليمنى مع هامش محدد"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    # حساب الموضع من اليمين مع الهامش
                    x_pos = 210 - margin - 90  # عرض الصفحة - الهامش - عرض النص
                    self.set_xy(x_pos, y)
                    self.cell(90, 5, processed_text, 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص العربي بهامش: {e}")
                    return False

            def add_english_text_left_margin(self, margin, y, text, size=12, style=''):
                """إضافة نص إنجليزي في الجهة اليسرى مع هامش محدد"""
                try:
                    self.set_font('Arial', style, size)
                    # الموضع من اليسار مع الهامش
                    self.set_xy(margin, y)
                    self.cell(90, 5, str(text), 0, 1, 'C')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص الإنجليزي بهامش: {e}")
                    return False

            def add_arabic_text_right_edge(self, y, text, size=12, style=''):
                """إضافة نص عربي من حافة الصفحة اليمنى"""
                try:
                    processed_text = self.process_arabic_text(text)
                    self.set_font(self.arabic_font, style, size)
                    # حساب الموضع من اليمين (عرض الصفحة - عرض النص)
                    x_pos = 210 - 100  # عرض الصفحة - عرض النص
                    self.set_xy(x_pos, y)
                    self.cell(100, 6, processed_text, 0, 1, 'R')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص العربي من الحافة: {e}")
                    return False

            def add_english_text_left_edge(self, y, text, size=12, style=''):
                """إضافة نص إنجليزي من حافة الصفحة اليسرى"""
                try:
                    self.set_font('Arial', style, size)
                    # الموضع من اليسار (بداية الصفحة)
                    self.set_xy(10, y)
                    self.cell(100, 6, str(text), 0, 1, 'L')
                    return True
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة النص الإنجليزي من الحافة: {e}")
                    return False
        
        # إنشاء PDF تجريبي
        pdf = ArabicPDF()

        # إضافة العناوين في الجهة اليمنى العليا (النصوص العربية)
        # بداية من حافة الصفحة اليمنى
        arabic_titles = [
            ("جمهورية العراق", 14, 'B'),
            ("وزارة الصحة", 12, 'B'),
            ("قسم الصحة العامة", 11, 'B'),
            ("مختبر الصحة العامة", 11, 'B'),
            ("وحدة الفايروسات", 10, 'B')
        ]

        y_pos = 5  # أعلى حافة الورقة
        for title, size, style in arabic_titles:
            # بداية من حافة الصفحة اليمنى
            success = pdf.add_arabic_text_right_edge(y_pos, title, size, style)
            if success:
                print(f"✅ تم إضافة العنوان العربي: {title}")
            else:
                print(f"❌ فشل في إضافة العنوان العربي: {title}")
            y_pos += 8  # بدون مسافة بين السطور

        # إضافة العناوين في الجهة اليسرى العليا (النصوص الإنجليزية)
        english_titles = [
            ("Republic of Iraq", 14, 'B'),
            ("Ministry of Health", 12, 'B'),
            ("Public Health Department", 11, 'B'),
            ("Public Health Laboratory", 11, 'B'),
            ("Virus Unit", 10, 'B')
        ]

        y_pos = 5  # أعلى حافة الورقة
        for title, size, style in english_titles:
            # بداية من حافة الصفحة اليسرى
            success = pdf.add_english_text_left_edge(y_pos, title, size, style)
            if success:
                print(f"✅ تم إضافة العنوان الإنجليزي: {title}")
            else:
                print(f"❌ فشل في إضافة العنوان الإنجليزي: {title}")
            y_pos += 8  # بدون مسافة بين السطور
        
        # إضافة الشعار في منتصف الصفحة بين العناوين
        # محاكاة الشعار بمستطيل
        pdf.set_fill_color(200, 200, 200)
        pdf.rect(85, 15, 40, 40, 'F')
        pdf.set_font('Arial', 'B', 8)
        pdf.set_xy(85, 30)
        pdf.cell(40, 10, "LOGO", 0, 1, 'C')
        print("✅ تم إضافة الشعار (محاكاة)")

        start_y = 65  # بداية المحتوى بعد الشعار والعناوين

        # عنوان التقرير
        pdf.add_arabic_text(0, start_y, "تقرير نتائج الفحوصات المختبرية", 16, 'B')
        start_y += 12
        pdf.set_font('Arial', 'B', 12)
        pdf.set_xy(0, start_y)
        pdf.cell(0, 8, "Laboratory Test Results Report", 0, 1, 'C')
        start_y += 10

        # التاريخ والوقت
        current_date = datetime.datetime.now()
        arabic_date = current_date.strftime("%Y/%m/%d")
        arabic_time = current_date.strftime("%H:%M:%S")

        pdf.add_arabic_text(0, start_y, f"تاريخ إنشاء التقرير: {arabic_date}", 10)
        start_y += 8
        pdf.add_arabic_text(0, start_y, f"وقت الإنشاء: {arabic_time}", 10)
        start_y += 15
        
        # جدول تجريبي واضح
        headers = ["الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحليل", "النتيجة"]
        col_widths = [25, 30, 15, 15, 20, 30, 25, 25]

        # رسم رؤوس الجدول
        pdf.set_xy(10, start_y)  # بداية من حافة الصفحة
        pdf.set_font(pdf.arabic_font, 'B', 9)

        x_pos = 10
        for i, header in enumerate(headers):
            processed_header = pdf.process_arabic_text(header)
            pdf.set_xy(x_pos, start_y)
            pdf.cell(col_widths[i], 8, processed_header, 1, 0, 'C')
            x_pos += col_widths[i]
        print("✅ تم رسم رؤوس الجدول الواضح")

        # بيانات تجريبية
        test_data = [
            ["12345678901", "أحمد محمد علي", "35", "ذكر", "دم", "مستشفى الناصرية", "CBC", "طبيعي"],
            ["12345678902", "فاطمة حسن", "28", "أنثى", "بول", "مركز صحي السوق", "Urine", "طبيعي"],
            ["12345678903", "علي أحمد", "42", "ذكر", "براز", "مستشفى الحبوبي", "Stool", "طبيعي"],
            ["12345678904", "زينب كاظم", "31", "أنثى", "دم", "مستشفى الناصرية", "CBC", "طبيعي"],
            ["12345678905", "محمد جاسم", "45", "ذكر", "بول", "مركز صحي الشطرة", "Urine", "طبيعي"],
            ["12345678906", "سارة أحمد", "29", "أنثى", "دم", "مستشفى الحبوبي", "CBC", "طبيعي"],
            ["12345678907", "حسام علي", "38", "ذكر", "بول", "مركز صحي الناصرية", "Urine", "طبيعي"]
        ]

        # رسم البيانات
        y_pos = start_y + 8
        pdf.set_font(pdf.arabic_font, '', 8)

        for row_data in test_data:
            x_pos = 10
            for i, value in enumerate(row_data):
                if i < len(col_widths):
                    # تقصير النص الطويل للوضوح
                    text = str(value)[:15] + "..." if len(str(value)) > 15 else str(value)
                    processed_text = pdf.process_arabic_text(text)
                    pdf.set_xy(x_pos, y_pos)
                    pdf.cell(col_widths[i], 7, processed_text, 1, 0, 'C')
                    x_pos += col_widths[i]
            y_pos += 7
            if y_pos > 250:  # تجنب تجاوز الصفحة
                break
        print("✅ تم رسم البيانات الواضحة")

        # إحصائيات التقرير
        y_pos += 10
        pdf.add_arabic_text(0, y_pos, f"إجمالي عدد السجلات في التقرير: {len(test_data)}", 11, 'B')

        # التذييل في أسفل الصفحة (العنوان والإيميل)
        footer_y = 275  # أسفل الصفحة
        address_text = "ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة"
        email_text = "<EMAIL>"
        success = pdf.add_footer_single_line(footer_y, address_text, email_text, 9)
        if success:
            print("✅ تم إضافة التذييل في أسفل الصفحة")
        else:
            print("❌ فشل في إضافة التذييل")

        # رقم الصفحة في أسفل الصفحة
        pdf.add_arabic_text(0, footer_y + 8, "صفحة 1", 8)
        print("✅ تم إضافة رقم الصفحة")
        
        # حفظ الملف
        filename = f"اختبار_fpdf_عربي_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        pdf.output(filename)
        
        if os.path.exists(filename):
            print(f"✅ تم إنشاء ملف PDF عربي: {filename}")
            
            # فتح الملف
            try:
                os.startfile(filename)
                print("📂 تم فتح الملف للمراجعة")
            except:
                print("📂 يمكنك فتح الملف يدوياً")
            
            return True
        else:
            print("❌ فشل في إنشاء الملف")
            return False
            
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("💡 لتثبيت المكتبات المطلوبة:")
        print("   py -m pip install fpdf2")
        print("   py -m pip install arabic-reshaper")
        print("   py -m pip install python-bidi")
        return False
    except Exception as e:
        print(f"❌ خطأ في fpdf2: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🇸🇦 اختبار fpdf2 للنصوص العربية")
    print("=" * 60)
    
    # اختبار fpdf2
    fpdf_test = test_fpdf_arabic()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"fpdf2 (النصوص العربية): {'✅ نجح' if fpdf_test else '❌ فشل'}")
    
    if fpdf_test:
        print("\n🎉 fpdf2 يعمل بشكل ممتاز!")
        print("✅ النصوص العربية تظهر بشكل صحيح")
        print("✅ الخطوط العربية تعمل بشكل مثالي")
        print("✅ الجداول والتنسيق يعملان بشكل احترافي")
        print("✅ معالجة النصوص العربية تلقائية")
        print("\n🚀 البرنامج سيستخدم fpdf2 لإنشاء التقارير العربية!")
        print("\n📋 المزايا:")
        print("   🔤 دعم كامل للخطوط العربية")
        print("   📝 معالجة تلقائية للنصوص العربية")
        print("   📊 جداول احترافية")
        print("   🖼️ دعم الصور والشعارات")
        print("   ⚡ سرعة عالية في الإنشاء")
    else:
        print("\n❌ fpdf2 لا يعمل")
        print("💡 تأكد من تثبيت المكتبات:")
        print("   py -m pip install fpdf2")
        print("   py -m pip install arabic-reshaper")
        print("   py -m pip install python-bidi")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
