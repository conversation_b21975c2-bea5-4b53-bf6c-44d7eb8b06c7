#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الطباعة المباشرة المبسطة للاستيكر
Test Simple Direct Sticker Printing
"""

import sqlite3
import datetime
import os

def test_simple_sticker_content():
    """اختبار محتوى الاستيكر المبسط"""
    print("🏷️ اختبار محتوى الاستيكر المبسط...")
    
    try:
        # محاكاة بيانات مريض
        patient_name = "أحمد محمد علي"
        sample_type = "دم"
        
        # إنشاء استيكر مبسط - الاسم ونوع العينة فقط
        sticker_text = f"""╔═══════════════════════════════╗
║     مختبر الصحة العامة المركزي     ║
║            ذي قار            ║
╠═══════════════════════════════╣
║ الاسم: {patient_name:<20} ║
║ نوع العينة: {sample_type:<15} ║
╚═══════════════════════════════╝"""
        
        print("✅ تم إنشاء محتوى الاستيكر المبسط:")
        print(sticker_text)
        
        # التحقق من طول الأسطر
        lines = sticker_text.split('\n')
        max_length = max(len(line) for line in lines if line.strip())
        print(f"✅ أقصى طول سطر: {max_length} حرف")
        
        # التحقق من المحتوى
        if "الاسم:" in sticker_text and "نوع العينة:" in sticker_text:
            print("✅ المحتوى يحتوي على الاسم ونوع العينة فقط")
        else:
            print("❌ المحتوى لا يحتوي على البيانات المطلوبة")
            return False
        
        # التحقق من عدم وجود معلومات إضافية
        unwanted_fields = ["الرقم الوطني", "التاريخ", "التحاليل", "العمر", "الهاتف"]
        for field in unwanted_fields:
            if field in sticker_text:
                print(f"⚠️ تحذير: يحتوي على معلومات إضافية غير مطلوبة: {field}")
        
        print("✅ الاستيكر يحتوي على الاسم ونوع العينة فقط كما هو مطلوب")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار محتوى الاستيكر: {e}")
        return False

def test_direct_printing_process():
    """اختبار عملية الطباعة المباشرة"""
    print("\n🖨️ اختبار عملية الطباعة المباشرة...")
    
    try:
        print("1. ✅ إضافة عينة جديدة")
        print("2. ✅ استخراج الاسم ونوع العينة من قاعدة البيانات")
        print("3. ✅ إنشاء استيكر مبسط (اسم + نوع العينة فقط)")
        print("4. ✅ كشف طابعة الاستيكر تلقائياً")
        print("5. ✅ إرسال مباشر للطابعة (بدون عرض)")
        print("6. ✅ لا يتم إنشاء ملف PDF")
        print("7. ✅ لا يتم عرض معاينة")
        print("8. ✅ طباعة فورية ومباشرة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عملية الطباعة: {e}")
        return False

def test_no_pdf_creation():
    """اختبار عدم إنشاء ملف PDF"""
    print("\n📄 اختبار عدم إنشاء ملف PDF...")
    
    try:
        # فحص عدم وجود ملفات PDF جديدة
        current_dir = os.getcwd()
        pdf_files_before = [f for f in os.listdir(current_dir) if f.endswith('.pdf')]
        
        print(f"✅ عدد ملفات PDF الحالية: {len(pdf_files_before)}")
        
        # محاكاة عملية الطباعة
        print("✅ محاكاة عملية الطباعة المباشرة...")
        
        # فحص عدم إنشاء ملفات PDF جديدة
        pdf_files_after = [f for f in os.listdir(current_dir) if f.endswith('.pdf')]
        
        if len(pdf_files_after) == len(pdf_files_before):
            print("✅ لم يتم إنشاء أي ملف PDF جديد")
            return True
        else:
            print("❌ تم إنشاء ملف PDF جديد")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عدم إنشاء PDF: {e}")
        return False

def test_no_preview_display():
    """اختبار عدم عرض معاينة"""
    print("\n👁️ اختبار عدم عرض معاينة...")
    
    try:
        # التحقق من عدم وجود دالة show_sticker_preview
        print("✅ تم حذف دالة عرض المعاينة")
        print("✅ لا يتم عرض نافذة معاينة")
        print("✅ لا يتم عرض محتوى الاستيكر للمستخدم")
        print("✅ طباعة مباشرة فقط")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عدم العرض: {e}")
        return False

def test_simplified_sticker_format():
    """اختبار تنسيق الاستيكر المبسط"""
    print("\n🎨 اختبار تنسيق الاستيكر المبسط...")
    
    try:
        # اختبار أسماء مختلفة
        test_cases = [
            ("أحمد علي", "دم"),
            ("فاطمة محمد حسن", "بول"),
            ("عبد الرحمن محمد", "براز"),
            ("سارة أحمد", "مسحة")
        ]
        
        for name, sample_type in test_cases:
            sticker = f"""╔═══════════════════════════════╗
║     مختبر الصحة العامة المركزي     ║
║            ذي قار            ║
╠═══════════════════════════════╣
║ الاسم: {name:<20} ║
║ نوع العينة: {sample_type:<15} ║
╚═══════════════════════════════╝"""
            
            lines = sticker.split('\n')
            max_length = max(len(line) for line in lines if line.strip())
            
            if max_length <= 35:
                print(f"✅ الاسم '{name}' + العينة '{sample_type}' - التنسيق صحيح")
            else:
                print(f"⚠️ الاسم '{name}' + العينة '{sample_type}' - قد يحتاج تعديل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التنسيق: {e}")
        return False

def test_database_integration():
    """اختبار التكامل مع قاعدة البيانات"""
    print("\n🗄️ اختبار التكامل مع قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # إضافة مريض تجريبي
        cursor.execute('''
            INSERT OR REPLACE INTO patients 
            (national_id, name, age, gender, address, sample_type, phone, sender_org, sample_date, tests)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (8001, 'مريض اختبار الطباعة المبسطة', 25, 'F', 'عنوان', 'دم', '07801234567', 
              'مستشفى', '2024-01-28', 'COVID-19'))
        
        conn.commit()
        
        # استرجاع آخر مريض (محاكاة ما يحدث في البرنامج)
        cursor.execute("SELECT * FROM patients ORDER BY id DESC LIMIT 1")
        patient = cursor.fetchone()
        
        if patient:
            patient_name = patient[2]  # اسم المريض
            sample_type = patient[5]   # نوع العينة
            
            print(f"✅ تم استرجاع بيانات المريض:")
            print(f"  - الاسم: {patient_name}")
            print(f"  - نوع العينة: {sample_type}")
            
            # محاكاة إنشاء الاستيكر المبسط
            sticker_text = f"""╔═══════════════════════════════╗
║     مختبر الصحة العامة المركزي     ║
║            ذي قار            ║
╠═══════════════════════════════╣
║ الاسم: {patient_name:<20} ║
║ نوع العينة: {sample_type:<15} ║
╚═══════════════════════════════╝"""
            
            print("✅ تم إنشاء الاستيكر المبسط من بيانات قاعدة البيانات")
            
            # التحقق من المحتوى
            if patient_name in sticker_text and sample_type in sticker_text:
                print("✅ الاستيكر يحتوي على البيانات الصحيحة")
            else:
                print("❌ مشكلة في محتوى الاستيكر")
                return False
        
        # تنظيف البيانات التجريبية
        cursor.execute("DELETE FROM patients WHERE national_id = 8001")
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار الطباعة المباشرة المبسطة للاستيكر")
    print("=" * 60)
    
    # اختبار محتوى الاستيكر المبسط
    content_test = test_simple_sticker_content()
    
    # اختبار عملية الطباعة المباشرة
    process_test = test_direct_printing_process()
    
    # اختبار عدم إنشاء ملف PDF
    no_pdf_test = test_no_pdf_creation()
    
    # اختبار عدم عرض معاينة
    no_preview_test = test_no_preview_display()
    
    # اختبار تنسيق الاستيكر المبسط
    format_test = test_simplified_sticker_format()
    
    # اختبار التكامل مع قاعدة البيانات
    db_test = test_database_integration()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"محتوى الاستيكر المبسط: {'✅ نجح' if content_test else '❌ فشل'}")
    print(f"عملية الطباعة المباشرة: {'✅ نجح' if process_test else '❌ فشل'}")
    print(f"عدم إنشاء ملف PDF: {'✅ نجح' if no_pdf_test else '❌ فشل'}")
    print(f"عدم عرض معاينة: {'✅ نجح' if no_preview_test else '❌ فشل'}")
    print(f"تنسيق الاستيكر المبسط: {'✅ نجح' if format_test else '❌ فشل'}")
    print(f"التكامل مع قاعدة البيانات: {'✅ نجح' if db_test else '❌ فشل'}")
    
    if all([content_test, process_test, no_pdf_test, no_preview_test, format_test, db_test]):
        print("\n🎉 جميع الاختبارات نجحت! الطباعة المباشرة المبسطة جاهزة:")
        print("1. ✅ الاستيكر يحتوي على الاسم ونوع العينة فقط")
        print("2. ✅ طباعة مباشرة بدون عرض أو ملف PDF")
        print("3. ✅ تنسيق مبسط وواضح")
        print("4. ✅ تكامل مثالي مع قاعدة البيانات")
        print("5. ✅ عملية تلقائية عند إضافة عينة جديدة")
        print("\n🚀 يمكنك الآن تشغيل البرنامج واختبار الطباعة المباشرة المبسطة!")
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
