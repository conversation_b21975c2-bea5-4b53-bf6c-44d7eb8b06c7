#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تثبيت مكتبات دعم النصوص العربية
Install Arabic Text Support Libraries
"""

import subprocess
import sys
import os

def install_package(package_name):
    """تثبيت مكتبة Python"""
    try:
        print(f"📦 تثبيت {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ تم تثبيت {package_name} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل تثبيت {package_name}: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت {package_name}: {e}")
        return False

def check_package(package_name):
    """فحص وجود مكتبة"""
    try:
        __import__(package_name)
        print(f"✅ {package_name}: متوفر")
        return True
    except ImportError:
        print(f"❌ {package_name}: غير متوفر")
        return False

def main():
    """الدالة الرئيسية لتثبيت دعم النصوص العربية"""
    print("🇸🇦 تثبيت مكتبات دعم النصوص العربية")
    print("=" * 50)
    
    # قائمة المكتبات المطلوبة
    required_packages = [
        ("arabic_reshaper", "arabic-reshaper"),
        ("bidi", "python-bidi")
    ]
    
    print("1️⃣ فحص المكتبات الحالية...")
    missing_packages = []
    
    for module_name, package_name in required_packages:
        if not check_package(module_name):
            missing_packages.append((module_name, package_name))
    
    if not missing_packages:
        print("\n🎉 جميع مكتبات دعم النصوص العربية متوفرة!")
        return True
    
    print(f"\n2️⃣ تثبيت {len(missing_packages)} مكتبة مفقودة...")
    
    success_count = 0
    for module_name, package_name in missing_packages:
        if install_package(package_name):
            success_count += 1
    
    print(f"\n3️⃣ التحقق من التثبيت...")
    
    all_installed = True
    for module_name, package_name in required_packages:
        if check_package(module_name):
            success_count += 1
        else:
            all_installed = False
    
    print("\n" + "=" * 50)
    if all_installed:
        print("🎉 تم تثبيت جميع مكتبات دعم النصوص العربية بنجاح!")
        print("✅ المكتبات المتوفرة:")
        print("   📦 arabic-reshaper: لإعادة تشكيل النصوص العربية")
        print("   📦 python-bidi: لدعم النصوص ثنائية الاتجاه")
        print("\n🚀 يمكنك الآن طباعة التقارير العربية بشكل صحيح!")
        
        # اختبار سريع
        print("\n4️⃣ اختبار سريع للمكتبات...")
        try:
            import arabic_reshaper
            from bidi.algorithm import get_display
            
            test_text = "مختبر الصحة العامة"
            reshaped = arabic_reshaper.reshape(test_text)
            bidi_text = get_display(reshaped)
            
            print(f"   📝 النص الأصلي: {test_text}")
            print(f"   📝 النص المعالج: {bidi_text}")
            print("   ✅ المكتبات تعمل بشكل صحيح!")
            
        except Exception as e:
            print(f"   ⚠️ خطأ في الاختبار: {e}")
        
        return True
    else:
        print("❌ فشل في تثبيت بعض المكتبات")
        print("💡 حاول تشغيل الأوامر التالية يدوياً:")
        for module_name, package_name in missing_packages:
            print(f"   pip install {package_name}")
        return False

def create_test_arabic_pdf():
    """إنشاء ملف PDF تجريبي للنصوص العربية"""
    print("\n5️⃣ إنشاء ملف PDF تجريبي للنصوص العربية...")
    
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import arabic_reshaper
        from bidi.algorithm import get_display
        import datetime
        
        # إنشاء ملف PDF
        filename = f"اختبار_النصوص_العربية_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        # محاولة تحميل خط عربي
        try:
            # خطوط Windows
            arabic_fonts = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/tahoma.ttf"
            ]
            
            font_loaded = False
            for font_path in arabic_fonts:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('ArabicFont', font_path))
                        c.setFont('ArabicFont', 16)
                        font_loaded = True
                        print(f"   ✅ تم تحميل الخط: {font_path}")
                        break
                    except:
                        continue
            
            if not font_loaded:
                c.setFont('Helvetica', 16)
                print("   ⚠️ استخدام Helvetica كبديل")
        
        except:
            c.setFont('Helvetica', 16)
        
        # نصوص تجريبية عربية
        arabic_texts = [
            "مختبر الصحة العامة المركزي",
            "تقرير نتائج الفحوصات المختبرية", 
            "جمهورية العراق - وزارة الصحة",
            "محافظة ذي قار - الناصرية"
        ]
        
        y_pos = height - 100
        
        for text in arabic_texts:
            # معالجة النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            
            # حساب الموضع في المنتصف
            text_width = c.stringWidth(bidi_text, 'ArabicFont' if font_loaded else 'Helvetica', 16)
            x_pos = (width - text_width) / 2
            
            # رسم النص
            c.drawString(x_pos, y_pos, bidi_text)
            y_pos -= 30
        
        # إضافة معلومات الاختبار
        c.setFont('Helvetica', 10)
        c.drawString(50, 50, f"Test file created: {datetime.datetime.now()}")
        c.drawString(50, 35, "Arabic text support test - مختبر الصحة العامة")
        
        c.save()
        
        if os.path.exists(filename):
            print(f"   ✅ تم إنشاء ملف PDF تجريبي: {filename}")
            
            # فتح الملف
            try:
                os.startfile(filename)
                print("   📂 تم فتح الملف للمراجعة")
            except:
                print("   📂 يمكنك فتح الملف يدوياً للمراجعة")
            
            return True
        else:
            print("   ❌ فشل في إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء ملف PDF تجريبي: {e}")
        return False

if __name__ == "__main__":
    print("🇸🇦 برنامج تثبيت دعم النصوص العربية")
    print("Arabic Text Support Installer")
    print("=" * 50)
    
    success = main()
    
    if success:
        # إنشاء ملف تجريبي
        create_test_arabic_pdf()
        
        print("\n" + "=" * 50)
        print("🎉 تم الانتهاء من تثبيت دعم النصوص العربية!")
        print("📋 الخطوات التالية:")
        print("1. ✅ تشغيل البرنامج الرئيسي: python main.py")
        print("2. ✅ الذهاب لتبويب 'التقارير والإحصائيات'")
        print("3. ✅ الضغط على 'طباعة التقرير'")
        print("4. ✅ مراجعة التقرير العربي الجديد")
        print("\n🚀 النصوص العربية ستظهر الآن بشكل صحيح!")
    else:
        print("\n❌ فشل في تثبيت بعض المكتبات")
        print("يرجى المحاولة مرة أخرى أو التثبيت اليدوي")
    
    input("\nاضغط Enter للخروج...")
