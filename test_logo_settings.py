#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إعدادات الشعار في التقرير
Test Logo Settings in Report
"""

import sqlite3
import datetime
import os

def test_logo_settings_database():
    """اختبار حفظ وتحميل إعدادات الشعار في قاعدة البيانات"""
    print("🗄️ اختبار إعدادات الشعار في قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # اختبار حفظ إعداد إظهار الشعار
        test_cases = [
            ('show_logo', 'True'),
            ('show_logo', 'False'),
            ('logo_path', 'ministry_logo.png'),
            ('logo_path', 'custom_logo.png')
        ]
        
        for setting_name, setting_value in test_cases:
            cursor.execute('''
                INSERT OR REPLACE INTO settings (category, name, value)
                VALUES (?, ?, ?)
            ''', ('report_settings', setting_name, setting_value))
            
            print(f"✅ تم حفظ إعداد {setting_name}: {setting_value}")
        
        conn.commit()
        
        # اختبار تحميل الإعدادات
        for setting_name, expected_value in test_cases:
            cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = ?", (setting_name,))
            result = cursor.fetchone()
            
            if result and result[0] == expected_value:
                print(f"✅ تم تحميل إعداد {setting_name}: {result[0]}")
            else:
                print(f"❌ خطأ في تحميل إعداد {setting_name}")
                return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_logo_display_logic():
    """اختبار منطق عرض الشعار"""
    print("\n🖼️ اختبار منطق عرض الشعار...")
    
    try:
        # اختبار حالات مختلفة لإعداد إظهار الشعار
        test_cases = [
            ('True', True, "يجب إظهار الشعار"),
            ('true', True, "يجب إظهار الشعار (حالة صغيرة)"),
            ('FALSE', False, "يجب إخفاء الشعار"),
            ('false', False, "يجب إخفاء الشعار (حالة صغيرة)"),
            ('', True, "القيمة الافتراضية - إظهار الشعار"),
            (None, True, "لا توجد قيمة - إظهار الشعار")
        ]
        
        for setting_value, expected_result, description in test_cases:
            if setting_value is None:
                show_logo = True  # القيمة الافتراضية
            elif setting_value == '':
                show_logo = True  # القيمة الافتراضية
            else:
                show_logo = setting_value.lower() == 'true'
            
            if show_logo == expected_result:
                print(f"✅ {description}: {show_logo}")
            else:
                print(f"❌ {description}: توقع {expected_result}, حصل على {show_logo}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار منطق العرض: {e}")
        return False

def test_logo_positioning():
    """اختبار موضع الشعار في أعلى منتصف الورقة"""
    print("\n📐 اختبار موضع الشعار في أعلى منتصف الورقة...")
    
    try:
        from reportlab.lib.pagesizes import A4
        width, height = A4
        
        # حساب الموضع في أعلى منتصف الورقة
        logo_width = 80
        logo_height = 80
        
        page_center_x = width / 2  # منتصف الصفحة
        logo_x = page_center_x - (logo_width / 2)  # منتصف الشعار في منتصف الصفحة
        logo_y = height - 100  # أعلى الورقة مع مسافة قليلة من الحافة
        
        print(f"✅ أبعاد الصفحة: {width:.1f} x {height:.1f}")
        print(f"✅ منتصف الصفحة: X={page_center_x:.1f}")
        print(f"✅ موضع الشعار في أعلى المنتصف: X={logo_x:.1f}, Y={logo_y:.1f}")
        print(f"✅ أبعاد الشعار: {logo_width} x {logo_height}")
        
        # التحقق من أن الشعار في المنتصف
        logo_center_x = logo_x + (logo_width / 2)
        
        if abs(page_center_x - logo_center_x) < 0.1:  # تسامح 0.1 نقطة
            print("✅ الشعار موضوع في منتصف الورقة بشكل صحيح")
        else:
            print(f"❌ الشعار ليس في المنتصف: مركز الصفحة {page_center_x:.1f}, مركز الشعار {logo_center_x:.1f}")
            return False
        
        # التحقق من أن الشعار في أعلى الورقة
        if logo_y > height - 150:  # يجب أن يكون في أعلى 150 نقطة من الورقة
            print("✅ الشعار موضوع في أعلى الورقة بشكل صحيح")
        else:
            print(f"❌ الشعار ليس في أعلى الورقة: Y={logo_y:.1f}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار موضع الشعار: {e}")
        return False

def test_report_header_positioning():
    """اختبار موضع عنوان التقرير بعد الشعار"""
    print("\n📄 اختبار موضع عنوان التقرير...")
    
    try:
        from reportlab.lib.pagesizes import A4
        width, height = A4
        
        # موضع الشعار
        logo_y = height - 100
        
        # موضع بداية النص بعد الشعار
        header_start_y_with_logo = height - 120
        header_start_y_without_logo = height - 50
        
        print(f"✅ موضع الشعار: Y={logo_y:.1f}")
        print(f"✅ موضع النص مع الشعار: Y={header_start_y_with_logo:.1f}")
        print(f"✅ موضع النص بدون شعار: Y={header_start_y_without_logo:.1f}")
        
        # التحقق من المسافة المناسبة
        space_with_logo = logo_y - header_start_y_with_logo
        if space_with_logo >= 20:  # مسافة كافية بين الشعار والنص
            print(f"✅ مسافة مناسبة بين الشعار والنص: {space_with_logo:.1f} نقطة")
        else:
            print(f"❌ مسافة غير كافية بين الشعار والنص: {space_with_logo:.1f} نقطة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار موضع العنوان: {e}")
        return False

def test_settings_ui_components():
    """اختبار مكونات واجهة الإعدادات"""
    print("\n🎛️ اختبار مكونات واجهة الإعدادات...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # محاكاة إعدادات التقرير
        settings_window = tk.Toplevel(root)
        settings_window.title("اختبار إعدادات التقرير")
        settings_window.geometry("600x400")
        settings_window.configure(bg='white')
        settings_window.withdraw()  # إخفاء النافذة
        
        # إطار إعدادات الشعار
        logo_frame = tk.LabelFrame(settings_window, text="شعار التقرير",
                                  font=('Arial', 12, 'bold'), bg='white')
        logo_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # خيار إظهار/إخفاء الشعار
        show_logo = tk.BooleanVar(value=True)
        logo_checkbox = tk.Checkbutton(logo_frame, text="إظهار الشعار في التقارير", 
                                      variable=show_logo, font=('Arial', 10, 'bold'),
                                      bg='white', fg='#2c3e50')
        logo_checkbox.pack(anchor='w', padx=10, pady=5)
        
        # أزرار إدارة الشعار
        buttons_frame = tk.Frame(logo_frame, bg='white')
        buttons_frame.pack(pady=10)
        
        select_button = tk.Button(buttons_frame, text="📁 اختيار شعار جديد", 
                                 font=('Arial', 10, 'bold'), bg='#3498db', fg='white')
        select_button.pack(side=tk.LEFT, padx=5)
        
        preview_button = tk.Button(buttons_frame, text="👁️ معاينة الشعار", 
                                  font=('Arial', 10, 'bold'), bg='#9b59b6', fg='white')
        preview_button.pack(side=tk.LEFT, padx=5)
        
        default_button = tk.Button(buttons_frame, text="🔄 استخدام الشعار الافتراضي", 
                                  font=('Arial', 10, 'bold'), bg='#f39c12', fg='white')
        default_button.pack(side=tk.LEFT, padx=5)
        
        # زر حفظ الإعدادات
        save_button = tk.Button(settings_window, text="💾 حفظ جميع الإعدادات", 
                               font=('Arial', 10, 'bold'), bg='#27ae60', fg='white')
        save_button.pack(pady=10)
        
        # التأكد من تحديث النافذة
        settings_window.update_idletasks()
        
        # اختبار وجود المكونات
        components = [logo_frame, logo_checkbox, buttons_frame, select_button, 
                     preview_button, default_button, save_button]
        
        for i, component in enumerate(components):
            if component.winfo_exists():
                print(f"✅ المكون {i+1}: موجود ويعمل")
            else:
                print(f"❌ المكون {i+1}: مفقود أو لا يعمل")
                return False
        
        # اختبار قيمة الخيار
        if show_logo.get() == True:
            print("✅ خيار إظهار الشعار: مفعل افتراضياً")
        else:
            print("❌ خيار إظهار الشعار: غير مفعل")
            return False
        
        # اختبار تغيير القيمة
        show_logo.set(False)
        if show_logo.get() == False:
            print("✅ تغيير خيار إظهار الشعار: يعمل بشكل صحيح")
        else:
            print("❌ تغيير خيار إظهار الشعار: لا يعمل")
            return False
        
        # إغلاق النوافذ التجريبية
        settings_window.destroy()
        root.destroy()
        
        print("✅ جميع مكونات واجهة الإعدادات تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مكونات الواجهة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار إعدادات الشعار في التقرير")
    print("=" * 60)
    
    # اختبار قاعدة البيانات
    db_test = test_logo_settings_database()
    
    # اختبار منطق عرض الشعار
    logic_test = test_logo_display_logic()
    
    # اختبار موضع الشعار
    position_test = test_logo_positioning()
    
    # اختبار موضع عنوان التقرير
    header_test = test_report_header_positioning()
    
    # اختبار مكونات واجهة الإعدادات
    ui_test = test_settings_ui_components()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"إعدادات قاعدة البيانات: {'✅ نجح' if db_test else '❌ فشل'}")
    print(f"منطق عرض الشعار: {'✅ نجح' if logic_test else '❌ فشل'}")
    print(f"موضع الشعار: {'✅ نجح' if position_test else '❌ فشل'}")
    print(f"موضع عنوان التقرير: {'✅ نجح' if header_test else '❌ فشل'}")
    print(f"مكونات واجهة الإعدادات: {'✅ نجح' if ui_test else '❌ فشل'}")
    
    if all([db_test, logic_test, position_test, header_test, ui_test]):
        print("\n🎉 جميع الاختبارات نجحت! إعدادات الشعار جاهزة:")
        print("1. ✅ خيار إظهار/إخفاء الشعار في إعدادات التقرير")
        print("2. ✅ حفظ وتحميل الإعدادات من قاعدة البيانات")
        print("3. ✅ موضع الشعار في أعلى منتصف الورقة")
        print("4. ✅ تحكم كامل في عرض الشعار")
        print("5. ✅ واجهة إعدادات سهلة الاستخدام")
        print("\n🚀 يمكنك الآن تشغيل البرنامج واختبار إعدادات الشعار!")
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
