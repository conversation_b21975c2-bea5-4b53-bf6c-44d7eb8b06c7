#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء اختصار على سطح المكتب لبرنامج مختبر الصحة العامة المركزي - ذي قار
Create Desktop Shortcut for Central Public Health Laboratory - Dhi Qar
"""

import os
import sys
import winshell
from win32com.client import Dispatch
import tkinter as tk
from tkinter import messagebox, filedialog

class ShortcutCreator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("إنشاء اختصار سطح المكتب")
        self.root.geometry("600x500")
        self.root.configure(bg='#ecf0f1')
        self.root.resizable(False, False)
        
        # جعل النافذة في المنتصف
        self.center_window()
        
        self.program_path = os.path.abspath(os.path.dirname(__file__))
        self.create_interface()
    
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_interface(self):
        """إنشاء واجهة إنشاء الاختصار"""
        # العنوان
        title_frame = tk.Frame(self.root, bg='#3498db', height=80)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        tk.Label(title_frame, text="إنشاء اختصار سطح المكتب", 
                font=('Arial', 18, 'bold'), fg='white', bg='#3498db').pack(pady=20)
        
        # معلومات البرنامج
        info_frame = tk.Frame(self.root, bg='#ecf0f1')
        info_frame.pack(pady=20, padx=20, fill='x')
        
        tk.Label(info_frame, text="معلومات البرنامج:", 
                font=('Arial', 14, 'bold'), bg='#ecf0f1').pack(anchor='w')
        
        tk.Label(info_frame, text=f"📁 مسار البرنامج: {self.program_path}", 
                font=('Arial', 10), bg='#ecf0f1', wraplength=550).pack(anchor='w', pady=2)
        
        tk.Label(info_frame, text=f"🐍 Python: {sys.executable}", 
                font=('Arial', 10), bg='#ecf0f1', wraplength=550).pack(anchor='w', pady=2)
        
        # خيارات الاختصار
        options_frame = tk.LabelFrame(self.root, text="خيارات الاختصار", 
                                     font=('Arial', 12, 'bold'), bg='#ecf0f1')
        options_frame.pack(pady=20, padx=20, fill='x')
        
        # اسم الاختصار
        tk.Label(options_frame, text="اسم الاختصار:", 
                font=('Arial', 11, 'bold'), bg='#ecf0f1').pack(anchor='w', pady=(10, 5))
        
        self.shortcut_name = tk.StringVar(value="مختبر الصحة العامة المركزي - ذي قار")
        name_entry = tk.Entry(options_frame, textvariable=self.shortcut_name, 
                             font=('Arial', 11), width=50)
        name_entry.pack(anchor='w', padx=10)
        
        # وصف الاختصار
        tk.Label(options_frame, text="وصف الاختصار:", 
                font=('Arial', 11, 'bold'), bg='#ecf0f1').pack(anchor='w', pady=(10, 5))
        
        self.shortcut_desc = tk.StringVar(value="برنامج إدارة مختبر الصحة العامة المركزي - محافظة ذي قار")
        desc_entry = tk.Entry(options_frame, textvariable=self.shortcut_desc, 
                             font=('Arial', 11), width=50)
        desc_entry.pack(anchor='w', padx=10)
        
        # نوع الملف للتشغيل
        tk.Label(options_frame, text="طريقة التشغيل:", 
                font=('Arial', 11, 'bold'), bg='#ecf0f1').pack(anchor='w', pady=(10, 5))
        
        self.launch_type = tk.StringVar(value="launcher")
        
        launch_frame = tk.Frame(options_frame, bg='#ecf0f1')
        launch_frame.pack(anchor='w', padx=10)
        
        tk.Radiobutton(launch_frame, text="مشغل البرنامج (launcher.py) - مستحسن", 
                      variable=self.launch_type, value="launcher", 
                      font=('Arial', 10), bg='#ecf0f1').pack(anchor='w')
        
        tk.Radiobutton(launch_frame, text="البرنامج مباشرة (main.py)", 
                      variable=self.launch_type, value="main", 
                      font=('Arial', 10), bg='#ecf0f1').pack(anchor='w')
        
        tk.Radiobutton(launch_frame, text="ملف Batch (تشغيل_مختبر_ذي_قار.bat)", 
                      variable=self.launch_type, value="batch", 
                      font=('Arial', 10), bg='#ecf0f1').pack(anchor='w', pady=(0, 10))
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self.root, bg='#ecf0f1')
        buttons_frame.pack(pady=20)
        
        # زر إنشاء الاختصار
        create_btn = tk.Button(buttons_frame, text="🔗 إنشاء اختصار سطح المكتب", 
                              font=('Arial', 12, 'bold'), bg='#27ae60', fg='white',
                              command=self.create_desktop_shortcut, width=25, height=2)
        create_btn.pack(pady=5)
        
        # زر إنشاء اختصار في قائمة ابدأ
        start_menu_btn = tk.Button(buttons_frame, text="📋 إنشاء اختصار في قائمة ابدأ", 
                                  font=('Arial', 12), bg='#3498db', fg='white',
                                  command=self.create_start_menu_shortcut, width=25)
        start_menu_btn.pack(pady=5)
        
        # زر اختبار الاختصار
        test_btn = tk.Button(buttons_frame, text="🧪 اختبار التشغيل", 
                            font=('Arial', 12), bg='#f39c12', fg='white',
                            command=self.test_launch, width=25)
        test_btn.pack(pady=5)
        
        # زر الخروج
        exit_btn = tk.Button(buttons_frame, text="❌ خروج", 
                            font=('Arial', 12), bg='#e74c3c', fg='white',
                            command=self.root.quit, width=25)
        exit_btn.pack(pady=5)
    
    def get_target_file(self):
        """الحصول على الملف المستهدف للتشغيل"""
        launch_type = self.launch_type.get()
        
        if launch_type == "launcher":
            target_file = os.path.join(self.program_path, "launcher.py")
            if not os.path.exists(target_file):
                messagebox.showerror("خطأ", "ملف launcher.py غير موجود!")
                return None
            return target_file
            
        elif launch_type == "main":
            target_file = os.path.join(self.program_path, "main.py")
            if not os.path.exists(target_file):
                messagebox.showerror("خطأ", "ملف main.py غير موجود!")
                return None
            return target_file
            
        elif launch_type == "batch":
            target_file = os.path.join(self.program_path, "تشغيل_مختبر_ذي_قار.bat")
            if not os.path.exists(target_file):
                messagebox.showerror("خطأ", "ملف تشغيل_مختبر_ذي_قار.bat غير موجود!")
                return None
            return target_file
        
        return None
    
    def create_desktop_shortcut(self):
        """إنشاء اختصار على سطح المكتب"""
        try:
            target_file = self.get_target_file()
            if not target_file:
                return
            
            # مسار سطح المكتب
            desktop = winshell.desktop()
            shortcut_path = os.path.join(desktop, f"{self.shortcut_name.get()}.lnk")
            
            # إنشاء الاختصار
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(shortcut_path)
            
            if self.launch_type.get() in ["launcher", "main"]:
                shortcut.Targetpath = sys.executable
                shortcut.Arguments = f'"{target_file}"'
            else:
                shortcut.Targetpath = target_file
            
            shortcut.WorkingDirectory = self.program_path
            shortcut.Description = self.shortcut_desc.get()
            
            # محاولة تعيين أيقونة
            try:
                # البحث عن أيقونة Python
                python_icon = os.path.join(os.path.dirname(sys.executable), "python.exe")
                if os.path.exists(python_icon):
                    shortcut.IconLocation = python_icon
            except:
                pass
            
            shortcut.save()
            
            messagebox.showinfo("نجح", f"تم إنشاء الاختصار بنجاح على سطح المكتب!\n\nمسار الاختصار:\n{shortcut_path}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء الاختصار:\n{str(e)}")
    
    def create_start_menu_shortcut(self):
        """إنشاء اختصار في قائمة ابدأ"""
        try:
            target_file = self.get_target_file()
            if not target_file:
                return
            
            # مسار قائمة ابدأ
            start_menu = winshell.start_menu()
            shortcut_path = os.path.join(start_menu, f"{self.shortcut_name.get()}.lnk")
            
            # إنشاء الاختصار
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(shortcut_path)
            
            if self.launch_type.get() in ["launcher", "main"]:
                shortcut.Targetpath = sys.executable
                shortcut.Arguments = f'"{target_file}"'
            else:
                shortcut.Targetpath = target_file
            
            shortcut.WorkingDirectory = self.program_path
            shortcut.Description = self.shortcut_desc.get()
            
            # محاولة تعيين أيقونة
            try:
                python_icon = os.path.join(os.path.dirname(sys.executable), "python.exe")
                if os.path.exists(python_icon):
                    shortcut.IconLocation = python_icon
            except:
                pass
            
            shortcut.save()
            
            messagebox.showinfo("نجح", f"تم إنشاء الاختصار بنجاح في قائمة ابدأ!\n\nمسار الاختصار:\n{shortcut_path}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء الاختصار:\n{str(e)}")
    
    def test_launch(self):
        """اختبار تشغيل البرنامج"""
        try:
            target_file = self.get_target_file()
            if not target_file:
                return
            
            import subprocess
            
            if self.launch_type.get() in ["launcher", "main"]:
                subprocess.Popen([sys.executable, target_file])
            else:
                subprocess.Popen([target_file], shell=True)
            
            messagebox.showinfo("اختبار", "تم تشغيل البرنامج للاختبار!")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل البرنامج:\n{str(e)}")
    
    def run(self):
        """تشغيل منشئ الاختصارات"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من توفر المكتبات المطلوبة
        import winshell
        from win32com.client import Dispatch
        
        creator = ShortcutCreator()
        creator.run()
        
    except ImportError as e:
        # في حالة عدم توفر المكتبات
        root = tk.Tk()
        root.withdraw()
        
        error_msg = f"مكتبة مطلوبة غير متوفرة: {e}\n\nلإنشاء الاختصارات، يرجى تثبيت المكتبات التالية:\n\npip install pywin32 winshell"
        messagebox.showerror("خطأ", error_msg)
        
        root.destroy()

if __name__ == "__main__":
    main()
