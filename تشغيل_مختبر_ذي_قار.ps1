# مختبر الصحة العامة المركزي - ذي قار
# Central Public Health Laboratory - Dhi Qar

# تعيين ترميز UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# تعيين عنوان النافذة
$Host.UI.RawUI.WindowTitle = "مختبر الصحة العامة المركزي - ذي قار"

Write-Host ""
Write-Host "========================================================" -ForegroundColor Cyan
Write-Host "   مختبر الصحة العامة المركزي - ذي قار" -ForegroundColor Yellow
Write-Host "   Central Public Health Laboratory - Dhi Qar" -ForegroundColor Green
Write-Host "========================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 جاري تشغيل البرنامج..." -ForegroundColor White
Write-Host ""

# تغيير المجلد إلى مجلد البرنامج
Set-Location -Path $PSScriptRoot

# التحقق من وجود Python
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم العثور على Python: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ خطأ: Python غير مثبت على النظام" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 يرجى تثبيت Python من الرابط التالي:" -ForegroundColor Yellow
    Write-Host "https://www.python.org/downloads/" -ForegroundColor Blue
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# التحقق من وجود الملف الرئيسي
if (-not (Test-Path "main.py")) {
    Write-Host "❌ خطأ: ملف main.py غير موجود" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 تأكد من وجود ملفات البرنامج في نفس المجلد" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✅ تم العثور على ملفات البرنامج" -ForegroundColor Green
Write-Host ""
Write-Host "🔄 جاري تشغيل مختبر الصحة العامة المركزي..." -ForegroundColor Cyan
Write-Host ""

# تشغيل البرنامج
try {
    python main.py
} catch {
    Write-Host "❌ خطأ في تشغيل البرنامج: $_" -ForegroundColor Red
}

# في حالة إغلاق البرنامج
Write-Host ""
Write-Host "📝 تم إغلاق البرنامج" -ForegroundColor Yellow
Write-Host ""
Read-Host "اضغط Enter للخروج"
