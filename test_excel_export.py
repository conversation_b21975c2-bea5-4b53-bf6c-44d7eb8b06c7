#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تصدير Excel
Test Excel Export
"""

import sys
import os
import datetime

def test_excel_export():
    """اختبار تصدير Excel"""
    print("🧪 اختبار تصدير Excel...")
    
    # 1. فحص المكتبات المطلوبة
    print("\n1️⃣ فحص المكتبات المطلوبة...")
    try:
        import pandas as pd
        print("   ✅ pandas: متوفر")
        
        import openpyxl
        print("   ✅ openpyxl: متوفر")
        
        from tkinter import filedialog
        print("   ✅ filedialog: متوفر")
        
    except ImportError as e:
        print(f"   ❌ مكتبة مفقودة: {e}")
        print("\n💡 لحل المشكلة:")
        print("pip install pandas openpyxl")
        return False
    
    # 2. إنشاء بيانات تجريبية
    print("\n2️⃣ إنشاء بيانات تجريبية...")
    try:
        test_data = [
            ["12345678901", "أحمد محمد علي", "35", "ذكر", "دم", "مستشفى الناصرية", "CBC", "طبيعي", "2025-01-12"],
            ["12345678902", "فاطمة حسن", "28", "أنثى", "بول", "مركز صحي السوق", "Urine", "طبيعي", "2025-01-12"],
            ["12345678903", "علي أحمد", "42", "ذكر", "براز", "مستشفى الحبوبي", "Stool", "طبيعي", "2025-01-12"],
            ["12345678904", "زينب كاظم", "31", "أنثى", "دم", "مستشفى الناصرية", "CBC", "طبيعي", "2025-01-12"],
            ["12345678905", "محمد جاسم", "45", "ذكر", "بول", "مركز صحي الشطرة", "Urine", "طبيعي", "2025-01-12"]
        ]
        
        columns = ['الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة',
                  'جهة الإرسال', 'التحليل', 'النتيجة', 'تاريخ النتيجة']
        
        print(f"   ✅ تم إنشاء {len(test_data)} صف من البيانات التجريبية")
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء البيانات: {e}")
        return False
    
    # 3. إنشاء DataFrame
    print("\n3️⃣ إنشاء DataFrame...")
    try:
        df = pd.DataFrame(test_data, columns=columns)
        print(f"   ✅ تم إنشاء DataFrame بحجم: {df.shape}")
        print(f"   📊 الأعمدة: {list(df.columns)}")
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء DataFrame: {e}")
        return False
    
    # 4. اختبار حفظ الملف
    print("\n4️⃣ اختبار حفظ ملف Excel...")
    try:
        test_filename = f"test_export_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # حفظ الملف
        df.to_excel(test_filename, index=False, engine='openpyxl')
        
        # التحقق من إنشاء الملف
        if os.path.exists(test_filename):
            file_size = os.path.getsize(test_filename)
            print(f"   ✅ تم إنشاء الملف: {test_filename}")
            print(f"   📁 حجم الملف: {file_size} بايت")
            
            # فتح الملف للتحقق
            try:
                test_df = pd.read_excel(test_filename, engine='openpyxl')
                print(f"   ✅ تم قراءة الملف بنجاح: {test_df.shape}")
                
                # حذف الملف التجريبي
                os.remove(test_filename)
                print("   ✅ تم حذف الملف التجريبي")
                
                return True
                
            except Exception as read_error:
                print(f"   ❌ خطأ في قراءة الملف: {read_error}")
                # حذف الملف في حالة الخطأ
                if os.path.exists(test_filename):
                    os.remove(test_filename)
                return False
                
        else:
            print("   ❌ فشل في إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في حفظ الملف: {e}")
        return False

def test_excel_export_with_arabic():
    """اختبار تصدير Excel مع النصوص العربية"""
    print("\n🔤 اختبار تصدير Excel مع النصوص العربية...")
    
    try:
        import pandas as pd
        
        # بيانات عربية
        arabic_data = [
            ["12345678901", "أحمد محمد علي", "35", "ذكر", "دم", "مستشفى الناصرية", "CBC", "طبيعي"],
            ["12345678902", "فاطمة حسن محمد", "28", "أنثى", "بول", "مركز صحي السوق", "Urine", "طبيعي"],
            ["12345678903", "علي أحمد حسين", "42", "ذكر", "براز", "مستشفى الحبوبي", "Stool", "طبيعي"]
        ]
        
        arabic_columns = ['الرقم الوطني', 'الاسم الكامل', 'العمر', 'الجنس', 'نوع العينة',
                         'جهة الإرسال', 'نوع التحليل', 'النتيجة']
        
        df = pd.DataFrame(arabic_data, columns=arabic_columns)
        
        test_filename = f"test_arabic_export_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # حفظ مع تشفير UTF-8
        with pd.ExcelWriter(test_filename, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='نتائج المختبر')
        
        if os.path.exists(test_filename):
            print(f"   ✅ تم إنشاء ملف Excel عربي: {test_filename}")
            
            # قراءة الملف للتحقق
            test_df = pd.read_excel(test_filename, engine='openpyxl', sheet_name='نتائج المختبر')
            print(f"   ✅ تم قراءة الملف العربي: {test_df.shape}")
            print(f"   📝 عينة من البيانات:")
            for i, row in test_df.head(2).iterrows():
                print(f"      {i+1}. {row['الاسم الكامل']} - {row['النتيجة']}")
            
            # حذف الملف التجريبي
            os.remove(test_filename)
            print("   ✅ تم حذف الملف التجريبي")
            
            return True
            
        else:
            print("   ❌ فشل في إنشاء الملف العربي")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النصوص العربية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل لتصدير Excel")
    print("=" * 60)
    
    # اختبار أساسي
    basic_test = test_excel_export()
    
    # اختبار النصوص العربية
    arabic_test = test_excel_export_with_arabic()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print("=" * 60)
    
    if basic_test:
        print("✅ الاختبار الأساسي: نجح")
    else:
        print("❌ الاختبار الأساسي: فشل")
    
    if arabic_test:
        print("✅ اختبار النصوص العربية: نجح")
    else:
        print("❌ اختبار النصوص العربية: فشل")
    
    if basic_test and arabic_test:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ تصدير Excel يعمل بشكل صحيح")
        print("✅ النصوص العربية تعمل بشكل صحيح")
        print("\n🚀 يمكنك الآن استخدام وظيفة تصدير Excel في البرنامج")
    else:
        print("\n❌ بعض الاختبارات فشلت")
        print("💡 تأكد من تثبيت المكتبات المطلوبة:")
        print("   pip install pandas openpyxl")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
