#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لوظائف التقارير المصلحة
Quick Test for Fixed Reports Functions
"""

import sqlite3
import datetime
import os

def quick_test():
    """اختبار سريع للتأكد من الإصلاحات"""
    print("🚀 اختبار سريع لوظائف التقارير المصلحة")
    print("=" * 50)
    
    # 1. فحص قاعدة البيانات
    print("1️⃣ فحص قاعدة البيانات...")
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM patients")
        patients_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM results")
        results_count = cursor.fetchone()[0]
        
        print(f"   ✅ المرضى: {patients_count}")
        print(f"   ✅ النتائج: {results_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
        return False
    
    # 2. فحص المكتبات
    print("\n2️⃣ فحص المكتبات المطلوبة...")
    try:
        import pandas as pd
        print("   ✅ pandas: متوفر")
        
        import openpyxl
        print("   ✅ openpyxl: متوفر")
        
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        print("   ✅ reportlab: متوفر")
        
    except ImportError as e:
        print(f"   ❌ مكتبة مفقودة: {e}")
        return False
    
    # 3. اختبار استعلام البيانات
    print("\n3️⃣ اختبار استعلام البيانات...")
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        query = '''
            SELECT p.national_id, p.name, p.age, p.gender, p.sample_type,
                   p.sender_org,
                   COALESCE(r.test_name, 'غير محدد') as test_name,
                   COALESCE(r.result, 'لا توجد نتيجة') as result,
                   COALESCE(r.result_date, '') as result_date
            FROM patients p
            LEFT JOIN results r ON p.id = r.patient_id
            ORDER BY p.receive_date DESC, p.national_id, r.test_name
            LIMIT 5
        '''
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"   ✅ تم جلب {len(results)} سجل")
        if results:
            print(f"   📋 مثال: {results[0][1]} - {results[0][6]} - {results[0][7]}")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في الاستعلام: {e}")
        return False
    
    # 4. اختبار إنشاء DataFrame
    print("\n4️⃣ اختبار إنشاء DataFrame...")
    try:
        import pandas as pd
        
        test_data = [
            [5001, 'أحمد', 30, 'M', 'دم', 'مستشفى', 'COVID-19', 'Negative', '2024-01-26'],
            [5002, 'فاطمة', 25, 'F', 'بول', 'مستشفى', 'Hepatitis', 'Positive', '2024-01-27']
        ]
        
        columns = ['الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة',
                  'جهة الإرسال', 'التحليل', 'النتيجة', 'تاريخ النتيجة']
        
        df = pd.DataFrame(test_data, columns=columns)
        print(f"   ✅ تم إنشاء DataFrame: {df.shape[0]} صف، {df.shape[1]} عمود")
        
    except Exception as e:
        print(f"   ❌ خطأ في DataFrame: {e}")
        return False
    
    # 5. اختبار إنشاء PDF
    print("\n5️⃣ اختبار إنشاء PDF...")
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        
        test_filename = f"test_quick_{datetime.datetime.now().strftime('%H%M%S')}.pdf"
        
        c = canvas.Canvas(test_filename, pagesize=A4)
        width, height = A4
        
        # اختبار الدالة المصلحة
        c.setFont("Helvetica-Bold", 16)
        title = "Test Report"
        title_width = c.stringWidth(title, "Helvetica-Bold", 16)
        c.drawString((width - title_width) / 2, height - 100, title)
        
        c.save()
        
        if os.path.exists(test_filename):
            print("   ✅ تم إنشاء ملف PDF تجريبي")
            os.remove(test_filename)
            print("   ✅ تم حذف الملف التجريبي")
        else:
            print("   ❌ فشل في إنشاء PDF")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في PDF: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 جميع الاختبارات نجحت!")
    print("✅ وظائف التقارير جاهزة للاستخدام:")
    print("   • تحميل البيانات تلقائياً")
    print("   • تصدير Excel")
    print("   • طباعة تقرير PDF")
    print("   • معاينة التقرير")
    print("\n🚀 يمكنك الآن استخدام البرنامج بثقة!")
    
    return True

if __name__ == "__main__":
    success = quick_test()
    if not success:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")
    
    input("\nاضغط Enter للخروج...")
