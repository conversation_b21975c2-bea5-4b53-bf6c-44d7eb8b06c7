#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لتصدير Excel
Quick Excel Export Test
"""

import datetime
import os

def quick_excel_test():
    """اختبار سريع لتصدير Excel"""
    print("🚀 اختبار سريع لتصدير Excel")
    print("=" * 50)
    
    # 1. فحص المكتبات
    print("\n1️⃣ فحص المكتبات المطلوبة...")
    try:
        import pandas as pd
        print("   ✅ pandas: متوفر")
        
        import openpyxl
        print("   ✅ openpyxl: متوفر")
        
    except ImportError as e:
        print(f"   ❌ مكتبة مفقودة: {e}")
        print("\n💡 لحل المشكلة:")
        print("pip install pandas openpyxl")
        return False
    
    # 2. إنشاء بيانات تجريبية
    print("\n2️⃣ إنشاء بيانات تجريبية...")
    try:
        test_data = [
            ["12345678901", "أحمد محمد علي", "35", "ذكر", "دم", "مستشفى الناصرية", "CBC", "طبيعي", "2025-01-12"],
            ["12345678902", "فاطمة حسن محمد", "28", "أنثى", "بول", "مركز صحي السوق", "Urine", "طبيعي", "2025-01-12"],
            ["12345678903", "علي أحمد حسين", "42", "ذكر", "براز", "مستشفى الحبوبي", "Stool", "طبيعي", "2025-01-12"],
            ["12345678904", "زينب كاظم محمد", "31", "أنثى", "دم", "مستشفى الناصرية", "CBC", "طبيعي", "2025-01-12"],
            ["12345678905", "محمد جاسم علي", "45", "ذكر", "بول", "مركز صحي الشطرة", "Urine", "طبيعي", "2025-01-12"]
        ]
        
        columns = ['الرقم الوطني', 'الاسم الكامل', 'العمر', 'الجنس', 'نوع العينة',
                  'جهة الإرسال', 'نوع التحليل', 'النتيجة', 'تاريخ النتيجة']
        
        print(f"   ✅ تم إنشاء {len(test_data)} صف من البيانات")
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء البيانات: {e}")
        return False
    
    # 3. إنشاء DataFrame
    print("\n3️⃣ إنشاء DataFrame...")
    try:
        df = pd.DataFrame(test_data, columns=columns)
        print(f"   ✅ تم إنشاء DataFrame بحجم: {df.shape}")
        print(f"   📊 الأعمدة: {list(df.columns)}")
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء DataFrame: {e}")
        return False
    
    # 4. حفظ ملف Excel
    print("\n4️⃣ حفظ ملف Excel...")
    try:
        filename = f"quick_test_export_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # حفظ مع ExcelWriter
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='نتائج المختبر')
        
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"   ✅ تم إنشاء الملف: {filename}")
            print(f"   📁 حجم الملف: {file_size} بايت")
            
            # قراءة الملف للتحقق
            test_df = pd.read_excel(filename, engine='openpyxl')
            print(f"   ✅ تم قراءة الملف: {test_df.shape}")
            
            return filename
            
        else:
            print("   ❌ فشل في إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في حفظ الملف: {e}")
        return False

def test_arabic_content():
    """اختبار المحتوى العربي"""
    print("\n🔤 اختبار المحتوى العربي...")
    
    try:
        import pandas as pd
        
        # بيانات عربية متنوعة
        arabic_data = [
            ["أحمد محمد علي الحسيني", "مستشفى الناصرية العام", "تحليل دم شامل", "جميع القيم طبيعية"],
            ["فاطمة حسن محمد الكربلائي", "مركز صحي السوق الكبير", "فحص البول الكامل", "لا توجد التهابات"],
            ["علي أحمد حسين البصري", "مستشفى الحبوبي التعليمي", "فحص البراز", "طبيعي - لا توجد طفيليات"],
            ["زينب كاظم محمد النجفي", "مستشفى الناصرية للنساء", "تحليل الحمل", "إيجابي - حمل طبيعي"],
            ["محمد جاسم علي الشطري", "مركز صحي الشطرة المركزي", "فحص السكر", "مستوى السكر طبيعي"]
        ]
        
        arabic_columns = ['اسم المريض الكامل', 'جهة الإرسال التفصيلية', 'نوع الفحص المطلوب', 'النتيجة التفصيلية']
        
        df = pd.DataFrame(arabic_data, columns=arabic_columns)
        
        filename = f"arabic_content_test_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # حفظ مع تنسيق خاص للعربية
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='المحتوى العربي')
            
            # تنسيق الورقة
            worksheet = writer.sheets['المحتوى العربي']
            
            # تعديل عرض الأعمدة
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"   ✅ تم إنشاء ملف المحتوى العربي: {filename}")
            print(f"   📁 حجم الملف: {file_size} بايت")
            
            # قراءة وعرض عينة
            test_df = pd.read_excel(filename, engine='openpyxl')
            print(f"   📝 عينة من البيانات:")
            for i, row in test_df.head(2).iterrows():
                print(f"      {i+1}. {row['اسم المريض الكامل']} - {row['النتيجة التفصيلية']}")
            
            return filename
        else:
            print("   ❌ فشل في إنشاء ملف المحتوى العربي")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار المحتوى العربي: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل وسريع لتصدير Excel")
    print("=" * 60)
    
    # الاختبار الأساسي
    basic_result = quick_excel_test()
    
    # اختبار المحتوى العربي
    arabic_result = test_arabic_content()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار النهائية:")
    print("=" * 60)
    
    if basic_result:
        print("✅ الاختبار الأساسي: نجح")
        print(f"   📁 الملف: {basic_result}")
    else:
        print("❌ الاختبار الأساسي: فشل")
    
    if arabic_result:
        print("✅ اختبار المحتوى العربي: نجح")
        print(f"   📁 الملف: {arabic_result}")
    else:
        print("❌ اختبار المحتوى العربي: فشل")
    
    if basic_result and arabic_result:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ تصدير Excel يعمل بشكل مثالي")
        print("✅ المحتوى العربي يعمل بشكل صحيح")
        print("✅ التنسيق والحفظ يعملان بشكل احترافي")
        
        print(f"\n📂 تم إنشاء الملفات:")
        print(f"   1. {basic_result}")
        print(f"   2. {arabic_result}")
        
        # سؤال عن فتح الملفات
        try:
            choice = input("\nهل تريد فتح الملفات؟ (y/n): ").lower().strip()
            if choice in ['y', 'yes', 'نعم', 'ن']:
                try:
                    os.startfile(basic_result)
                    os.startfile(arabic_result)
                    print("✅ تم فتح الملفات")
                except:
                    print("⚠️ لا يمكن فتح الملفات تلقائياً")
            else:
                # حذف الملفات التجريبية
                try:
                    os.remove(basic_result)
                    os.remove(arabic_result)
                    print("✅ تم حذف الملفات التجريبية")
                except:
                    print("⚠️ لا يمكن حذف الملفات التجريبية")
        except:
            pass
        
        print("\n🚀 تصدير Excel جاهز للاستخدام في البرنامج الرئيسي!")
        
    else:
        print("\n❌ بعض الاختبارات فشلت")
        print("💡 تأكد من تثبيت المكتبات المطلوبة:")
        print("   pip install pandas openpyxl")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
