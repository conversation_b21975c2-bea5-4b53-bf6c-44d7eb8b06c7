#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات النهائية
Test Final Fixes
"""

import sqlite3
import datetime
import os
from PIL import Image

def test_logo_in_reports():
    """اختبار الشعار في التقارير"""
    print("🖼️ اختبار الشعار في التقارير...")
    
    try:
        # التحقق من وجود الشعار الافتراضي
        if os.path.exists('ministry_logo.png'):
            print("✅ الشعار الافتراضي موجود: ministry_logo.png")
            
            # اختبار فتح الصورة
            img = Image.open('ministry_logo.png')
            print(f"✅ أبعاد الشعار: {img.width}x{img.height}")
            print(f"✅ تنسيق الشعار: {img.format}")
        else:
            print("❌ الشعار الافتراضي مفقود")
            return False
        
        # اختبار إعدادات الشعار في قاعدة البيانات
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # حفظ إعدادات الشعار
        cursor.execute('''
            INSERT OR REPLACE INTO settings (category, name, value)
            VALUES (?, ?, ?)
        ''', ('report_settings', 'logo_path', 'ministry_logo.png'))
        
        conn.commit()
        
        # التحقق من الحفظ
        cursor.execute("SELECT value FROM settings WHERE category = 'report_settings' AND name = 'logo_path'")
        result = cursor.fetchone()
        
        if result and result[0] == 'ministry_logo.png':
            print("✅ تم حفظ إعدادات الشعار في قاعدة البيانات")
        else:
            print("❌ فشل في حفظ إعدادات الشعار")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الشعار: {e}")
        return False

def test_multiple_selection():
    """اختبار التحديد المتعدد للنتائج"""
    print("\n📋 اختبار التحديد المتعدد للنتائج...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # إنشاء بيانات تجريبية للاختبار
        # إضافة مريض مع تحاليل متعددة
        cursor.execute('''
            INSERT OR IGNORE INTO patients 
            (national_id, name, age, gender, address, sample_type, phone, sender_org, sample_date, tests)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (999, 'مريض اختبار متعدد', 40, 'F', 'عنوان اختبار', 'دم', '07801234567', 
              'مستشفى اختبار', '2024-01-15', 'COVID-19,Hepatitis,CBC'))
        
        # إضافة وجبة تجريبية
        cursor.execute('''
            INSERT OR IGNORE INTO batches 
            (batch_no, start_date, end_date, technician)
            VALUES (?, ?, ?, ?)
        ''', (999, '2024-01-15', '2024-01-15', 'فني اختبار'))
        
        # الحصول على معرفات المريض والوجبة
        cursor.execute("SELECT id FROM patients WHERE national_id = 999")
        patient_result = cursor.fetchone()
        
        cursor.execute("SELECT id FROM batches WHERE batch_no = 999")
        batch_result = cursor.fetchone()
        
        if patient_result and batch_result:
            patient_id = patient_result[0]
            batch_id = batch_result[0]
            
            # إضافة نتائج متعددة
            tests = ['COVID-19', 'Hepatitis', 'CBC']
            for test in tests:
                cursor.execute('''
                    INSERT OR REPLACE INTO results 
                    (patient_id, batch_id, test_name, result, result_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', (patient_id, batch_id, test, 'لم يتم إدخال النتيجة', '2024-01-16'))
            
            print(f"✅ تم إنشاء {len(tests)} تحليل للاختبار")
            
            # اختبار تحديث نتائج متعددة
            for test in tests:
                cursor.execute('''
                    UPDATE results SET result = ? 
                    WHERE patient_id = ? AND batch_id = ? AND test_name = ?
                ''', ('Negative', patient_id, batch_id, test))
            
            print("✅ تم تحديث النتائج المتعددة بنجاح")
            
            # التحقق من النتائج
            cursor.execute('''
                SELECT test_name, result FROM results 
                WHERE patient_id = ? AND batch_id = ?
            ''', (patient_id, batch_id))
            
            results = cursor.fetchall()
            for test_name, result in results:
                print(f"  - {test_name}: {result}")
            
            # تنظيف البيانات التجريبية
            cursor.execute("DELETE FROM results WHERE patient_id = ?", (patient_id,))
            cursor.execute("DELETE FROM patients WHERE national_id = 999")
            cursor.execute("DELETE FROM batches WHERE batch_no = 999")
            
            print("✅ تم تنظيف البيانات التجريبية")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحديد المتعدد: {e}")
        return False

def test_report_generation():
    """اختبار إنشاء التقرير مع الشعار"""
    print("\n📄 اختبار إنشاء التقرير مع الشعار...")
    
    try:
        # اختبار استيراد المكتبات المطلوبة
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        from reportlab.lib.utils import ImageReader
        
        print("✅ تم استيراد مكتبات ReportLab بنجاح")
        
        # اختبار إنشاء تقرير تجريبي
        filename = "test_report.pdf"
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        # اختبار إضافة الشعار
        if os.path.exists('ministry_logo.png'):
            try:
                logo = ImageReader('ministry_logo.png')
                logo_width = 100
                logo_height = 100
                logo_x = (width - logo_width) / 2
                logo_y = height - 120
                
                c.drawImage(logo, logo_x, logo_y, width=logo_width, height=logo_height)
                print("✅ تم إضافة الشعار للتقرير التجريبي")
            except Exception as e:
                print(f"⚠️ خطأ في إضافة الشعار: {e}")
        
        # إضافة نص تجريبي
        c.setFont("Helvetica-Bold", 16)
        c.drawString(width/2-100, height-200, "Test Report - تقرير اختبار")
        
        c.save()
        
        # التحقق من إنشاء الملف
        if os.path.exists(filename):
            print(f"✅ تم إنشاء التقرير التجريبي: {filename}")
            
            # حذف الملف التجريبي
            os.remove(filename)
            print("✅ تم حذف التقرير التجريبي")
            
            return True
        else:
            print("❌ فشل في إنشاء التقرير")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء التقرير: {e}")
        return False

def test_database_integrity():
    """اختبار سلامة قاعدة البيانات"""
    print("\n🗄️ اختبار سلامة قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # اختبار الجداول المطلوبة
        tables = ['patients', 'batches', 'results', 'settings']
        for table in tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            result = cursor.fetchone()
            if result:
                print(f"✅ جدول {table} موجود")
            else:
                print(f"❌ جدول {table} مفقود")
                return False
        
        # اختبار أعمدة جدول النتائج
        cursor.execute("PRAGMA table_info(results)")
        columns = [row[1] for row in cursor.fetchall()]
        required_columns = ['id', 'patient_id', 'batch_id', 'test_name', 'result', 'result_date']
        
        for col in required_columns:
            if col in columns:
                print(f"✅ عمود {col} موجود في جدول النتائج")
            else:
                print(f"❌ عمود {col} مفقود في جدول النتائج")
                return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار الإصلاحات النهائية")
    print("=" * 60)
    
    # اختبار الشعار في التقارير
    logo_test = test_logo_in_reports()
    
    # اختبار التحديد المتعدد
    multiple_test = test_multiple_selection()
    
    # اختبار إنشاء التقرير
    report_test = test_report_generation()
    
    # اختبار سلامة قاعدة البيانات
    db_test = test_database_integrity()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"الشعار في التقارير: {'✅ نجح' if logo_test else '❌ فشل'}")
    print(f"التحديد المتعدد: {'✅ نجح' if multiple_test else '❌ فشل'}")
    print(f"إنشاء التقرير: {'✅ نجح' if report_test else '❌ فشل'}")
    print(f"سلامة قاعدة البيانات: {'✅ نجح' if db_test else '❌ فشل'}")
    
    if all([logo_test, multiple_test, report_test, db_test]):
        print("\n🎉 جميع الاختبارات نجحت! الإصلاحات النهائية جاهزة:")
        print("1. ✅ الشعار يظهر بشكل صحيح في التقارير")
        print("2. ✅ يمكن تحديد عدة تحاليل وإعطائهم نتيجة واحدة")
        print("3. ✅ إنشاء التقارير يعمل بشكل مثالي")
        print("4. ✅ قاعدة البيانات سليمة ومكتملة")
        print("\n🚀 يمكنك الآن تشغيل البرنامج واختبار الميزات الجديدة!")
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
