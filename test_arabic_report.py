#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التقرير العربي مع الشعار
Test Arabic Report with Logo
"""

import sqlite3
import datetime
import os

def test_arabic_report_generation():
    """اختبار إنشاء التقرير العربي"""
    print("🇸🇦 اختبار إنشاء التقرير العربي مع الشعار")
    print("=" * 60)
    
    try:
        # 1. فحص المكتبات المطلوبة
        print("1️⃣ فحص مكتبات reportlab...")
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        from reportlab.lib.utils import ImageReader
        print("   ✅ جميع مكتبات reportlab متوفرة")
        
        # 2. فحص قاعدة البيانات
        print("\n2️⃣ فحص قاعدة البيانات...")
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # فحص البيانات
        cursor.execute("SELECT COUNT(*) FROM patients")
        patients_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM results")
        results_count = cursor.fetchone()[0]
        
        print(f"   📊 المرضى: {patients_count}")
        print(f"   📊 النتائج: {results_count}")
        
        if patients_count == 0:
            print("   ⚠️ لا توجد بيانات - إضافة بيانات تجريبية...")
            # إضافة بيانات تجريبية
            test_data = [
                (6001, 'أحمد محمد علي', 35, 'M', 'بغداد', 'دم', '07801234567', 'مستشفى بغداد', '2024-01-25', 'كوفيد-19'),
                (6002, 'فاطمة حسن محمود', 28, 'F', 'البصرة', 'بول', '07801234568', 'مستشفى البصرة', '2024-01-26', 'التهاب الكبد'),
                (6003, 'علي أحمد حسين', 42, 'M', 'الموصل', 'دم', '07801234569', 'مستشفى الموصل', '2024-01-27', 'كوفيد-19')
            ]
            
            for patient in test_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO patients 
                    (national_id, name, age, gender, address, sample_type, phone, sender_org, sample_date, tests)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', patient)
            
            # إضافة نتائج
            cursor.execute("SELECT id FROM patients WHERE national_id IN (6001, 6002, 6003)")
            patient_ids = cursor.fetchall()
            
            test_results = [
                (patient_ids[0][0], 1, 'كوفيد-19', 'سلبي', '2024-01-26'),
                (patient_ids[1][0], 1, 'التهاب الكبد', 'إيجابي', '2024-01-27'),
                (patient_ids[2][0], 1, 'كوفيد-19', 'سلبي', '2024-01-28')
            ]
            
            for result in test_results:
                cursor.execute('''
                    INSERT OR REPLACE INTO results 
                    (patient_id, batch_id, test_name, result, result_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', result)
            
            conn.commit()
            print("   ✅ تم إضافة بيانات تجريبية عربية")
        
        # 3. اختبار إنشاء التقرير العربي
        print("\n3️⃣ اختبار إنشاء التقرير العربي...")
        
        # إنشاء ملف PDF تجريبي
        filename = f"تقرير_مختبر_تجريبي_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        print(f"   📄 إنشاء ملف: {filename}")
        print(f"   📐 أبعاد الصفحة: {width:.1f} x {height:.1f}")
        
        # 4. اختبار إضافة الشعار
        print("\n4️⃣ اختبار إضافة الشعار...")
        
        # فحص وجود ملف الشعار
        logo_files = ['ministry_logo.png', 'logo.png', 'icon.png']
        logo_path = None
        
        for logo_file in logo_files:
            if os.path.exists(logo_file):
                logo_path = logo_file
                break
        
        if logo_path:
            try:
                logo = ImageReader(logo_path)
                logo_width = 100
                logo_height = 100
                logo_x = (width - logo_width) / 2
                logo_y = height - 120
                
                c.drawImage(logo, logo_x, logo_y, width=logo_width, height=logo_height)
                print(f"   ✅ تم إضافة الشعار: {logo_path}")
                print(f"   📍 موضع الشعار: X={logo_x:.1f}, Y={logo_y:.1f}")
                header_start_y = height - 140
            except Exception as logo_error:
                print(f"   ⚠️ خطأ في الشعار: {logo_error}")
                # نص بديل
                c.setFont("Helvetica-Bold", 14)
                text = "وزارة الصحة - جمهورية العراق"
                text_width = c.stringWidth(text, "Helvetica-Bold", 14)
                text_x = (width - text_width) / 2
                c.drawString(text_x, height - 80, text)
                header_start_y = height - 100
                print("   ✅ تم إضافة نص بديل للشعار")
        else:
            print("   ⚠️ لم يتم العثور على ملف شعار")
            c.setFont("Helvetica-Bold", 14)
            text = "وزارة الصحة - جمهورية العراق"
            text_width = c.stringWidth(text, "Helvetica-Bold", 14)
            text_x = (width - text_width) / 2
            c.drawString(text_x, height - 80, text)
            header_start_y = height - 100
            print("   ✅ تم إضافة نص بديل للشعار")
        
        # 5. اختبار العناوين العربية
        print("\n5️⃣ اختبار العناوين العربية...")
        
        c.setFont("Helvetica-Bold", 14)
        main_header = [
            "جمهورية العراق",
            "وزارة الصحة",
            "قسم الصحة العامة - محافظة ذي قار",
            "مختبر الصحة العامة المركزي",
            "وحدة الفايروسات والأحياء المجهرية"
        ]
        
        y_pos = header_start_y
        for line in main_header:
            text_width = c.stringWidth(line, "Helvetica-Bold", 14)
            text_x = (width - text_width) / 2
            c.drawString(text_x, y_pos, line)
            y_pos -= 18
        
        print("   ✅ تم إضافة العناوين العربية")
        
        # خط فاصل
        c.setStrokeColorRGB(0.2, 0.2, 0.2)
        c.setLineWidth(2)
        c.line(80, y_pos - 10, width - 80, y_pos - 10)
        
        # 6. اختبار عنوان التقرير
        print("\n6️⃣ اختبار عنوان التقرير...")
        
        content_start_y = y_pos - 30
        c.setFont("Helvetica-Bold", 18)
        main_title = "تقرير نتائج الفحوصات المختبرية"
        title_width = c.stringWidth(main_title, "Helvetica-Bold", 18)
        c.drawString((width - title_width) / 2, content_start_y - 20, main_title)
        
        c.setFont("Helvetica-Bold", 14)
        sub_title = "Laboratory Test Results Report"
        sub_title_width = c.stringWidth(sub_title, "Helvetica-Bold", 14)
        c.drawString((width - sub_title_width) / 2, content_start_y - 45, sub_title)
        
        print("   ✅ تم إضافة عنوان التقرير")
        
        # 7. اختبار التاريخ العربي
        print("\n7️⃣ اختبار التاريخ العربي...")
        
        c.setFont("Helvetica", 12)
        current_date = datetime.datetime.now()
        arabic_date = current_date.strftime("%Y/%m/%d")
        arabic_time = current_date.strftime("%H:%M:%S")
        
        date_y = content_start_y - 80
        date_text = f"تاريخ إنشاء التقرير: {arabic_date}"
        date_width = c.stringWidth(date_text, "Helvetica", 12)
        c.drawString((width - date_width) / 2, date_y, date_text)
        
        time_text = f"وقت الإنشاء: {arabic_time}"
        time_width = c.stringWidth(time_text, "Helvetica", 12)
        c.drawString((width - time_width) / 2, date_y - 20, time_text)
        
        print("   ✅ تم إضافة التاريخ والوقت بالعربية")
        
        # 8. اختبار رؤوس الجدول العربية
        print("\n8️⃣ اختبار رؤوس الجدول العربية...")
        
        table_start_y = date_y - 60
        c.setFont("Helvetica-Bold", 10)
        
        arabic_headers = ["الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحليل", "النتيجة"]
        x_positions = [50, 120, 200, 240, 280, 340, 420, 480]
        
        for i, header in enumerate(arabic_headers):
            if i < len(x_positions):
                c.drawString(x_positions[i], table_start_y, header)
        
        c.line(40, table_start_y - 8, width - 40, table_start_y - 8)
        print("   ✅ تم إضافة رؤوس الجدول العربية")
        
        # 9. اختبار البيانات التجريبية
        print("\n9️⃣ اختبار البيانات التجريبية...")
        
        c.setFont("Helvetica", 9)
        y_position = table_start_y - 25
        
        # بيانات تجريبية
        test_data_display = [
            ["6001", "أحمد محمد علي", "35", "ذكر", "دم", "مستشفى بغداد", "كوفيد-19", "سلبي"],
            ["6002", "فاطمة حسن محمود", "28", "أنثى", "بول", "مستشفى البصرة", "التهاب الكبد", "إيجابي"],
            ["6003", "علي أحمد حسين", "42", "ذكر", "دم", "مستشفى الموصل", "كوفيد-19", "سلبي"]
        ]
        
        for row in test_data_display:
            for i, value in enumerate(row):
                if i < len(x_positions):
                    c.drawString(x_positions[i], y_position, value)
            y_position -= 15
        
        print("   ✅ تم إضافة البيانات التجريبية")
        
        # 10. اختبار التذييل العربي
        print("\n🔟 اختبار التذييل العربي...")
        
        # إحصائيات
        stats_y = y_position - 30
        c.setFont("Helvetica-Bold", 12)
        stats_text = f"إجمالي عدد السجلات في التقرير: {len(test_data_display)}"
        stats_width = c.stringWidth(stats_text, "Helvetica-Bold", 12)
        c.drawString((width - stats_width) / 2, stats_y, stats_text)
        
        # معلومات المختبر
        c.setFont("Helvetica", 10)
        footer_y = 80
        
        address_text = "العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة"
        address_width = c.stringWidth(address_text, "Helvetica", 10)
        c.drawString((width - address_width) / 2, footer_y, address_text)
        
        c.setFont("Helvetica", 9)
        info_text = "مختبر الصحة العامة المركزي - وحدة الفايروسات والأحياء المجهرية"
        info_width = c.stringWidth(info_text, "Helvetica", 9)
        c.drawString((width - info_width) / 2, footer_y - 15, info_text)
        
        # خط فاصل ورقم الصفحة
        c.line(60, footer_y + 20, width - 60, footer_y + 20)
        
        c.setFont("Helvetica", 8)
        page_text = "صفحة 1"
        page_width = c.stringWidth(page_text, "Helvetica", 8)
        c.drawString((width - page_width) / 2, 30, page_text)
        
        print("   ✅ تم إضافة التذييل العربي")
        
        # حفظ الملف
        c.save()
        
        # 11. التحقق من النتيجة
        print("\n1️⃣1️⃣ التحقق من النتيجة...")
        
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"   ✅ تم إنشاء التقرير العربي بنجاح!")
            print(f"   📄 اسم الملف: {filename}")
            print(f"   📊 حجم الملف: {file_size} بايت")
            
            # حذف الملف التجريبي
            try:
                os.remove(filename)
                print("   🗑️ تم حذف الملف التجريبي")
            except:
                print("   ⚠️ لم يتم حذف الملف التجريبي")
            
            return True
        else:
            print("   ❌ فشل في إنشاء التقرير")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التقرير العربي: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🇸🇦 اختبار التقرير العربي مع الشعار")
    print("=" * 60)
    
    success = test_arabic_report_generation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 نجح اختبار التقرير العربي!")
        print("✅ المميزات المتوفرة:")
        print("   🇸🇦 التقرير باللغة العربية كاملاً")
        print("   🏥 الشعار في أعلى منتصف التقرير")
        print("   📋 رؤوس الجدول باللغة العربية")
        print("   📊 البيانات مع ترجمة الجنس (ذكر/أنثى)")
        print("   📅 التاريخ والوقت بالتنسيق العربي")
        print("   📍 العنوان والمعلومات باللغة العربية")
        print("   📄 اسم الملف باللغة العربية")
        print("\n🚀 يمكنك الآن استخدام طباعة التقرير في البرنامج!")
    else:
        print("❌ فشل اختبار التقرير العربي")
        print("يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
