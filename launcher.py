#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل برنامج مختبر الصحة العامة المركزي - ذي قار
Central Public Health Laboratory - Dhi Qar Launcher
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox
import time

class LabLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مختبر الصحة العامة المركزي - ذي قار")
        self.root.geometry("500x400")
        self.root.configure(bg='#2c3e50')
        self.root.resizable(False, False)
        
        # جعل النافذة في المنتصف
        self.center_window()
        
        self.create_interface()
    
    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_interface(self):
        """إنشاء واجهة المشغل"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50')
        title_frame.pack(pady=20)
        
        tk.Label(title_frame, text="مختبر الصحة العامة المركزي", 
                font=('Arial', 18, 'bold'), fg='#ecf0f1', bg='#2c3e50').pack()
        tk.Label(title_frame, text="محافظة ذي قار", 
                font=('Arial', 14, 'bold'), fg='#3498db', bg='#2c3e50').pack()
        tk.Label(title_frame, text="Central Public Health Laboratory - Dhi Qar", 
                font=('Arial', 12), fg='#95a5a6', bg='#2c3e50').pack(pady=5)
        
        # معلومات النظام
        info_frame = tk.Frame(self.root, bg='#34495e', relief='raised', bd=2)
        info_frame.pack(pady=20, padx=20, fill='x')
        
        tk.Label(info_frame, text="معلومات النظام", 
                font=('Arial', 12, 'bold'), fg='#ecf0f1', bg='#34495e').pack(pady=5)
        
        # فحص Python
        python_status = self.check_python()
        python_color = '#27ae60' if python_status['status'] else '#e74c3c'
        tk.Label(info_frame, text=f"Python: {python_status['message']}", 
                font=('Arial', 10), fg=python_color, bg='#34495e').pack()
        
        # فحص ملفات البرنامج
        files_status = self.check_files()
        files_color = '#27ae60' if files_status['status'] else '#e74c3c'
        tk.Label(info_frame, text=f"ملفات البرنامج: {files_status['message']}", 
                font=('Arial', 10), fg=files_color, bg='#34495e').pack()
        
        # فحص المكتبات
        libs_status = self.check_libraries()
        libs_color = '#27ae60' if libs_status['status'] else '#f39c12'
        tk.Label(info_frame, text=f"المكتبات المطلوبة: {libs_status['message']}", 
                font=('Arial', 10), fg=libs_color, bg='#34495e').pack(pady=(0, 5))
        
        # أزرار التحكم
        buttons_frame = tk.Frame(self.root, bg='#2c3e50')
        buttons_frame.pack(pady=20)
        
        # زر تشغيل البرنامج
        start_btn = tk.Button(buttons_frame, text="🚀 تشغيل البرنامج", 
                             font=('Arial', 14, 'bold'), bg='#27ae60', fg='white',
                             command=self.start_program, width=15, height=2)
        start_btn.pack(pady=5)
        
        # زر فحص المتطلبات
        check_btn = tk.Button(buttons_frame, text="🔍 فحص المتطلبات", 
                             font=('Arial', 12), bg='#3498db', fg='white',
                             command=self.check_requirements, width=15)
        check_btn.pack(pady=5)
        
        # زر تثبيت المكتبات
        install_btn = tk.Button(buttons_frame, text="📦 تثبيت المكتبات", 
                               font=('Arial', 12), bg='#f39c12', fg='white',
                               command=self.install_libraries, width=15)
        install_btn.pack(pady=5)
        
        # زر الخروج
        exit_btn = tk.Button(buttons_frame, text="❌ خروج", 
                            font=('Arial', 12), bg='#e74c3c', fg='white',
                            command=self.root.quit, width=15)
        exit_btn.pack(pady=5)
        
        # معلومات إضافية
        footer_frame = tk.Frame(self.root, bg='#2c3e50')
        footer_frame.pack(side='bottom', pady=10)
        
        tk.Label(footer_frame, text="© 2025 مختبر الصحة العامة المركزي - ذي قار", 
                font=('Arial', 8), fg='#95a5a6', bg='#2c3e50').pack()
    
    def check_python(self):
        """فحص وجود Python"""
        try:
            result = subprocess.run([sys.executable, '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                return {'status': True, 'message': f'متوفر ({version})'}
            else:
                return {'status': False, 'message': 'غير متوفر'}
        except:
            return {'status': False, 'message': 'غير متوفر'}
    
    def check_files(self):
        """فحص وجود ملفات البرنامج"""
        required_files = ['main.py']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if not missing_files:
            return {'status': True, 'message': 'جميع الملفات موجودة'}
        else:
            return {'status': False, 'message': f'ملفات مفقودة: {", ".join(missing_files)}'}
    
    def check_libraries(self):
        """فحص المكتبات المطلوبة"""
        required_libs = ['tkinter', 'sqlite3', 'pandas', 'PIL', 'reportlab', 'openpyxl']
        missing_libs = []
        
        for lib in required_libs:
            try:
                if lib == 'PIL':
                    import PIL
                elif lib == 'tkinter':
                    import tkinter
                else:
                    __import__(lib)
            except ImportError:
                missing_libs.append(lib)
        
        if not missing_libs:
            return {'status': True, 'message': 'جميع المكتبات متوفرة'}
        else:
            return {'status': False, 'message': f'مكتبات مفقودة: {len(missing_libs)}'}
    
    def start_program(self):
        """تشغيل البرنامج الرئيسي"""
        if not os.path.exists('main.py'):
            messagebox.showerror("خطأ", "ملف main.py غير موجود!")
            return
        
        try:
            # إخفاء نافذة المشغل
            self.root.withdraw()
            
            # تشغيل البرنامج الرئيسي
            subprocess.run([sys.executable, 'main.py'])
            
            # إظهار نافذة المشغل مرة أخرى
            self.root.deiconify()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل البرنامج:\n{str(e)}")
            self.root.deiconify()
    
    def check_requirements(self):
        """فحص شامل للمتطلبات"""
        report = "تقرير فحص المتطلبات:\n\n"
        
        # فحص Python
        python_status = self.check_python()
        report += f"Python: {python_status['message']}\n"
        
        # فحص الملفات
        files_status = self.check_files()
        report += f"ملفات البرنامج: {files_status['message']}\n"
        
        # فحص المكتبات بالتفصيل
        required_libs = ['tkinter', 'sqlite3', 'pandas', 'PIL', 'reportlab', 'openpyxl']
        report += "\nالمكتبات المطلوبة:\n"
        
        for lib in required_libs:
            try:
                if lib == 'PIL':
                    import PIL
                elif lib == 'tkinter':
                    import tkinter
                else:
                    __import__(lib)
                report += f"  ✅ {lib}: متوفر\n"
            except ImportError:
                report += f"  ❌ {lib}: غير متوفر\n"
        
        messagebox.showinfo("تقرير فحص المتطلبات", report)
    
    def install_libraries(self):
        """تثبيت المكتبات المطلوبة"""
        libraries = ['pandas', 'pillow', 'reportlab', 'openpyxl', 'pywin32']
        
        result = messagebox.askyesno("تثبيت المكتبات", 
                                   f"هل تريد تثبيت المكتبات المطلوبة؟\n\n{', '.join(libraries)}")
        
        if result:
            try:
                for lib in libraries:
                    subprocess.run([sys.executable, '-m', 'pip', 'install', lib], 
                                 check=True)
                
                messagebox.showinfo("نجح", "تم تثبيت جميع المكتبات بنجاح!")
                
                # تحديث واجهة المشغل
                self.root.destroy()
                self.__init__()
                
            except subprocess.CalledProcessError as e:
                messagebox.showerror("خطأ", f"فشل في تثبيت المكتبات:\n{str(e)}")
    
    def run(self):
        """تشغيل المشغل"""
        self.root.mainloop()

if __name__ == "__main__":
    launcher = LabLauncher()
    launcher.run()
