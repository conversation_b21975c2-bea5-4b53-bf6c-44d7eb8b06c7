#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للنصوص العربية في التقارير
Comprehensive Arabic Text Test for Reports
"""

import os
import datetime

def test_arabic_libraries():
    """اختبار مكتبات النصوص العربية"""
    print("🔍 اختبار مكتبات النصوص العربية...")
    
    try:
        import arabic_reshaper
        from bidi.algorithm import get_display
        print("✅ arabic_reshaper: متوفر")
        print("✅ python-bidi: متوفر")
        
        # اختبار معالجة النص
        test_text = "مختبر الصحة العامة المركزي"
        reshaped = arabic_reshaper.reshape(test_text)
        bidi_text = get_display(reshaped)
        
        print(f"📝 النص الأصلي: {test_text}")
        print(f"📝 النص المعالج: {bidi_text}")
        
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في المعالجة: {e}")
        return False

def test_fonts():
    """اختبار الخطوط العربية"""
    print("\n🔤 اختبار الخطوط العربية...")
    
    arabic_fonts = [
        "C:/Windows/Fonts/arial.ttf",
        "C:/Windows/Fonts/tahoma.ttf",
        "C:/Windows/Fonts/calibri.ttf",
        "C:/Windows/Fonts/times.ttf",
        "C:/Windows/Fonts/verdana.ttf"
    ]
    
    available_fonts = []
    for font_path in arabic_fonts:
        if os.path.exists(font_path):
            available_fonts.append(font_path)
            print(f"✅ {font_path}: متوفر")
        else:
            print(f"❌ {font_path}: غير متوفر")
    
    if available_fonts:
        print(f"✅ تم العثور على {len(available_fonts)} خط عربي")
        return True
    else:
        print("❌ لم يتم العثور على أي خط عربي")
        return False

def create_arabic_pdf_test():
    """إنشاء ملف PDF تجريبي مع النصوص العربية"""
    print("\n📄 إنشاء ملف PDF تجريبي مع النصوص العربية...")
    
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import arabic_reshaper
        from bidi.algorithm import get_display
        
        # إنشاء ملف PDF
        filename = f"اختبار_النصوص_العربية_نهائي_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        print(f"📄 إنشاء ملف: {filename}")
        
        # تحميل خط عربي
        arabic_font_name = "ArabicFont"
        font_loaded = False
        
        arabic_fonts = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/tahoma.ttf",
            "C:/Windows/Fonts/calibri.ttf"
        ]
        
        for font_path in arabic_fonts:
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont(arabic_font_name, font_path))
                    font_loaded = True
                    print(f"✅ تم تحميل الخط: {font_path}")
                    break
                except Exception as e:
                    print(f"⚠️ فشل تحميل الخط: {font_path} - {e}")
                    continue
        
        if not font_loaded:
            arabic_font_name = "Helvetica"
            print("⚠️ استخدام Helvetica كبديل")
        
        # دالة معالجة النص العربي
        def process_arabic_text(text):
            try:
                if not text or not isinstance(text, str):
                    return str(text) if text else ""
                
                has_arabic = any('\u0600' <= char <= '\u06FF' for char in text)
                
                if has_arabic:
                    reshaped_text = arabic_reshaper.reshape(text)
                    bidi_text = get_display(reshaped_text)
                    return bidi_text
                else:
                    return text
            except Exception as e:
                print(f"⚠️ خطأ في معالجة النص: {e}")
                return text
        
        # دالة رسم النص الآمنة
        def draw_text_safe(canvas_obj, x, y, text, font_name, font_size):
            try:
                processed_text = process_arabic_text(text)
                canvas_obj.setFont(font_name, font_size)
                canvas_obj.drawString(x, y, processed_text)
                return True
            except Exception as e:
                try:
                    canvas_obj.setFont("Helvetica", font_size)
                    canvas_obj.drawString(x, y, processed_text)
                    return True
                except:
                    print(f"⚠️ فشل في رسم النص: {text}")
                    return False
        
        # دالة حساب عرض النص
        def get_text_width_safe(canvas_obj, text, font_name, font_size):
            try:
                processed_text = process_arabic_text(text)
                return canvas_obj.stringWidth(processed_text, font_name, font_size)
            except:
                try:
                    return canvas_obj.stringWidth(processed_text, "Helvetica", font_size)
                except:
                    return 100
        
        # اختبار النصوص العربية
        arabic_texts = [
            "جمهورية العراق",
            "وزارة الصحة",
            "قسم الصحة العامة - محافظة ذي قار",
            "مختبر الصحة العامة المركزي",
            "وحدة الفايروسات والأحياء المجهرية",
            "تقرير نتائج الفحوصات المختبرية",
            "تاريخ إنشاء التقرير: 2025/07/12",
            "وقت الإنشاء: 05:30:00"
        ]
        
        y_pos = height - 100
        
        # رسم العناوين
        for i, text in enumerate(arabic_texts):
            font_size = 16 if i < 2 else 14 if i < 5 else 12
            font_name = arabic_font_name + "-Bold" if i < 6 else arabic_font_name
            
            text_width = get_text_width_safe(c, text, font_name, font_size)
            x_pos = (width - text_width) / 2
            
            success = draw_text_safe(c, x_pos, y_pos, text, font_name, font_size)
            
            if success:
                print(f"✅ تم رسم: {text}")
            else:
                print(f"❌ فشل رسم: {text}")
            
            y_pos -= 25
        
        # اختبار رؤوس الجدول
        y_pos -= 30
        headers = ["الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحليل", "النتيجة"]
        x_positions = [50, 120, 200, 240, 280, 340, 420, 480]
        
        for i, header in enumerate(headers):
            if i < len(x_positions):
                success = draw_text_safe(c, x_positions[i], y_pos, header, arabic_font_name + "-Bold", 10)
                if success:
                    print(f"✅ رأس الجدول: {header}")
                else:
                    print(f"❌ فشل رأس الجدول: {header}")
        
        # خط تحت الرؤوس
        c.line(40, y_pos - 8, width - 40, y_pos - 8)
        
        # اختبار البيانات
        y_pos -= 30
        test_data = [
            ["5001", "أحمد محمد علي", "35", "ذكر", "دم", "مستشفى بغداد", "كوفيد-19", "سلبي"],
            ["5002", "فاطمة حسن", "28", "أنثى", "بول", "مستشفى البصرة", "التهاب الكبد", "إيجابي"],
            ["5003", "علي أحمد", "42", "ذكر", "دم", "مستشفى الموصل", "كوفيد-19", "سلبي"]
        ]
        
        for row in test_data:
            for i, cell in enumerate(row):
                if i < len(x_positions):
                    success = draw_text_safe(c, x_positions[i], y_pos, cell, arabic_font_name, 9)
                    if not success:
                        print(f"❌ فشل في رسم البيانات: {cell}")
            y_pos -= 15
        
        # اختبار التذييل
        y_pos = 100
        footer_texts = [
            "إجمالي عدد السجلات في التقرير: 3",
            "العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة",
            "مختبر الصحة العامة المركزي - وحدة الفايروسات والأحياء المجهرية",
            "صفحة 1"
        ]
        
        for text in footer_texts:
            text_width = get_text_width_safe(c, text, arabic_font_name, 10)
            x_pos = (width - text_width) / 2
            success = draw_text_safe(c, x_pos, y_pos, text, arabic_font_name, 10)
            
            if success:
                print(f"✅ تذييل: {text[:30]}...")
            else:
                print(f"❌ فشل تذييل: {text[:30]}...")
            
            y_pos -= 15
        
        # حفظ الملف
        c.save()
        
        if os.path.exists(filename):
            print(f"✅ تم إنشاء ملف PDF: {filename}")
            
            # فتح الملف
            try:
                os.startfile(filename)
                print("📂 تم فتح الملف للمراجعة")
            except:
                print("📂 يمكنك فتح الملف يدوياً")
            
            return True
        else:
            print("❌ فشل في إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🇸🇦 اختبار شامل للنصوص العربية في التقارير")
    print("=" * 60)
    
    # اختبار المكتبات
    libs_test = test_arabic_libraries()
    
    # اختبار الخطوط
    fonts_test = test_fonts()
    
    # اختبار إنشاء PDF
    pdf_test = create_arabic_pdf_test()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار الشامل:")
    print(f"مكتبات النصوص العربية: {'✅ نجح' if libs_test else '❌ فشل'}")
    print(f"الخطوط العربية: {'✅ نجح' if fonts_test else '❌ فشل'}")
    print(f"إنشاء PDF عربي: {'✅ نجح' if pdf_test else '❌ فشل'}")
    
    if all([libs_test, fonts_test, pdf_test]):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النصوص العربية جاهزة للعمل:")
        print("   📦 مكتبات المعالجة متوفرة")
        print("   🔤 خطوط عربية محملة")
        print("   📄 PDF عربي تم إنشاؤه بنجاح")
        print("\n🚀 يمكنك الآن استخدام البرنامج لطباعة التقارير العربية!")
        print("📋 الخطوات:")
        print("1. شغل البرنامج: python main.py")
        print("2. اذهب لتبويب 'التقارير والإحصائيات'")
        print("3. اضغط 'طباعة التقرير'")
        print("4. راجع التقرير العربي الجديد")
    else:
        print("\n❌ بعض الاختبارات فشلت")
        if not libs_test:
            print("💡 لحل مشكلة المكتبات:")
            print("   py -m pip install arabic-reshaper python-bidi")
        if not fonts_test:
            print("💡 تأكد من وجود خطوط عربية في النظام")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
