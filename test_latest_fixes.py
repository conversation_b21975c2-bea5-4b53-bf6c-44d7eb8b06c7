#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات الأخيرة
Test Latest Fixes
"""

import sqlite3
import datetime
import os

def test_duplicate_name_detection():
    """اختبار كشف الأسماء المكررة"""
    print("👥 اختبار كشف الأسماء المكررة...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # إضافة مريض تجريبي
        test_name = "أحمد محمد علي"
        cursor.execute('''
            INSERT OR IGNORE INTO patients 
            (national_id, name, age, gender, address, sample_type, phone, sender_org, sample_date, tests)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (1001, test_name, 30, 'M', 'عنوان اختبار 1', 'دم', '07801234567', 
              'مستشفى اختبار', '2024-01-15', 'COVID-19'))
        
        # إضافة مريض آخر بنفس الاسم
        cursor.execute('''
            INSERT OR IGNORE INTO patients 
            (national_id, name, age, gender, address, sample_type, phone, sender_org, sample_date, tests)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (1002, test_name, 35, 'M', 'عنوان اختبار 2', 'دم', '07801234568', 
              'مستشفى اختبار 2', '2024-01-16', 'Hepatitis'))
        
        conn.commit()
        
        # اختبار البحث عن الأسماء المكررة
        cursor.execute("SELECT COUNT(*) FROM patients WHERE LOWER(name) = LOWER(?)", (test_name,))
        count = cursor.fetchone()[0]
        
        if count >= 2:
            print(f"✅ تم العثور على {count} مريض بنفس الاسم '{test_name}'")
            
            # اختبار استرجاع البيانات المكررة
            cursor.execute('''
                SELECT national_id, name, age, gender, address, phone, sender_org, sample_date
                FROM patients 
                WHERE LOWER(name) = LOWER(?)
                ORDER BY sample_date DESC
            ''', (test_name,))
            
            duplicates = cursor.fetchall()
            print("✅ بيانات المرضى المكررين:")
            for i, patient in enumerate(duplicates, 1):
                print(f"  {i}. الرقم الوطني: {patient[0]}, العمر: {patient[2]}, العنوان: {patient[4]}")
        else:
            print("❌ لم يتم العثور على أسماء مكررة")
            return False
        
        # تنظيف البيانات التجريبية
        cursor.execute("DELETE FROM patients WHERE national_id IN (1001, 1002)")
        conn.commit()
        print("✅ تم تنظيف البيانات التجريبية")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار كشف الأسماء المكررة: {e}")
        return False

def test_result_editing():
    """اختبار تعديل النتائج"""
    print("\n✏️ اختبار تعديل النتائج...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # إنشاء بيانات تجريبية
        # إضافة مريض
        cursor.execute('''
            INSERT OR IGNORE INTO patients 
            (national_id, name, age, gender, address, sample_type, phone, sender_org, sample_date, tests)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (2001, 'مريض تعديل النتيجة', 40, 'F', 'عنوان اختبار', 'دم', '07801234567', 
              'مستشفى اختبار', '2024-01-15', 'COVID-19'))
        
        # إضافة وجبة
        cursor.execute('''
            INSERT OR IGNORE INTO batches 
            (batch_no, start_date, end_date, technician)
            VALUES (?, ?, ?, ?)
        ''', (2001, '2024-01-15', '2024-01-15', 'فني اختبار'))
        
        # الحصول على معرفات المريض والوجبة
        cursor.execute("SELECT id FROM patients WHERE national_id = 2001")
        patient_result = cursor.fetchone()
        
        cursor.execute("SELECT id FROM batches WHERE batch_no = 2001")
        batch_result = cursor.fetchone()
        
        if patient_result and batch_result:
            patient_id = patient_result[0]
            batch_id = batch_result[0]
            
            # إضافة نتيجة أولية
            cursor.execute('''
                INSERT OR REPLACE INTO results 
                (patient_id, batch_id, test_name, result, result_date)
                VALUES (?, ?, ?, ?, ?)
            ''', (patient_id, batch_id, 'COVID-19', 'Negative', '2024-01-16'))
            
            print("✅ تم إضافة نتيجة أولية: Negative")
            
            # اختبار تعديل النتيجة
            cursor.execute('''
                UPDATE results SET result = ?, result_date = DATE('now')
                WHERE patient_id = ? AND batch_id = ? AND test_name = ?
            ''', ('Positive', patient_id, batch_id, 'COVID-19'))
            
            # التحقق من التعديل
            cursor.execute('''
                SELECT result FROM results 
                WHERE patient_id = ? AND batch_id = ? AND test_name = ?
            ''', (patient_id, batch_id, 'COVID-19'))
            
            updated_result = cursor.fetchone()
            if updated_result and updated_result[0] == 'Positive':
                print("✅ تم تعديل النتيجة بنجاح من Negative إلى Positive")
            else:
                print("❌ فشل في تعديل النتيجة")
                return False
            
            # تنظيف البيانات التجريبية
            cursor.execute("DELETE FROM results WHERE patient_id = ?", (patient_id,))
            cursor.execute("DELETE FROM patients WHERE national_id = 2001")
            cursor.execute("DELETE FROM batches WHERE batch_no = 2001")
            
            print("✅ تم تنظيف البيانات التجريبية")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تعديل النتائج: {e}")
        return False

def test_logo_positioning():
    """اختبار موضع الشعار"""
    print("\n🖼️ اختبار موضع الشعار...")
    
    try:
        # التحقق من وجود الشعار
        if not os.path.exists('ministry_logo.png'):
            print("❌ الشعار الافتراضي غير موجود")
            return False
        
        # اختبار حساب موضع الشعار
        from reportlab.lib.pagesizes import A4
        width, height = A4
        
        # حساب موضع الشعار (يجب أن يكون في المنتصف)
        logo_width = 80
        logo_height = 80
        logo_x = (width - logo_width) / 2  # منتصف العرض
        logo_y = height - 90  # أعلى الصفحة
        
        print(f"✅ أبعاد الصفحة: {width:.1f} x {height:.1f}")
        print(f"✅ موضع الشعار: X={logo_x:.1f}, Y={logo_y:.1f}")
        print(f"✅ أبعاد الشعار: {logo_width} x {logo_height}")
        
        # التحقق من أن الشعار في المنتصف
        center_x = width / 2
        calculated_center = logo_x + (logo_width / 2)
        
        if abs(center_x - calculated_center) < 1:  # تسامح 1 نقطة
            print("✅ الشعار موضوع في منتصف الصفحة بشكل صحيح")
        else:
            print(f"❌ الشعار ليس في المنتصف: المركز الفعلي {center_x:.1f}, المحسوب {calculated_center:.1f}")
            return False
        
        # اختبار إنشاء تقرير تجريبي مع الشعار
        from reportlab.pdfgen import canvas
        from reportlab.lib.utils import ImageReader
        
        filename = "test_logo_position.pdf"
        c = canvas.Canvas(filename, pagesize=A4)
        
        # إضافة الشعار
        logo = ImageReader('ministry_logo.png')
        c.drawImage(logo, logo_x, logo_y, width=logo_width, height=logo_height)
        
        # إضافة خطوط مرجعية لتوضيح المنتصف
        c.setStrokeColorRGB(0.8, 0.8, 0.8)  # لون رمادي فاتح
        c.line(center_x, 0, center_x, height)  # خط عمودي في المنتصف
        
        # إضافة نص توضيحي
        c.setFont("Helvetica", 10)
        c.drawString(50, height - 200, f"Logo position: X={logo_x:.1f}, Y={logo_y:.1f}")
        c.drawString(50, height - 220, f"Page center: X={center_x:.1f}")
        c.drawString(50, height - 240, f"Logo center: X={calculated_center:.1f}")
        
        c.save()
        
        if os.path.exists(filename):
            print(f"✅ تم إنشاء تقرير اختبار الشعار: {filename}")
            # حذف الملف التجريبي
            os.remove(filename)
            print("✅ تم حذف التقرير التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار موضع الشعار: {e}")
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("\n🗄️ اختبار عمليات قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('lab_database.db')
        cursor = conn.cursor()
        
        # اختبار البحث بحساسية الأحرف
        test_names = ["أحمد محمد", "أحمد محمد", "AHMED MOHAMMED"]
        
        for name in test_names:
            cursor.execute("SELECT COUNT(*) FROM patients WHERE LOWER(name) = LOWER(?)", (name,))
            count = cursor.fetchone()[0]
            print(f"✅ البحث عن '{name}': {count} نتيجة")
        
        # اختبار تحديث النتائج
        cursor.execute("SELECT COUNT(*) FROM results")
        results_count = cursor.fetchone()[0]
        print(f"✅ عدد النتائج في قاعدة البيانات: {results_count}")
        
        # اختبار الفهارس والأداء
        cursor.execute("EXPLAIN QUERY PLAN SELECT * FROM patients WHERE LOWER(name) = LOWER('test')")
        query_plan = cursor.fetchall()
        print(f"✅ خطة الاستعلام: {len(query_plan)} خطوة")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار الإصلاحات الأخيرة")
    print("=" * 60)
    
    # اختبار كشف الأسماء المكررة
    duplicate_test = test_duplicate_name_detection()
    
    # اختبار تعديل النتائج
    edit_test = test_result_editing()
    
    # اختبار موضع الشعار
    logo_test = test_logo_positioning()
    
    # اختبار عمليات قاعدة البيانات
    db_test = test_database_operations()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"كشف الأسماء المكررة: {'✅ نجح' if duplicate_test else '❌ فشل'}")
    print(f"تعديل النتائج: {'✅ نجح' if edit_test else '❌ فشل'}")
    print(f"موضع الشعار: {'✅ نجح' if logo_test else '❌ فشل'}")
    print(f"عمليات قاعدة البيانات: {'✅ نجح' if db_test else '❌ فشل'}")
    
    if all([duplicate_test, edit_test, logo_test, db_test]):
        print("\n🎉 جميع الاختبارات نجحت! الإصلاحات الأخيرة جاهزة:")
        print("1. ✅ كشف الأسماء المكررة مع خيارات متعددة")
        print("2. ✅ تعديل النتائج في أي وقت مع نافذة تفاعلية")
        print("3. ✅ الشعار يظهر في منتصف أعلى الصفحة بشكل مثالي")
        print("4. ✅ قاعدة البيانات تعمل بكفاءة عالية")
        print("\n🚀 يمكنك الآن تشغيل البرنامج واختبار الميزات الجديدة!")
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
