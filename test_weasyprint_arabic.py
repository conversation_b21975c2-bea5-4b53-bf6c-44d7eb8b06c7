#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار WeasyPrint للنصوص العربية
WeasyPrint Arabic Text Test
"""

import os
import datetime

def test_weasyprint_arabic():
    """اختبار WeasyPrint مع النصوص العربية"""
    print("🔍 اختبار WeasyPrint للنصوص العربية...")
    
    try:
        import weasyprint
        from weasyprint import HTML, CSS
        print("✅ WeasyPrint: متوفر")
        
        # إنشاء HTML عربي تجريبي
        html_content = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار النصوص العربية</title>
</head>
<body>
    <div style="text-align: center; font-family: Aria<PERSON>, <PERSON><PERSON><PERSON>, sans-serif;">
        <h1>جمهورية العراق</h1>
        <h2>وزارة الصحة</h2>
        <h3>قسم الصحة العامة - محافظة ذي قار</h3>
        <h3>مختبر الصحة العامة المركزي</h3>
        <h4>وحدة الفايروسات والأحياء المجهرية</h4>
        
        <hr>
        
        <h2>تقرير نتائج الفحوصات المختبرية</h2>
        <h3>Laboratory Test Results Report</h3>
        
        <p>تاريخ إنشاء التقرير: 2025/07/12</p>
        <p>وقت الإنشاء: 06:00:00</p>
        
        <table border="1" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="padding: 8px;">الرقم الوطني</th>
                    <th style="padding: 8px;">الاسم</th>
                    <th style="padding: 8px;">العمر</th>
                    <th style="padding: 8px;">الجنس</th>
                    <th style="padding: 8px;">نوع العينة</th>
                    <th style="padding: 8px;">جهة الإرسال</th>
                    <th style="padding: 8px;">التحليل</th>
                    <th style="padding: 8px;">النتيجة</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="padding: 6px;">5001</td>
                    <td style="padding: 6px;">أحمد محمد علي</td>
                    <td style="padding: 6px;">35</td>
                    <td style="padding: 6px;">ذكر</td>
                    <td style="padding: 6px;">دم</td>
                    <td style="padding: 6px;">مستشفى بغداد</td>
                    <td style="padding: 6px;">كوفيد-19</td>
                    <td style="padding: 6px;">سلبي</td>
                </tr>
                <tr>
                    <td style="padding: 6px;">5002</td>
                    <td style="padding: 6px;">فاطمة حسن</td>
                    <td style="padding: 6px;">28</td>
                    <td style="padding: 6px;">أنثى</td>
                    <td style="padding: 6px;">بول</td>
                    <td style="padding: 6px;">مستشفى البصرة</td>
                    <td style="padding: 6px;">التهاب الكبد</td>
                    <td style="padding: 6px;">إيجابي</td>
                </tr>
                <tr>
                    <td style="padding: 6px;">5003</td>
                    <td style="padding: 6px;">علي أحمد</td>
                    <td style="padding: 6px;">42</td>
                    <td style="padding: 6px;">ذكر</td>
                    <td style="padding: 6px;">دم</td>
                    <td style="padding: 6px;">مستشفى الموصل</td>
                    <td style="padding: 6px;">كوفيد-19</td>
                    <td style="padding: 6px;">سلبي</td>
                </tr>
            </tbody>
        </table>
        
        <p><strong>إجمالي عدد السجلات في التقرير: 3</strong></p>
        
        <hr>
        
        <p>العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة</p>
        <p>مختبر الصحة العامة المركزي - وحدة الفايروسات والأحياء المجهرية</p>
        <p>صفحة 1</p>
    </div>
</body>
</html>
        """
        
        # CSS للتنسيق
        css_content = """
        @page {
            size: A4;
            margin: 2cm;
        }
        
        body {
            font-family: 'Arial', 'Tahoma', 'Times New Roman', sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            color: #333;
        }
        
        table {
            font-size: 12px;
        }
        
        th {
            font-weight: bold;
            background-color: #f5f5f5 !important;
        }
        """
        
        # إنشاء ملف PDF
        filename = f"اختبار_weasyprint_عربي_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        html_doc = HTML(string=html_content)
        css_doc = CSS(string=css_content)
        
        html_doc.write_pdf(filename, stylesheets=[css_doc])
        
        if os.path.exists(filename):
            print(f"✅ تم إنشاء ملف PDF عربي: {filename}")
            
            # فتح الملف
            try:
                os.startfile(filename)
                print("📂 تم فتح الملف للمراجعة")
            except:
                print("📂 يمكنك فتح الملف يدوياً")
            
            return True
        else:
            print("❌ فشل في إنشاء الملف")
            return False
            
    except ImportError as e:
        print(f"❌ WeasyPrint غير متوفر: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في WeasyPrint: {e}")
        return False

def test_pil_arabic():
    """اختبار PIL لإنشاء صور النصوص العربية"""
    print("\n🖼️ اختبار PIL لإنشاء صور النصوص العربية...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL: متوفر")
        
        # إنشاء صورة تجريبية
        img_width = 600
        img_height = 400
        img = Image.new('RGB', (img_width, img_height), color='white')
        draw = ImageDraw.Draw(img)
        
        # محاولة تحميل خط عربي
        font_loaded = False
        arabic_fonts = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/tahoma.ttf",
            "C:/Windows/Fonts/calibri.ttf"
        ]
        
        for font_path in arabic_fonts:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, 20)
                    font_loaded = True
                    print(f"✅ تم تحميل الخط: {font_path}")
                    break
                except:
                    continue
        
        if not font_loaded:
            font = ImageFont.load_default()
            print("⚠️ استخدام الخط الافتراضي")
        
        # النصوص العربية للاختبار
        arabic_texts = [
            "جمهورية العراق",
            "وزارة الصحة",
            "قسم الصحة العامة - محافظة ذي قار",
            "مختبر الصحة العامة المركزي",
            "وحدة الفايروسات والأحياء المجهرية",
            "تقرير نتائج الفحوصات المختبرية"
        ]
        
        y_pos = 50
        for text in arabic_texts:
            # حساب موضع النص
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            x = (img_width - text_width) // 2
            
            # رسم النص
            draw.text((x, y_pos), text, fill='black', font=font)
            y_pos += 40
        
        # حفظ الصورة
        filename = f"اختبار_صور_عربية_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        img.save(filename)
        
        if os.path.exists(filename):
            print(f"✅ تم إنشاء صورة النصوص العربية: {filename}")
            
            # فتح الصورة
            try:
                os.startfile(filename)
                print("📂 تم فتح الصورة للمراجعة")
            except:
                print("📂 يمكنك فتح الصورة يدوياً")
            
            return True
        else:
            print("❌ فشل في إنشاء الصورة")
            return False
            
    except ImportError as e:
        print(f"❌ PIL غير متوفر: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في PIL: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🇸🇦 اختبار الحلول البديلة للنصوص العربية")
    print("=" * 60)
    
    # اختبار WeasyPrint
    weasyprint_test = test_weasyprint_arabic()
    
    # اختبار PIL
    pil_test = test_pil_arabic()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"WeasyPrint (HTML/CSS): {'✅ نجح' if weasyprint_test else '❌ فشل'}")
    print(f"PIL (صور النصوص): {'✅ نجح' if pil_test else '❌ فشل'}")
    
    if weasyprint_test:
        print("\n🎉 WeasyPrint يعمل بشكل ممتاز!")
        print("✅ النصوص العربية تظهر بشكل صحيح")
        print("✅ التنسيق والجداول تعمل بشكل مثالي")
        print("✅ يمكن استخدام WeasyPrint كحل أساسي")
        print("\n🚀 البرنامج سيستخدم WeasyPrint لإنشاء التقارير العربية!")
    elif pil_test:
        print("\n🎨 PIL يعمل كحل بديل!")
        print("✅ يمكن إنشاء صور للنصوص العربية")
        print("✅ يمكن إدراج الصور في PDF")
        print("⚠️ الحل أبطأ لكنه فعال")
        print("\n🚀 البرنامج سيستخدم PIL كحل بديل!")
    else:
        print("\n❌ جميع الحلول البديلة فشلت")
        print("💡 يرجى تثبيت:")
        print("   py -m pip install weasyprint")
        print("   py -m pip install pillow")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
