# دليل تثبيت وتشغيل برنامج مختبر الصحة العامة المركزي - ذي قار
## Installation and Launch Guide - Central Public Health Laboratory Dhi Qar

### الإصدار 4.0 - دليل التثبيت والتشغيل | Version 4.0 - Installation & Launch Guide
**تاريخ التحديث:** 2025-07-12

---

## 🎯 طرق تثبيت البرنامج كأيقونة على سطح المكتب

### **الطريقة الأولى: استخدام منشئ الاختصارات (مستحسن)** ✅

#### **الخطوات:**
1. **شغل منشئ الاختصارات:**
   ```bash
   python create_desktop_shortcut.py
   ```

2. **اختر الإعدادات:**
   - **اسم الاختصار:** مختبر الصحة العامة المركزي - ذي قار
   - **طريقة التشغيل:** مشغل البرنامج (launcher.py) - مستحسن
   - **الوصف:** برنامج إدارة مختبر الصحة العامة المركزي

3. **اضغط "إنشاء اختصار سطح المكتب"**

4. **ستجد الأيقونة على سطح المكتب**

---

### **الطريقة الثانية: استخدام ملف Batch** ✅

#### **الخطوات:**
1. **انسخ ملف `تشغيل_مختبر_ذي_قار.bat` إلى سطح المكتب**

2. **انقر بالزر الأيمن على الملف → خصائص**

3. **في تبويب "اختصار":**
   - **الهدف:** تأكد من صحة المسار
   - **البدء في:** مجلد البرنامج
   - **تغيير الأيقونة:** اختر أيقونة مناسبة

4. **اضغط "موافق"**

---

### **الطريقة الثالثة: إنشاء اختصار يدوي** ✅

#### **الخطوات:**
1. **انقر بالزر الأيمن على سطح المكتب**
2. **اختر "جديد" → "اختصار"**
3. **في خانة الموقع، اكتب:**
   ```
   python "C:\Users\<USER>\Desktop\3\launcher.py"
   ```
   (غير المسار حسب موقع ملفات البرنامج)

4. **اضغط "التالي"**
5. **اكتب اسم الاختصار:** مختبر الصحة العامة المركزي - ذي قار
6. **اضغط "إنهاء"**

---

## 🚀 طرق تشغيل البرنامج

### **1. المشغل الذكي (launcher.py)** - الأفضل ✅

#### **المزايا:**
- **فحص المتطلبات** قبل التشغيل
- **واجهة جميلة** مع معلومات النظام
- **تثبيت المكتبات** المفقودة تلقائياً
- **رسائل خطأ واضحة**

#### **التشغيل:**
```bash
python launcher.py
```

### **2. ملف Batch (تشغيل_مختبر_ذي_قار.bat)** ✅

#### **المزايا:**
- **تشغيل مباشر** بدون كتابة أوامر
- **فحص Python** والملفات المطلوبة
- **رسائل عربية** واضحة
- **يعمل بنقرة واحدة**

#### **التشغيل:**
```bash
تشغيل_مختبر_ذي_قار.bat
```

### **3. ملف PowerShell (تشغيل_مختبر_ذي_قار.ps1)** ✅

#### **المزايا:**
- **واجهة ملونة** وجميلة
- **فحص شامل** للمتطلبات
- **رسائل مفصلة** عن حالة النظام

#### **التشغيل:**
```powershell
.\تشغيل_مختبر_ذي_قار.ps1
```

### **4. التشغيل المباشر** ✅

#### **للمطورين:**
```bash
python main.py
```

---

## 📦 تثبيت المكتبات المطلوبة

### **الطريقة الأولى: استخدام المشغل الذكي** ✅
1. **شغل `launcher.py`**
2. **اضغط "تثبيت المكتبات"**
3. **انتظر انتهاء التثبيت**

### **الطريقة الثانية: التثبيت اليدوي** ✅
```bash
pip install pandas openpyxl pillow reportlab pywin32 winshell
```

### **الطريقة الثالثة: ملف requirements.txt** ✅
```bash
pip install -r requirements.txt
```

---

## 🔧 حل المشاكل الشائعة

### **مشكلة: "Python غير متوفر"**
#### **الحل:**
1. **حمل Python من:** https://www.python.org/downloads/
2. **تأكد من تحديد "Add Python to PATH" أثناء التثبيت**
3. **أعد تشغيل الكمبيوتر**

### **مشكلة: "مكتبة مفقودة"**
#### **الحل:**
```bash
pip install اسم_المكتبة
```

### **مشكلة: "ملف غير موجود"**
#### **الحل:**
1. **تأكد من وجود جميع ملفات البرنامج في نفس المجلد**
2. **تأكد من صحة المسار في الاختصار**

### **مشكلة: "خطأ في الصلاحيات"**
#### **الحل:**
1. **شغل البرنامج كمدير (Run as Administrator)**
2. **تأكد من صلاحيات الكتابة في مجلد البرنامج**

---

## 📁 هيكل ملفات البرنامج

```
📁 مجلد البرنامج/
├── 📄 main.py                           # البرنامج الرئيسي
├── 📄 launcher.py                       # المشغل الذكي
├── 📄 create_desktop_shortcut.py        # منشئ الاختصارات
├── 📄 تشغيل_مختبر_ذي_قار.bat           # ملف Batch
├── 📄 تشغيل_مختبر_ذي_قار.ps1           # ملف PowerShell
├── 📄 lab_database.db                   # قاعدة البيانات
├── 📄 requirements.txt                  # قائمة المكتبات
├── 📄 دليل_التثبيت_والتشغيل.md          # هذا الملف
└── 📁 icons/                           # مجلد الأيقونات (اختياري)
    └── 📄 lab_icon.ico
```

---

## 🎨 تخصيص الأيقونة

### **إضافة أيقونة مخصصة:**
1. **ضع ملف الأيقونة (.ico) في مجلد البرنامج**
2. **عدل الاختصار:**
   - انقر بالزر الأيمن → خصائص
   - اضغط "تغيير الأيقونة"
   - اختر ملف الأيقونة

### **أيقونات مقترحة:**
- 🏥 أيقونة مستشفى
- 🧪 أيقونة مختبر
- 📊 أيقونة تقارير
- 🇮🇶 أيقونة العراق

---

## 🚀 التشغيل التلقائي مع Windows

### **إضافة البرنامج لبدء التشغيل:**
1. **اضغط `Win + R`**
2. **اكتب `shell:startup`**
3. **انسخ اختصار البرنامج إلى هذا المجلد**

### **أو استخدم Task Scheduler:**
1. **افتح Task Scheduler**
2. **أنشئ مهمة جديدة**
3. **اختر "تشغيل عند بدء التشغيل"**
4. **حدد ملف البرنامج**

---

## 📱 استخدام البرنامج

### **بعد إنشاء الأيقونة:**
1. **انقر نقرة مزدوجة على الأيقونة**
2. **سيفتح المشغل الذكي (إذا اخترت launcher.py)**
3. **اضغط "تشغيل البرنامج"**
4. **استمتع بالبرنامج!**

### **الوصول السريع:**
- **سطح المكتب:** نقرة مزدوجة على الأيقونة
- **قائمة ابدأ:** ابحث عن "مختبر الصحة العامة"
- **شريط المهام:** اسحب الأيقونة إلى شريط المهام

---

## 🏆 التوصيات

### **للاستخدام اليومي:** ✅
- **استخدم المشغل الذكي (launcher.py)**
- **أنشئ اختصار على سطح المكتب**
- **أضف اختصار في قائمة ابدأ**

### **للمطورين:** ✅
- **استخدم التشغيل المباشر (main.py)**
- **احتفظ بنسخة احتياطية من قاعدة البيانات**
- **راجع ملفات السجل للأخطاء**

### **للمدراء:** ✅
- **وزع ملف Batch على جميع الأجهزة**
- **تأكد من تثبيت Python على جميع الأجهزة**
- **أنشئ دليل استخدام للموظفين**

---

## 🎉 النتيجة النهائية

### **بعد اتباع هذا الدليل ستحصل على:**
- ✅ **أيقونة جميلة على سطح المكتب**
- ✅ **تشغيل سهل بنقرة واحدة**
- ✅ **فحص تلقائي للمتطلبات**
- ✅ **رسائل خطأ واضحة**
- ✅ **واجهة احترافية**

**🎊 مبروك! أصبح برنامج مختبر الصحة العامة المركزي جاهز للاستخدام بسهولة تامة!**

---

**© 2025 مختبر الصحة العامة المركزي - ذي قار | دليل التثبيت والتشغيل**
