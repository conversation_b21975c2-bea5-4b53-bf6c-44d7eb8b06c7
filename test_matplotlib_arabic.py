#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار matplotlib للنصوص العربية
Matplotlib Arabic Text Test
"""

import os
import datetime

def test_matplotlib_arabic():
    """اختبار matplotlib مع النصوص العربية"""
    print("🔍 اختبار matplotlib للنصوص العربية...")
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        from matplotlib.backends.backend_pdf import PdfPages
        import matplotlib.font_manager as fm
        print("✅ matplotlib: متوفر")
        
        # إعداد matplotlib للنصوص العربية
        plt.rcParams['font.family'] = ['Arial', 'Tahoma', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # إنشاء ملف PDF
        filename = f"اختبار_matplotlib_عربي_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        with PdfPages(filename) as pdf:
            # إنشاء صفحة جديدة
            fig, ax = plt.subplots(figsize=(8.27, 11.69))  # A4 size
            ax.set_xlim(0, 10)
            ax.set_ylim(0, 14)
            ax.axis('off')
            
            # العناوين الرسمية
            y_pos = 12
            titles = [
                ("جمهورية العراق", 16, 'bold'),
                ("وزارة الصحة", 14, 'bold'),
                ("قسم الصحة العامة - محافظة ذي قار", 12, 'bold'),
                ("مختبر الصحة العامة المركزي", 12, 'bold'),
                ("وحدة الفايروسات والأحياء المجهرية", 10, 'bold')
            ]
            
            for title, size, weight in titles:
                ax.text(5, y_pos, title, ha='center', va='center', 
                       fontsize=size, fontweight=weight, 
                       fontfamily=['Arial', 'Tahoma'])
                y_pos -= 0.4
                print(f"✅ تم رسم: {title}")
            
            # عنوان التقرير
            y_pos -= 0.5
            ax.text(5, y_pos, "تقرير نتائج الفحوصات المختبرية", 
                   ha='center', va='center', fontsize=18, fontweight='bold',
                   fontfamily=['Arial', 'Tahoma'])
            print("✅ تم رسم عنوان التقرير")
            
            y_pos -= 0.3
            ax.text(5, y_pos, "Laboratory Test Results Report", 
                   ha='center', va='center', fontsize=14, fontweight='bold')
            
            # التاريخ والوقت
            current_date = datetime.datetime.now()
            arabic_date = current_date.strftime("%Y/%m/%d")
            arabic_time = current_date.strftime("%H:%M:%S")
            
            y_pos -= 0.8
            ax.text(5, y_pos, f"تاريخ إنشاء التقرير: {arabic_date}", 
                   ha='center', va='center', fontsize=12,
                   fontfamily=['Arial', 'Tahoma'])
            y_pos -= 0.3
            ax.text(5, y_pos, f"وقت الإنشاء: {arabic_time}", 
                   ha='center', va='center', fontsize=12,
                   fontfamily=['Arial', 'Tahoma'])
            print("✅ تم رسم التاريخ والوقت")
            
            # رسم جدول تجريبي
            y_pos -= 1
            headers = ["الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحليل", "النتيجة"]
            col_positions = [0.5, 1.5, 2.5, 3.5, 4.5, 6, 7.5, 9]
            col_widths = [0.8, 0.8, 0.6, 0.6, 0.8, 1.2, 1.2, 1]
            
            # رسم رؤوس الجدول
            for i, header in enumerate(headers):
                if i < len(col_positions):
                    # رسم خلفية الرأس
                    rect = patches.Rectangle((col_positions[i] - col_widths[i]/2, y_pos - 0.15), 
                                           col_widths[i], 0.3, 
                                           linewidth=1, edgecolor='black', facecolor='lightgray')
                    ax.add_patch(rect)
                    
                    # رسم النص
                    ax.text(col_positions[i], y_pos, header, 
                           ha='center', va='center', fontsize=8, fontweight='bold',
                           fontfamily=['Arial', 'Tahoma'])
            print("✅ تم رسم رؤوس الجدول")
            
            # بيانات تجريبية
            test_data = [
                ["5001", "أحمد محمد علي", "35", "ذكر", "دم", "مستشفى بغداد", "كوفيد-19", "سلبي"],
                ["5002", "فاطمة حسن", "28", "أنثى", "بول", "مستشفى البصرة", "التهاب الكبد", "إيجابي"],
                ["5003", "علي أحمد", "42", "ذكر", "دم", "مستشفى الموصل", "كوفيد-19", "سلبي"]
            ]
            
            # رسم البيانات
            y_pos -= 0.4
            for row_data in test_data:
                for i, value in enumerate(row_data):
                    if i < len(col_positions):
                        # رسم خلفية الخلية
                        rect = patches.Rectangle((col_positions[i] - col_widths[i]/2, y_pos - 0.1), 
                                               col_widths[i], 0.2, 
                                               linewidth=0.5, edgecolor='gray', facecolor='white')
                        ax.add_patch(rect)
                        
                        # رسم النص
                        ax.text(col_positions[i], y_pos, str(value), 
                               ha='center', va='center', fontsize=7,
                               fontfamily=['Arial', 'Tahoma'])
                
                y_pos -= 0.25
            print("✅ تم رسم البيانات")
            
            # إحصائيات التقرير
            y_pos = 2.5
            ax.text(5, y_pos, f"إجمالي عدد السجلات في التقرير: {len(test_data)}", 
                   ha='center', va='center', fontsize=14, fontweight='bold',
                   fontfamily=['Arial', 'Tahoma'])
            
            # التذييل
            y_pos = 1.5
            ax.text(5, y_pos, "العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة", 
                   ha='center', va='center', fontsize=10,
                   fontfamily=['Arial', 'Tahoma'])
            y_pos -= 0.3
            ax.text(5, y_pos, "مختبر الصحة العامة المركزي - وحدة الفايروسات والأحياء المجهرية", 
                   ha='center', va='center', fontsize=9,
                   fontfamily=['Arial', 'Tahoma'])
            y_pos -= 0.3
            ax.text(5, y_pos, "صفحة 1", 
                   ha='center', va='center', fontsize=8,
                   fontfamily=['Arial', 'Tahoma'])
            print("✅ تم رسم التذييل")
            
            # حفظ الصفحة
            pdf.savefig(fig, bbox_inches='tight')
            plt.close(fig)
        
        if os.path.exists(filename):
            print(f"✅ تم إنشاء ملف PDF عربي: {filename}")
            
            # فتح الملف
            try:
                os.startfile(filename)
                print("📂 تم فتح الملف للمراجعة")
            except:
                print("📂 يمكنك فتح الملف يدوياً")
            
            return True
        else:
            print("❌ فشل في إنشاء الملف")
            return False
            
    except ImportError as e:
        print(f"❌ matplotlib غير متوفر: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في matplotlib: {e}")
        return False

def test_all_solutions():
    """اختبار جميع الحلول المتاحة"""
    print("🇸🇦 اختبار جميع الحلول للنصوص العربية")
    print("=" * 60)
    
    results = {}
    
    # اختبار matplotlib
    print("1️⃣ اختبار matplotlib...")
    results['matplotlib'] = test_matplotlib_arabic()
    
    # اختبار PIL
    print("\n2️⃣ اختبار PIL...")
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL: متوفر")
        results['PIL'] = True
    except ImportError:
        print("❌ PIL: غير متوفر")
        results['PIL'] = False
    
    # اختبار WeasyPrint
    print("\n3️⃣ اختبار WeasyPrint...")
    try:
        import weasyprint
        print("✅ WeasyPrint: متوفر")
        results['WeasyPrint'] = True
    except ImportError:
        print("❌ WeasyPrint: غير متوفر")
        results['WeasyPrint'] = False
    except Exception as e:
        print(f"❌ WeasyPrint: خطأ - {e}")
        results['WeasyPrint'] = False
    
    # اختبار reportlab
    print("\n4️⃣ اختبار reportlab...")
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        print("✅ reportlab: متوفر")
        results['reportlab'] = True
    except ImportError:
        print("❌ reportlab: غير متوفر")
        results['reportlab'] = False
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار الشامل:")
    for solution, status in results.items():
        print(f"{solution}: {'✅ يعمل' if status else '❌ لا يعمل'}")
    
    # التوصية
    print("\n🎯 التوصية:")
    if results.get('matplotlib'):
        print("🥇 matplotlib: الحل الأفضل - يدعم النصوص العربية بشكل ممتاز")
        print("   ✅ سهولة الاستخدام")
        print("   ✅ دعم كامل للنصوص العربية")
        print("   ✅ تنسيق احترافي")
    elif results.get('PIL'):
        print("🥈 PIL: حل جيد - إنشاء صور للنصوص العربية")
        print("   ✅ يعمل مع جميع الخطوط")
        print("   ⚠️ أبطأ قليلاً")
    elif results.get('reportlab'):
        print("🥉 reportlab: حل أساسي - مع تحسينات للنصوص العربية")
        print("   ⚠️ يحتاج معالجة إضافية للنصوص العربية")
    else:
        print("❌ لا توجد حلول متاحة")
        print("💡 يرجى تثبيت إحدى المكتبات:")
        print("   py -m pip install matplotlib")
        print("   py -m pip install pillow")
        print("   py -m pip install reportlab")

def main():
    """الدالة الرئيسية"""
    test_all_solutions()

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
